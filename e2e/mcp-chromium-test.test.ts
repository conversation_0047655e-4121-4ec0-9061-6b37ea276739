import { expect, test } from '@playwright/test';
import { MCPHelpers } from './utils/mcp-helpers';

test.describe('MCP Chromium Setup Verification', () => {
	test('should load page in Chromium browser', async ({ page, browserName }) => {
		const mcp = new MCPHelpers(page);

		// Verify we're using Chromium
		expect(browserName).toBe('chromium');

		// Navigate to the home page
		await page.goto('/');
		await mcp.waitForPageReady();

		// Verify the page loads correctly
		await expect(page.locator('h1')).toBeVisible();

		// Take a screenshot to verify visual rendering
		await mcp.takeTimestampedScreenshot('chromium-homepage');

		// Get comprehensive page context
		const context = await mcp.getPageContext();
		expect(context.url).toContain('localhost:5173');
		expect(context.title).toBeTruthy();
		expect(context.userAgent).toContain('Chrome');

		// Test JavaScript execution
		const result = await page.evaluate(() => {
			return {
				windowWidth: window.innerWidth,
				windowHeight: window.innerHeight,
				hasLocalStorage: typeof localStorage !== 'undefined',
				hasSessionStorage: typeof sessionStorage !== 'undefined',
			};
		});

		expect(result.windowWidth).toBeGreaterThan(0);
		expect(result.windowHeight).toBeGreaterThan(0);
		expect(result.hasLocalStorage).toBe(true);
		expect(result.hasSessionStorage).toBe(true);
	});

	test('should handle MCP-style interactions', async ({ page }) => {
		const mcp = new MCPHelpers(page);

		await page.goto('/');
		await mcp.waitForPageReady();

		// Test smart element finding
		const interactiveElements = await mcp.getInteractiveElements();
		console.log(`Found ${interactiveElements.length} interactive elements`);

		// Test form interactions if available
		const inputs = page.locator('input');
		const inputCount = await inputs.count();

		if (inputCount > 0) {
			// Try to find a specific input type first
			const emailInput = page.locator('input[type="email"]');
			if ((await emailInput.count()) > 0) {
				const success = await mcp.smartType('input[type="email"]', '<EMAIL>');
				expect(success).toBe(true);
			} else {
				// Fallback to first input
				const success = await mcp.smartType('input', 'test-value');
				expect(success).toBe(true);
			}
		}

		// Test navigation elements
		const links = page.locator('a');
		const linkCount = await links.count();
		console.log(`Found ${linkCount} links on the page`);
	});

	test('should support modern web features for MCP', async ({ page }) => {
		const mcp = new MCPHelpers(page);

		await page.goto('/');
		await mcp.waitForPageReady();

		// Test modern JavaScript features
		const modernFeatures = await page.evaluate(() => {
			return {
				hasPromise: typeof Promise !== 'undefined',
				hasAsyncAwait: (async () => true)().constructor.name === 'Promise',
				hasFetch: typeof fetch !== 'undefined',
				hasWebGL: !!document.createElement('canvas').getContext('webgl'),
				hasWebWorkers: typeof Worker !== 'undefined',
				hasIntersectionObserver: typeof IntersectionObserver !== 'undefined',
				hasResizeObserver: typeof ResizeObserver !== 'undefined',
			};
		});

		expect(modernFeatures.hasPromise).toBe(true);
		expect(modernFeatures.hasAsyncAwait).toBe(true);
		expect(modernFeatures.hasFetch).toBe(true);
		expect(modernFeatures.hasWebGL).toBe(true);
		expect(modernFeatures.hasWebWorkers).toBe(true);
		expect(modernFeatures.hasIntersectionObserver).toBe(true);
		expect(modernFeatures.hasResizeObserver).toBe(true);

		// Take final screenshot
		await mcp.takeTimestampedScreenshot('modern-features-test');
	});
});
