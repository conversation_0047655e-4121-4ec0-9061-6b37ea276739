import { expect, test } from '@playwright/test';

test.describe('Budget Version Manager - Version History dialog', () => {
	test('signs in, generates demo project, and opens dialog + dropdown', async ({ page }) => {
		// Sign in with seeded local user
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', '<EMAIL>');
		await page.fill('input[name="password"]', 'testtest');
		await page.click('button:has-text("Sign In")');

		// Navigate to admin tools and generate demo project data
		await page.goto('/admin-tools');
		await expect(page.locator('h1:has-text("Admin Tools")')).toBeVisible();

		// Fill total budget and confirm
		const budgetInput = page.locator('input[name="totalBudget"]');
		if (await budgetInput.count()) {
			await budgetInput.fill('1000000000');
		}
		await page.locator('#riba-confirm-checkbox').click();
		await page.click('button:has-text("Generate RIBA Demo Project Data")');

		// Click the toast action to go to the generated project
		const goToProject = page.getByRole('button', { name: 'Go to Project' });
		await goToProject.waitFor({ state: 'visible', timeout: 120000 });
		await goToProject.click();

		// Landed on project overview; navigate to budget page
		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+\/overview/);
		const budgetUrl = page.url().replace('/overview', '/budget');
		await page.goto(budgetUrl);

		// Open Version History dialog
		await page.getByRole('button', { name: 'Version History' }).click();
		await expect(page.getByText('Budget Version History')).toBeVisible();

		// If versions exist, open the row actions dropdown and then close it
		const rows = page.locator('table tbody tr');
		if ((await rows.count()) > 0) {
			const menuButton = rows.first().locator('td').last().locator('button').first();
			if ((await menuButton.count()) > 0) {
				await menuButton.click();
				// Expect any of the menu items to appear
				const anyMenuItem = page.locator(
					'text=Compare to Active, text=Activate Version, text=Undo Import',
				);
				await anyMenuItem
					.first()
					.waitFor({ state: 'visible', timeout: 5000 })
					.catch(() => {});
				// Close the dropdown (Escape)
				await page.keyboard.press('Escape');
			}
		}

		// Close dialog via Escape to avoid ambiguous "Close" buttons
		await page.keyboard.press('Escape');
		await expect(page.getByText('Budget Version History')).toHaveCount(0);
	});
});
