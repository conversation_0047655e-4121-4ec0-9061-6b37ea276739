import { chromium, FullConfig, Page, Route } from '@playwright/test';

/**
 * Global setup for Playwright e2e tests
 * This runs once before all tests and sets up global configurations
 */
async function globalSetup(config: FullConfig) {
	console.log('Starting global e2e test setup...');

	// Launch a browser to verify the setup works
	const browser = await chromium.launch();
	const page = await browser.newPage();

	try {
		// Test that the dev server is running
		const baseURL = config.projects[0]?.use?.baseURL || 'http://localhost:5173';
		console.log(`📡 Testing connection to ${baseURL}...`);

		await page.goto(baseURL, { timeout: 10000 });
		console.log('✅ Dev server is accessible');

		// Verify that the page loads basic content
		await page.waitForLoadState('domcontentloaded');
		const title = await page.title();
		console.log(`📄 Page title: ${title}`);
	} catch (error) {
		console.error('❌ Global setup failed:', error);
		throw new Error(
			'Global setup failed. Make sure the dev server is running with "npm run dev" or "pnpm dev"',
		);
	} finally {
		await browser.close();
	}

	console.log('✅ Global e2e test setup completed successfully');
}

export default globalSetup;

// Email mock utilities for e2e tests
export interface MockEmailCall {
	to: string;
	subject: string;
	text?: string;
	html?: string;
	timestamp: number;
}

class EmailMockService {
	private emailCalls: MockEmailCall[] = [];
	private shouldFailEmails = false;

	setupEmailMocking(page: Page) {
		// Mock the /api/invites endpoint which sends emails
		return page.route('/api/invites', async (route: Route) => {
			const request = route.request();

			if (request.method() === 'POST') {
				try {
					const body = await request.postDataJSON();

					// Capture email details from the invite request
					const emailCall: MockEmailCall = {
						to: body.inviteeEmail,
						subject: `You have been invited to join ${this.getJoinWhat(body)} on Cost Atlas`,
						html: `<p>You have been invited to join ${this.getJoinWhat(body)}. Click <a href="http://localhost:5173/auth/invite/mock-token">here</a> to accept or decline the invitation.</p>`,
						timestamp: Date.now(),
					};

					this.emailCalls.push(emailCall);

					// Return success or failure based on mock configuration
					if (this.shouldFailEmails) {
						await route.fulfill({
							status: 500,
							contentType: 'application/json',
							body: JSON.stringify({ error: 'Mock email service failure' }),
						});
					} else {
						await route.fulfill({
							status: 200,
							contentType: 'application/json',
							body: JSON.stringify({ success: true }),
						});
					}
				} catch (error) {
					console.error('Error in email mock:', error);
					await route.fulfill({
						status: 500,
						contentType: 'application/json',
						body: JSON.stringify({ error: 'Mock processing error' }),
					});
				}
			} else {
				await route.continue();
			}
		});
	}

	private getJoinWhat(body: { resourceType?: string }): string {
		switch (body.resourceType) {
			case 'organization':
				return 'the organization';
			case 'client':
				return 'the client';
			case 'project':
				return 'the project';
			default:
				return 'the resource';
		}
	}

	setEmailFailure(shouldFail: boolean): void {
		this.shouldFailEmails = shouldFail;
	}

	getEmailCalls(): MockEmailCall[] {
		return [...this.emailCalls];
	}

	getLastEmailCall(): MockEmailCall | null {
		return this.emailCalls.length > 0 ? this.emailCalls[this.emailCalls.length - 1] : null;
	}

	getEmailCallsTo(email: string): MockEmailCall[] {
		return this.emailCalls.filter((call) => call.to === email);
	}

	getEmailCallsBySubject(subjectPattern: string | RegExp): MockEmailCall[] {
		const pattern =
			typeof subjectPattern === 'string' ? new RegExp(subjectPattern, 'i') : subjectPattern;
		return this.emailCalls.filter((call) => pattern.test(call.subject));
	}

	clearEmailCalls(): void {
		this.emailCalls = [];
	}

	getEmailCallCount(): number {
		return this.emailCalls.length;
	}

	wasEmailSentTo(email: string): boolean {
		return this.emailCalls.some((call) => call.to === email);
	}

	wasEmailSentWithSubject(subjectPattern: string | RegExp): boolean {
		const pattern =
			typeof subjectPattern === 'string' ? new RegExp(subjectPattern, 'i') : subjectPattern;
		return this.emailCalls.some((call) => pattern.test(call.subject));
	}

	async waitForEmail(
		predicate: (call: MockEmailCall) => boolean,
		timeout: number = 5000,
	): Promise<MockEmailCall | null> {
		const startTime = Date.now();

		while (Date.now() - startTime < timeout) {
			const matchingCall = this.emailCalls.find(predicate);
			if (matchingCall) {
				return matchingCall;
			}
			await new Promise((resolve) => setTimeout(resolve, 100));
		}

		return null;
	}

	assertEmailSent(criteria: {
		to?: string;
		subject?: string | RegExp;
		containsText?: string;
		containsHtml?: string;
	}): MockEmailCall {
		const matchingCalls = this.emailCalls.filter((call) => {
			if (criteria.to && call.to !== criteria.to) return false;

			if (criteria.subject) {
				const pattern =
					typeof criteria.subject === 'string'
						? new RegExp(criteria.subject, 'i')
						: criteria.subject;
				if (!pattern.test(call.subject)) return false;
			}

			if (criteria.containsText && call.text && !call.text.includes(criteria.containsText)) {
				return false;
			}

			if (criteria.containsHtml && call.html && !call.html.includes(criteria.containsHtml)) {
				return false;
			}

			return true;
		});

		if (matchingCalls.length === 0) {
			const criteriaStr = JSON.stringify(criteria, null, 2);
			const callsStr = JSON.stringify(this.emailCalls, null, 2);
			throw new Error(
				`No email found matching criteria:\n${criteriaStr}\n\nActual emails sent:\n${callsStr}`,
			);
		}

		return matchingCalls[0];
	}
}

// Global email mock instance
export const emailMock = new EmailMockService();
