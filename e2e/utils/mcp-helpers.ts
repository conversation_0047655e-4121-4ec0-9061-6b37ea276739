import { Page, Locator } from '@playwright/test';

/**
 * MCP Helper utilities for Playwright automation
 * These utilities are designed to work well with AI-driven browser automation
 */

export class MCPHelpers {
	constructor(private page: Page) {}

	/**
	 * Wait for page to be fully loaded and interactive
	 */
	async waitForPageReady(): Promise<void> {
		try {
			// Try networkidle first, but with a shorter timeout
			await this.page.waitForLoadState('networkidle', { timeout: 10000 });
		} catch {
			// If networkidle fails (e.g., due to hot reloading), fall back to domcontentloaded
			await this.page.waitForLoadState('domcontentloaded');
		}
		await this.page.waitForFunction(() => document.readyState === 'complete');
	}

	/**
	 * Take a screenshot with timestamp for debugging
	 */
	async takeTimestampedScreenshot(name: string): Promise<void> {
		const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
		await this.page.screenshot({
			path: `test-results-mcp/screenshots/${name}-${timestamp}.png`,
			fullPage: true,
		});
	}

	/**
	 * Get detailed page information for MCP context
	 */
	async getPageContext(): Promise<{
		url: string;
		title: string;
		viewport: { width: number; height: number };
		userAgent: string;
		cookies: Array<Record<string, unknown>>;
		localStorage: Record<string, string>;
	}> {
		const url = this.page.url();
		const title = await this.page.title();
		const viewport = this.page.viewportSize() || { width: 0, height: 0 };
		const userAgent = await this.page.evaluate(() => navigator.userAgent);
		const cookies = await this.page.context().cookies();

		const localStorage = await this.page.evaluate(() => {
			const storage: Record<string, string> = {};
			for (let i = 0; i < localStorage.length; i++) {
				const key = localStorage.key(i);
				if (key) {
					storage[key] = localStorage.getItem(key) || '';
				}
			}
			return storage;
		});

		return { url, title, viewport, userAgent, cookies, localStorage };
	}

	/**
	 * Smart element finder that tries multiple strategies
	 */
	async findElement(selector: string): Promise<Locator | null> {
		// Try exact selector first - if multiple elements, return the first one
		let element = this.page.locator(selector);
		if ((await element.count()) > 0) {
			return element.first();
		}

		// Try as text content
		element = this.page.getByText(selector);
		if ((await element.count()) > 0) {
			return element.first();
		}

		// Try as placeholder
		element = this.page.getByPlaceholder(selector);
		if ((await element.count()) > 0) {
			return element.first();
		}

		// Try as label
		element = this.page.getByLabel(selector);
		if ((await element.count()) > 0) {
			return element.first();
		}

		// Try as role
		try {
			element = this.page.getByRole('button', { name: selector });
			if ((await element.count()) > 0) {
				return element.first();
			}
		} catch {
			// Ignore role errors
		}

		return null;
	}

	/**
	 * Smart click that waits for element and handles different scenarios
	 */
	async smartClick(selector: string): Promise<boolean> {
		const element = await this.findElement(selector);
		if (!element) {
			return false;
		}

		try {
			await element.waitFor({ state: 'visible' });
			await element.click();
			return true;
		} catch (error) {
			console.warn(`Failed to click element: ${selector}`, error);
			return false;
		}
	}

	/**
	 * Smart type that handles different input types
	 */
	async smartType(selector: string, text: string): Promise<boolean> {
		const element = await this.findElement(selector);
		if (!element) {
			return false;
		}

		try {
			await element.waitFor({ state: 'visible' });
			await element.clear();
			await element.fill(text);
			return true;
		} catch (error) {
			console.warn(`Failed to type in element: ${selector}`, error);
			return false;
		}
	}

	/**
	 * Get all interactive elements on the page
	 */
	async getInteractiveElements(): Promise<
		Array<{
			tagName: string;
			text: string;
			selector: string;
			type?: string;
			href?: string;
		}>
	> {
		return await this.page.evaluate(() => {
			const elements = document.querySelectorAll(
				'button, input, select, textarea, a[href], [onclick], [role="button"]',
			);

			return Array.from(elements)
				.map((el, index) => {
					const rect = el.getBoundingClientRect();
					const isVisible =
						rect.width > 0 &&
						rect.height > 0 &&
						window.getComputedStyle(el).visibility !== 'hidden';

					if (!isVisible) return null;

					return {
						tagName: el.tagName.toLowerCase(),
						text: el.textContent?.trim() || '',
						selector: `${el.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
						type: (el as HTMLInputElement).type || undefined,
						href: (el as HTMLAnchorElement).href || undefined,
					};
				})
				.filter(Boolean);
		});
	}

	/**
	 * Wait for navigation with better error handling
	 */
	async waitForNavigation(timeout: number = 30000): Promise<void> {
		try {
			await this.page.waitForLoadState('networkidle', { timeout });
		} catch (error) {
			console.warn('Navigation timeout, continuing...', error);
		}
	}

	/**
	 * Scroll element into view and ensure it's visible
	 */
	async scrollToElement(selector: string): Promise<boolean> {
		const element = await this.findElement(selector);
		if (!element) {
			return false;
		}

		try {
			await element.scrollIntoViewIfNeeded();
			await element.waitFor({ state: 'visible' });
			return true;
		} catch (error) {
			console.warn(`Failed to scroll to element: ${selector}`, error);
			return false;
		}
	}
}
