import { expect, test } from '@playwright/test';

// Test fixtures for authentication flows
test.describe('Authentication E2E Tests', () => {
	// Generate a unique email for testing to avoid conflicts
	const testEmail = `test-${Date.now()}@example.com`;
	const password = 'TestPassword123';

	test('complete user registration journey', async ({ page }) => {
		// Navigate to signup page
		await page.goto('/auth/signup');

		// Verify the page title
		await expect(page.locator('h1:has-text("Sign up")')).toBeVisible();

		// Fill the signup form
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', password);

		// Submit the form
		await page.click('button[type="submit"]');

		// Should be redirected to signin page after successful signup
		await page.waitForURL(/\/auth\/signin/, { timeout: 10000 });
	});

	test('failed login with incorrect credentials', async ({ page }) => {
		// Navigate to signin page
		await page.goto('/auth/signin');

		// Verify the page title
		await expect(page.locator('h1:has-text("Sign in")')).toBeVisible();

		// Fill the login form with wrong password
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', 'WrongPassword123');

		// Submit the form
		await page.click('button[type="submit"]');

		// Should show an error message
		await expect(page.locator('text=Invalid login credentials')).toBeVisible({ timeout: 10000 });
	});

	test('successful login with valid credentials', async ({ page }) => {
		// This test verifies that users can sign in successfully after signup
		// In the current setup, email verification may not be strictly enforced

		// Navigate to signin page
		await page.goto('/auth/signin');

		// Fill the login form with correct credentials
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', password);

		// Submit the form
		await page.click('button[type="submit"]');

		// Should be redirected to org creation page since user has no orgs
		await expect(page).toHaveURL(/\/org\/new/, { timeout: 10000 });
	});

	// Password reset flow test - note this requires email integration
	// and can be challenging to fully automate
	test('initiate password reset flow', async ({ page }) => {
		// Navigate to password reset page
		await page.goto('/auth/reset-password');

		// Verify the page title - should be "Reset Password" (exact match)
		await expect(page.locator('h1')).toContainText('Reset Password');

		// Fill in the email
		await page.fill('input[name="email"]', testEmail);

		// Submit the form
		await page.click('button[type="submit"]');

		// Wait a moment for the form to process
		await page.waitForTimeout(2000);

		// Verify that we're still on the reset password page (form was submitted successfully)
		// and no validation errors are shown
		await expect(page.locator('h1')).toContainText('Reset Password');

		// Check that no form errors are visible (which would indicate the form was processed)
		const formErrors = page.locator('[data-fs-field-errors]:visible');
		await expect(formErrors).toHaveCount(0);
	});
});
