import { expect, test } from '@playwright/test';
import { MCPHelpers } from './utils/mcp-helpers';

test.describe('MCP Chromium Setup Verification', () => {
	test('should load page in Chromium browser with MCP config', async ({ page, browserName }) => {
		const mcp = new MCPHelpers(page);

		// Verify we're using Chromium
		expect(browserName).toBe('chromium');

		// Navigate to the home page
		await page.goto('/');
		await mcp.waitForPageReady();

		// Verify the page loads correctly
		await expect(page.locator('h1')).toBeVisible();

		// Take a screenshot to verify visual rendering
		await mcp.takeTimestampedScreenshot('mcp-chromium-homepage');

		// Get comprehensive page context
		const context = await mcp.getPageContext();
		expect(context.url).toContain('localhost:5173');
		expect(context.title).toBeTruthy();
		expect(context.userAgent).toContain('Chrome');

		console.log('MCP Page Context:', {
			url: context.url,
			title: context.title,
			viewport: context.viewport,
		});
	});

	test('should demonstrate MCP automation capabilities', async ({ page }) => {
		const mcp = new MCPHelpers(page);

		await page.goto('/');
		await mcp.waitForPageReady();

		// Discover all interactive elements
		const interactiveElements = await mcp.getInteractiveElements();
		console.log(`MCP discovered ${interactiveElements.length} interactive elements:`);
		interactiveElements.forEach((el, index) => {
			console.log(`  ${index + 1}. ${el.tagName} - "${el.text}" (${el.selector})`);
		});

		// Test smart element finding and interaction
		if (interactiveElements.length > 0) {
			const firstButton = interactiveElements.find((el) => el.tagName === 'button');
			if (firstButton) {
				console.log(`Attempting to click button: "${firstButton.text}"`);
				const clickSuccess = await mcp.smartClick(firstButton.text);
				console.log(`Click result: ${clickSuccess}`);
			}
		}

		// Test form interactions if available
		const emailInput = page.locator('input[type="email"]');
		if ((await emailInput.count()) > 0) {
			console.log('Testing email input interaction');
			const typeSuccess = await mcp.smartType('input[type="email"]', '<EMAIL>');
			expect(typeSuccess).toBe(true);

			// Verify the value was set
			const inputValue = await emailInput.inputValue();
			expect(inputValue).toBe('<EMAIL>');
		}

		// Take final screenshot
		await mcp.takeTimestampedScreenshot('mcp-automation-complete');
	});

	test('should verify MCP-optimized browser features', async ({ page }) => {
		const mcp = new MCPHelpers(page);

		await page.goto('/');
		await mcp.waitForPageReady();

		// Test modern JavaScript features that MCP might use
		const features = await page.evaluate(() => {
			return {
				// Core features
				hasPromise: typeof Promise !== 'undefined',
				hasFetch: typeof fetch !== 'undefined',
				hasAsyncAwait: (async () => true)().constructor.name === 'Promise',

				// DOM features
				hasQuerySelector: typeof document.querySelector !== 'undefined',
				hasGetElementById: typeof document.getElementById !== 'undefined',
				hasAddEventListener: typeof document.addEventListener !== 'undefined',

				// Modern APIs
				hasIntersectionObserver: typeof IntersectionObserver !== 'undefined',
				hasResizeObserver: typeof ResizeObserver !== 'undefined',
				hasMutationObserver: typeof MutationObserver !== 'undefined',

				// Storage
				hasLocalStorage: typeof localStorage !== 'undefined',
				hasSessionStorage: typeof sessionStorage !== 'undefined',

				// Graphics
				hasCanvas: !!document.createElement('canvas').getContext,
				hasWebGL: !!document.createElement('canvas').getContext('webgl'),

				// Network
				hasWebSocket: typeof WebSocket !== 'undefined',
				hasEventSource: typeof EventSource !== 'undefined',

				// Performance
				hasPerformance: typeof performance !== 'undefined',
				hasRequestAnimationFrame: typeof requestAnimationFrame !== 'undefined',
			};
		});

		// Verify all essential features are available
		expect(features.hasPromise).toBe(true);
		expect(features.hasFetch).toBe(true);
		expect(features.hasAsyncAwait).toBe(true);
		expect(features.hasQuerySelector).toBe(true);
		expect(features.hasLocalStorage).toBe(true);
		expect(features.hasCanvas).toBe(true);

		console.log('MCP Browser Features Check:', features);

		// Test page interaction capabilities
		const pageInfo = await page.evaluate(() => {
			return {
				readyState: document.readyState,
				visibilityState: document.visibilityState,
				hasFocus: document.hasFocus(),
				activeElement: document.activeElement?.tagName || 'none',
				scrollPosition: { x: window.scrollX, y: window.scrollY },
				viewport: { width: window.innerWidth, height: window.innerHeight },
			};
		});

		expect(pageInfo.readyState).toBe('complete');
		expect(pageInfo.visibilityState).toBe('visible');

		console.log('MCP Page State:', pageInfo);

		// Final verification screenshot
		await mcp.takeTimestampedScreenshot('mcp-features-verified');
	});
});
