# Playwright MCP (Model Context Protocol) Setup

This document describes the Playwright setup optimized for MCP (Model Context Protocol) usage in your project.

## Overview

The project is now configured with <PERSON><PERSON> using Chromium browser, specifically optimized for AI-driven browser automation through MCP. This setup provides reliable, consistent browser automation capabilities for AI agents.

## Configuration Files

### 1. `playwright.config.ts`

The main Playwright configuration file with basic Chromium setup:

- Uses Chromium browser with Desktop Chrome device settings
- Configured for development server on `localhost:5173`
- Includes screenshot and video capture on failures
- Optimized timeouts for reliable automation

### 2. `playwright-mcp.config.ts`

Enhanced configuration specifically for MCP usage:

- Additional Chrome launch arguments for better automation compatibility
- Extended timeouts for AI-driven operations
- Single worker configuration to avoid conflicts
- Specialized test patterns (`*.mcp.test.ts`, `*-mcp.test.ts`)
- Enhanced reporting and debugging features

## Helper Utilities

### `e2e/utils/mcp-helpers.ts`

A comprehensive utility class (`MCPHelpers`) that provides:

- **Smart Element Finding**: Multiple strategies to locate elements (selector, text, placeholder, label, role)
- **Intelligent Interactions**: `smartClick()` and `smartType()` methods that handle various scenarios
- **Page Context**: Detailed page information extraction for AI context
- **Screenshot Management**: Timestamped screenshots for debugging
- **Interactive Element Discovery**: Automatic detection of all interactive elements on a page
- **Navigation Helpers**: Robust navigation and waiting utilities

## Usage

### Running Tests

```bash
# Run standard Playwright tests
pnpm test:e2e

# Run MCP-optimized tests
pnpm test:mcp

# Run specific MCP test file
pnpm test:e2e -- --project=chromium e2e/mcp-chromium-test.test.ts
```

### Using MCP Helpers in Tests

```typescript
import { test, expect } from '@playwright/test';
import { MCPHelpers } from './utils/mcp-helpers';

test('example MCP test', async ({ page }) => {
	const mcp = new MCPHelpers(page);

	await page.goto('/');
	await mcp.waitForPageReady();

	// Get page context for AI
	const context = await mcp.getPageContext();
	console.log('Page context:', context);

	// Find and interact with elements intelligently
	const success = await mcp.smartClick('Login');
	expect(success).toBe(true);

	// Type in form fields
	await mcp.smartType('email', '<EMAIL>');

	// Take debugging screenshots
	await mcp.takeTimestampedScreenshot('after-login');
});
```

## Key Features for MCP

### 1. Browser Compatibility

- Uses stable Chrome browser for maximum compatibility
- Disabled security features for automation (when safe)
- Optimized launch arguments for AI-driven automation

### 2. Intelligent Element Location

- Multiple fallback strategies for finding elements
- Handles dynamic content and changing selectors
- Returns first element when multiple matches exist

### 3. Robust Error Handling

- Graceful degradation when elements aren't found
- Comprehensive logging for debugging
- Screenshot capture on failures

### 4. AI-Friendly Context

- Detailed page state extraction
- Interactive element discovery
- Comprehensive browser capability detection

## Test Structure

### Verification Tests

The `e2e/mcp-chromium-test.test.ts` file contains comprehensive tests that verify:

1. **Browser Setup**: Confirms Chromium is working correctly
2. **MCP Interactions**: Tests smart element finding and interaction
3. **Modern Web Features**: Verifies support for contemporary web APIs

### Test Results

- Screenshots saved to `test-results-mcp/screenshots/`
- Videos and traces available for failed tests
- HTML reports generated for detailed analysis

## Development Workflow

1. **Start Development Server**: `pnpm dev` (runs on port 5173)
2. **Run MCP Tests**: `pnpm test:mcp`
3. **Debug Issues**: Check screenshots and videos in test results
4. **Iterate**: Use MCP helpers to build robust automation

## Browser Configuration

The setup includes optimized Chrome launch arguments:

- `--disable-web-security`: For cross-origin testing
- `--enable-automation`: Better automation support
- `--no-sandbox`: Required for some environments
- Additional performance and compatibility flags

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Ensure development server is running on port 5173
2. **Element Not Found**: Use `getInteractiveElements()` to discover available elements
3. **Timeout Issues**: Adjust timeouts in configuration for slower operations
4. **Multiple Elements**: MCP helpers automatically select the first matching element

### Debugging Tips

1. Use `takeTimestampedScreenshot()` to capture page state
2. Check `getPageContext()` for comprehensive page information
3. Enable video recording for visual debugging
4. Use `getInteractiveElements()` to understand page structure

## Next Steps

1. Create more specific MCP test patterns for your application
2. Extend MCP helpers with domain-specific utilities
3. Integrate with your CI/CD pipeline using headless mode
4. Consider adding custom selectors for your application's components

This setup provides a solid foundation for AI-driven browser automation using Playwright and Chromium.
