-- Create project gateway stage information table
CREATE TABLE IF NOT EXISTS "public"."project_gateway_stage_info" (
	project_gateway_stage_info_id uuid primary key default gen_random_uuid(),
	project_stage_id uuid not null references project_stage (project_stage_id) on update restrict on delete restrict,
	-- Floor Areas section
	basement_floors numeric(20, 4),
	ground_floor numeric(20, 4),
	upper_floors numeric(20, 4),
	total_gross_internal_floor_area numeric(20, 4),
	usable_area numeric(20, 4),
	circulation_area numeric(20, 4),
	ancillary_areas numeric(20, 4),
	internal_divisions numeric(20, 4),
	spaces_not_enclosed numeric(20, 4),
	total_gross_internal_floor_area_2 numeric(20, 4),
	internal_cube numeric(20, 4),
	area_of_lowest_floor numeric(20, 4),
	site_area numeric(20, 4),
	number_of_units numeric(20, 4),
	-- Storeys section
	nr_of_storeys numeric(20, 4),
	nr_of_storeys_primary numeric(20, 4),
	nr_of_storeys_secondary numeric(20, 4),
	basement_storeys_included_above numeric(20, 4),
	average_storey_height numeric(20, 4),
	below_ground_floors numeric(20, 4),
	ground_floor_height numeric(20, 4),
	above_ground_floors numeric(20, 4),
	external_vertical_envelope numeric(20, 4),
	-- Additional data as JSON
	additional_data jsonb,
	created_by_user_id uuid default auth.uid () not null references profile (user_id) on update restrict on delete restrict,
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null,
	unique (project_stage_id)
);

-- Add comments to describe the table and its columns
comment on table "public"."project_gateway_stage_info" is 'Stores building details for a project''s gateway stage';

comment on column "public"."project_gateway_stage_info"."basement_floors" is 'Basement floors area in m²';

comment on column "public"."project_gateway_stage_info"."ground_floor" is 'Ground floor area in m²';

comment on column "public"."project_gateway_stage_info"."upper_floors" is 'Upper floors area in m²';

comment on column "public"."project_gateway_stage_info"."total_gross_internal_floor_area" is 'Total (Gross Internal Floor Area) in m²';

comment on column "public"."project_gateway_stage_info"."usable_area" is 'Usable Area in m²';

comment on column "public"."project_gateway_stage_info"."circulation_area" is 'Circulation Area in m²';

comment on column "public"."project_gateway_stage_info"."ancillary_areas" is 'Ancillary Areas in m²';

comment on column "public"."project_gateway_stage_info"."internal_divisions" is 'Internal Divisions in m²';

comment on column "public"."project_gateway_stage_info"."spaces_not_enclosed" is 'Spaces not enclosed in m²';

comment on column "public"."project_gateway_stage_info"."total_gross_internal_floor_area_2" is 'Total (Gross Internal Floor Area) in m²';

comment on column "public"."project_gateway_stage_info"."internal_cube" is 'Internal cube in m³';

comment on column "public"."project_gateway_stage_info"."area_of_lowest_floor" is 'Area of lowest floor in m²';

comment on column "public"."project_gateway_stage_info"."site_area" is 'Site area in m²';

comment on column "public"."project_gateway_stage_info"."number_of_units" is 'Number of units in m²';

comment on column "public"."project_gateway_stage_info"."nr_of_storeys" is 'Number of storeys';

comment on column "public"."project_gateway_stage_info"."nr_of_storeys_primary" is 'Number of storeys (Primary) in Nr';

comment on column "public"."project_gateway_stage_info"."nr_of_storeys_secondary" is 'Number of storeys (Secondary) in Nr';

comment on column "public"."project_gateway_stage_info"."basement_storeys_included_above" is 'Basement storeys included above in Nr';

comment on column "public"."project_gateway_stage_info"."average_storey_height" is 'Average storey height';

comment on column "public"."project_gateway_stage_info"."below_ground_floors" is 'Below ground floor(s) in m';

comment on column "public"."project_gateway_stage_info"."ground_floor_height" is 'Ground floor height in m';

comment on column "public"."project_gateway_stage_info"."above_ground_floors" is 'Above ground floor(s) in m';

comment on column "public"."project_gateway_stage_info"."external_vertical_envelope" is 'External vertical envelope in m²';

comment on column "public"."project_gateway_stage_info"."additional_data" is 'Additional data stored as JSON';

-- Create trigger for updating the updated_at column
CREATE TRIGGER update_updated_at BEFORE
UPDATE ON project_gateway_stage_info FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column ();

-- Add RLS policies
alter table project_gateway_stage_info enable row level security;

-- Policy for viewing gateway stage info (same access as project)
create policy "Users can view stage info for projects they can view" on project_gateway_stage_info for
select
	using (
		exists (
			select
				1
			from
				project_stage ps
			where
				ps.project_stage_id = project_gateway_stage_info.project_stage_id
				and can_access_project (ps.project_id)
		)
	);

-- Policy for inserting gateway stage info
create policy "Users can insert gateway stage info for projects they can edit" on project_gateway_stage_info for insert
with
	check (
		exists (
			select
				1
			from
				project_stage ps
			where
				ps.project_stage_id = project_gateway_stage_info.project_stage_id
				and can_modify_project (ps.project_id)
		)
	);

-- Policy for updating gateway stage info
create policy "Users can update gateway stage info for projects they can edit" on project_gateway_stage_info
for update
	using (
		exists (
			select
				1
			from
				project_stage ps
			where
				ps.project_stage_id = project_gateway_stage_info.project_stage_id
				and can_modify_project (ps.project_id)
		)
	)
with
	check (
		exists (
			select
				1
			from
				project_stage ps
			where
				ps.project_stage_id = project_gateway_stage_info.project_stage_id
				and can_modify_project (ps.project_id)
		)
	);

-- Policy for deleting gateway stage info
create policy "Users can delete gateway stage info for projects they can edit" on project_gateway_stage_info for delete using (
	exists (
		select
			1
		from
			project_stage ps
		where
			ps.project_stage_id = project_gateway_stage_info.project_stage_id
			and current_user_has_entity_role ('project', ps.project_id, 'owner')
	)
);
