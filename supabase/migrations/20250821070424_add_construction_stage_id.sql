create table "public"."project_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"old_values" jsonb,
	"new_values" jsonb,
	"project_id" uuid,
	"name" text,
	"description" text,
	"created_by_user_id" uuid,
	"client_id" uuid,
	"wbs_library_id" uuid,
	"construction_stage_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."project_audit" enable row level security;

alter table "public"."project"
add column "construction_stage_id" uuid;

CREATE INDEX idx_project_audit_changed_at ON public.project_audit USING btree (changed_at);

CREATE INDEX idx_project_audit_changed_by ON public.project_audit USING btree (changed_by);

CREATE INDEX idx_project_audit_operation_type ON public.project_audit USING btree (operation_type);

CREATE INDEX idx_project_audit_project_id ON public.project_audit USING btree (project_id);

CREATE UNIQUE INDEX project_audit_pkey ON public.project_audit USING btree (audit_id);

alter table "public"."project_audit"
add constraint "project_audit_pkey" PRIMARY KEY using index "project_audit_pkey";

alter table "public"."project"
add constraint "project_construction_stage_id_fkey" FOREIGN KEY (construction_stage_id) REFERENCES project_stage (project_stage_id) ON UPDATE RESTRICT ON DELETE SET NULL not valid;

alter table "public"."project" validate constraint "project_construction_stage_id_fkey";

alter table "public"."project_audit"
add constraint "project_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."project_audit" validate constraint "project_audit_changed_by_fkey";

alter table "public"."project_audit"
add constraint "project_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."project_audit" validate constraint "project_audit_operation_type_check";

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.audit_project_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.project_audit (
            operation_type, changed_by, changed_at, old_values,
            project_id, name, description, created_by_user_id, client_id,
            wbs_library_id, construction_stage_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.project_id, OLD.name, OLD.description, OLD.created_by_user_id, OLD.client_id,
            OLD.wbs_library_id, OLD.construction_stage_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.project_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            project_id, name, description, created_by_user_id, client_id,
            wbs_library_id, construction_stage_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.project_id, NEW.name, NEW.description, NEW.created_by_user_id, NEW.client_id,
            NEW.wbs_library_id, NEW.construction_stage_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.project_audit (
            operation_type, changed_by, changed_at, new_values,
            project_id, name, description, created_by_user_id, client_id,
            wbs_library_id, construction_stage_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.project_id, NEW.name, NEW.description, NEW.created_by_user_id, NEW.client_id,
            NEW.wbs_library_id, NEW.construction_stage_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

grant references on table "public"."project_audit" to "authenticated";

grant
select
	on table "public"."project_audit" to "authenticated";

grant trigger on table "public"."project_audit" to "authenticated";

grant delete on table "public"."project_audit" to "service_role";

grant insert on table "public"."project_audit" to "service_role";

grant references on table "public"."project_audit" to "service_role";

grant
select
	on table "public"."project_audit" to "service_role";

grant trigger on table "public"."project_audit" to "service_role";

grant
truncate on table "public"."project_audit" to "service_role";

grant
update on table "public"."project_audit" to "service_role";

create policy "System can insert project audit records" on "public"."project_audit" as permissive for insert to service_role
with
	check (true);

create policy "Users can view project audit for accessible projects" on "public"."project_audit" as permissive for
select
	to authenticated using (
		current_user_has_entity_access ('project'::entity_type, project_id)
	);

CREATE TRIGGER audit_project_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.project FOR EACH ROW
EXECUTE FUNCTION audit_project_changes ();
