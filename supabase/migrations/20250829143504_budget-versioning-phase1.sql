create type "public"."budget_version_kind" as enum('stage', 'import', 'manual', 'system');

create table "public"."budget_import" (
	"budget_import_id" uuid not null default gen_random_uuid(),
	"project_id" uuid not null,
	"source_filename" text not null,
	"source_hash" text not null,
	"pre_version_id" uuid not null,
	"new_version_id" uuid not null,
	"is_undone" boolean default false,
	"undone_at" timestamp with time zone,
	"undone_by_user_id" uuid,
	"created_by_user_id" uuid not null,
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."budget_import" enable row level security;

create table "public"."budget_version" (
	"budget_version_id" uuid not null default gen_random_uuid(),
	"project_id" uuid not null,
	"label" text,
	"kind" budget_version_kind not null,
	"stage_id" uuid,
	"prev_version_id" uuid,
	"created_by_user_id" uuid not null,
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."budget_version" enable row level security;

create table "public"."budget_version_item" (
	"budget_version_item_id" uuid not null default gen_random_uuid(),
	"budget_version_id" uuid not null,
	"wbs_library_item_id" uuid not null,
	"quantity" numeric(20, 4) not null,
	"unit" text,
	"material_rate" numeric(20, 4) not null,
	"labor_rate" numeric(20, 4),
	"productivity_per_hour" numeric(20, 4),
	"unit_rate_manual_override" boolean not null default false,
	"unit_rate" numeric(20, 4) not null,
	"factor" numeric(20, 4),
	"remarks" text,
	"cost_certainty" numeric(5, 2),
	"design_certainty" numeric(5, 2),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."budget_version_item" enable row level security;

alter table "public"."budget_snapshot"
add column "budget_version_id" uuid;

alter table "public"."project"
add column "active_budget_version_id" uuid;

CREATE INDEX budget_import_created_at_idx ON public.budget_import USING btree (created_at);

CREATE UNIQUE INDEX budget_import_pkey ON public.budget_import USING btree (budget_import_id);

CREATE INDEX budget_import_project_id_idx ON public.budget_import USING btree (project_id);

CREATE UNIQUE INDEX budget_import_project_source_hash_key ON public.budget_import USING btree (project_id, source_hash);

CREATE INDEX budget_snapshot_budget_version_id_idx ON public.budget_snapshot USING btree (budget_version_id);

CREATE UNIQUE INDEX budget_version_item_pkey ON public.budget_version_item USING btree (budget_version_item_id);

CREATE UNIQUE INDEX budget_version_item_unique_per_version ON public.budget_version_item USING btree (budget_version_id, wbs_library_item_id);

CREATE INDEX budget_version_item_version_id_idx ON public.budget_version_item USING btree (budget_version_id);

CREATE INDEX budget_version_item_wbs_library_item_id_idx ON public.budget_version_item USING btree (wbs_library_item_id);

CREATE INDEX budget_version_kind_project_id_idx ON public.budget_version USING btree (kind, project_id);

CREATE UNIQUE INDEX budget_version_pkey ON public.budget_version USING btree (budget_version_id);

CREATE INDEX budget_version_prev_version_id_idx ON public.budget_version USING btree (prev_version_id);

CREATE INDEX budget_version_project_id_created_at_idx ON public.budget_version USING btree (project_id, created_at DESC);

CREATE INDEX budget_version_stage_id_idx ON public.budget_version USING btree (stage_id);

CREATE INDEX project_active_budget_version_id_idx ON public.project USING btree (active_budget_version_id);

alter table "public"."budget_import"
add constraint "budget_import_pkey" PRIMARY KEY using index "budget_import_pkey";

alter table "public"."budget_version"
add constraint "budget_version_pkey" PRIMARY KEY using index "budget_version_pkey";

alter table "public"."budget_version_item"
add constraint "budget_version_item_pkey" PRIMARY KEY using index "budget_version_item_pkey";

alter table "public"."budget_import"
add constraint "budget_import_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_import" validate constraint "budget_import_created_by_user_id_fkey";

alter table "public"."budget_import"
add constraint "budget_import_new_version_id_fkey" FOREIGN KEY (new_version_id) REFERENCES budget_version (budget_version_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_import" validate constraint "budget_import_new_version_id_fkey";

alter table "public"."budget_import"
add constraint "budget_import_pre_version_id_fkey" FOREIGN KEY (pre_version_id) REFERENCES budget_version (budget_version_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_import" validate constraint "budget_import_pre_version_id_fkey";

alter table "public"."budget_import"
add constraint "budget_import_project_id_fkey" FOREIGN KEY (project_id) REFERENCES project (project_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_import" validate constraint "budget_import_project_id_fkey";

alter table "public"."budget_import"
add constraint "budget_import_project_source_hash_key" UNIQUE using index "budget_import_project_source_hash_key";

alter table "public"."budget_import"
add constraint "budget_import_undone_by_user_id_fkey" FOREIGN KEY (undone_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_import" validate constraint "budget_import_undone_by_user_id_fkey";

alter table "public"."budget_snapshot"
add constraint "budget_snapshot_budget_version_id_fkey" FOREIGN KEY (budget_version_id) REFERENCES budget_version (budget_version_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_snapshot" validate constraint "budget_snapshot_budget_version_id_fkey";

alter table "public"."budget_version"
add constraint "budget_version_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_version" validate constraint "budget_version_created_by_user_id_fkey";

alter table "public"."budget_version"
add constraint "budget_version_no_self_reference" CHECK ((budget_version_id <> prev_version_id)) not valid;

alter table "public"."budget_version" validate constraint "budget_version_no_self_reference";

alter table "public"."budget_version"
add constraint "budget_version_prev_version_id_fkey" FOREIGN KEY (prev_version_id) REFERENCES budget_version (budget_version_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_version" validate constraint "budget_version_prev_version_id_fkey";

alter table "public"."budget_version"
add constraint "budget_version_project_id_fkey" FOREIGN KEY (project_id) REFERENCES project (project_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_version" validate constraint "budget_version_project_id_fkey";

alter table "public"."budget_version"
add constraint "budget_version_stage_consistency" CHECK (
	(
		(
			(kind = 'stage'::budget_version_kind)
			AND (stage_id IS NOT NULL)
		)
		OR (kind <> 'stage'::budget_version_kind)
	)
) not valid;

alter table "public"."budget_version" validate constraint "budget_version_stage_consistency";

alter table "public"."budget_version"
add constraint "budget_version_stage_id_fkey" FOREIGN KEY (stage_id) REFERENCES project_stage (project_stage_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_version" validate constraint "budget_version_stage_id_fkey";

alter table "public"."budget_version_item"
add constraint "budget_version_item_unique_per_version" UNIQUE using index "budget_version_item_unique_per_version";

alter table "public"."budget_version_item"
add constraint "budget_version_item_version_id_fkey" FOREIGN KEY (budget_version_id) REFERENCES budget_version (budget_version_id) ON UPDATE RESTRICT ON DELETE CASCADE not valid;

alter table "public"."budget_version_item" validate constraint "budget_version_item_version_id_fkey";

alter table "public"."budget_version_item"
add constraint "budget_version_item_wbs_library_item_id_fkey" FOREIGN KEY (wbs_library_item_id) REFERENCES wbs_library_item (wbs_library_item_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_version_item" validate constraint "budget_version_item_wbs_library_item_id_fkey";

alter table "public"."project"
add constraint "project_active_budget_version_id_fkey" FOREIGN KEY (active_budget_version_id) REFERENCES budget_version (budget_version_id) ON UPDATE RESTRICT ON DELETE SET NULL not valid;

alter table "public"."project" validate constraint "project_active_budget_version_id_fkey";

grant delete on table "public"."budget_import" to "anon";

grant insert on table "public"."budget_import" to "anon";

grant references on table "public"."budget_import" to "anon";

grant
select
	on table "public"."budget_import" to "anon";

grant trigger on table "public"."budget_import" to "anon";

grant
truncate on table "public"."budget_import" to "anon";

grant
update on table "public"."budget_import" to "anon";

grant delete on table "public"."budget_import" to "authenticated";

grant insert on table "public"."budget_import" to "authenticated";

grant references on table "public"."budget_import" to "authenticated";

grant
select
	on table "public"."budget_import" to "authenticated";

grant trigger on table "public"."budget_import" to "authenticated";

grant
truncate on table "public"."budget_import" to "authenticated";

grant
update on table "public"."budget_import" to "authenticated";

grant delete on table "public"."budget_import" to "service_role";

grant insert on table "public"."budget_import" to "service_role";

grant references on table "public"."budget_import" to "service_role";

grant
select
	on table "public"."budget_import" to "service_role";

grant trigger on table "public"."budget_import" to "service_role";

grant
truncate on table "public"."budget_import" to "service_role";

grant
update on table "public"."budget_import" to "service_role";

grant delete on table "public"."budget_version" to "anon";

grant insert on table "public"."budget_version" to "anon";

grant references on table "public"."budget_version" to "anon";

grant
select
	on table "public"."budget_version" to "anon";

grant trigger on table "public"."budget_version" to "anon";

grant
truncate on table "public"."budget_version" to "anon";

grant
update on table "public"."budget_version" to "anon";

grant delete on table "public"."budget_version" to "authenticated";

grant insert on table "public"."budget_version" to "authenticated";

grant references on table "public"."budget_version" to "authenticated";

grant
select
	on table "public"."budget_version" to "authenticated";

grant trigger on table "public"."budget_version" to "authenticated";

grant
truncate on table "public"."budget_version" to "authenticated";

grant
update on table "public"."budget_version" to "authenticated";

grant delete on table "public"."budget_version" to "service_role";

grant insert on table "public"."budget_version" to "service_role";

grant references on table "public"."budget_version" to "service_role";

grant
select
	on table "public"."budget_version" to "service_role";

grant trigger on table "public"."budget_version" to "service_role";

grant
truncate on table "public"."budget_version" to "service_role";

grant
update on table "public"."budget_version" to "service_role";

grant delete on table "public"."budget_version_item" to "anon";

grant insert on table "public"."budget_version_item" to "anon";

grant references on table "public"."budget_version_item" to "anon";

grant
select
	on table "public"."budget_version_item" to "anon";

grant trigger on table "public"."budget_version_item" to "anon";

grant
truncate on table "public"."budget_version_item" to "anon";

grant
update on table "public"."budget_version_item" to "anon";

grant delete on table "public"."budget_version_item" to "authenticated";

grant insert on table "public"."budget_version_item" to "authenticated";

grant references on table "public"."budget_version_item" to "authenticated";

grant
select
	on table "public"."budget_version_item" to "authenticated";

grant trigger on table "public"."budget_version_item" to "authenticated";

grant
truncate on table "public"."budget_version_item" to "authenticated";

grant
update on table "public"."budget_version_item" to "authenticated";

grant delete on table "public"."budget_version_item" to "service_role";

grant insert on table "public"."budget_version_item" to "service_role";

grant references on table "public"."budget_version_item" to "service_role";

grant
select
	on table "public"."budget_version_item" to "service_role";

grant trigger on table "public"."budget_version_item" to "service_role";

grant
truncate on table "public"."budget_version_item" to "service_role";

grant
update on table "public"."budget_version_item" to "service_role";

create policy "Project editors can delete budget imports" on "public"."budget_import" as permissive for delete to authenticated using (can_modify_project (project_id));

create policy "Project editors can insert budget imports" on "public"."budget_import" as permissive for insert to authenticated
with
	check (can_modify_project (project_id));

create policy "Project editors can update budget imports" on "public"."budget_import" as permissive
for update
	to authenticated using (can_modify_project (project_id))
with
	check (can_modify_project (project_id));

create policy "Project viewers can view budget imports" on "public"."budget_import" as permissive for
select
	to authenticated using (can_access_project (project_id));

create policy "Project editors can delete budget versions" on "public"."budget_version" as permissive for delete to authenticated using (can_modify_project (project_id));

create policy "Project editors can insert budget versions" on "public"."budget_version" as permissive for insert to authenticated
with
	check (can_modify_project (project_id));

create policy "Project editors can update budget versions" on "public"."budget_version" as permissive
for update
	to authenticated using (can_modify_project (project_id))
with
	check (can_modify_project (project_id));

create policy "Project viewers can view budget versions" on "public"."budget_version" as permissive for
select
	to authenticated using (can_access_project (project_id));

create policy "Editors can delete items for active stage version" on "public"."budget_version_item" as permissive for delete to authenticated using (
	(
		EXISTS (
			SELECT
				1
			FROM
				(
					budget_version v
					JOIN project p ON ((p.project_id = v.project_id))
				)
			WHERE
				(
					(
						v.budget_version_id = budget_version_item.budget_version_id
					)
					AND (v.kind = 'stage'::budget_version_kind)
					AND (p.active_budget_version_id = v.budget_version_id)
					AND can_modify_project (v.project_id)
				)
		)
	)
);

create policy "Editors can insert items for active stage version" on "public"."budget_version_item" as permissive for insert to authenticated
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						budget_version v
						JOIN project p ON ((p.project_id = v.project_id))
					)
				WHERE
					(
						(
							v.budget_version_id = budget_version_item.budget_version_id
						)
						AND (v.kind = 'stage'::budget_version_kind)
						AND (p.active_budget_version_id = v.budget_version_id)
						AND can_modify_project (v.project_id)
					)
			)
		)
	);

create policy "Editors can update items for active stage version" on "public"."budget_version_item" as permissive
for update
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						budget_version v
						JOIN project p ON ((p.project_id = v.project_id))
					)
				WHERE
					(
						(
							v.budget_version_id = budget_version_item.budget_version_id
						)
						AND (v.kind = 'stage'::budget_version_kind)
						AND (p.active_budget_version_id = v.budget_version_id)
						AND can_modify_project (v.project_id)
					)
			)
		)
	)
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						budget_version v
						JOIN project p ON ((p.project_id = v.project_id))
					)
				WHERE
					(
						(
							v.budget_version_id = budget_version_item.budget_version_id
						)
						AND (v.kind = 'stage'::budget_version_kind)
						AND (p.active_budget_version_id = v.budget_version_id)
						AND can_modify_project (v.project_id)
					)
			)
		)
	);

create policy "Project viewers can view budget version items" on "public"."budget_version_item" as permissive for
select
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					budget_version v
				WHERE
					(
						(
							v.budget_version_id = budget_version_item.budget_version_id
						)
						AND can_access_project (v.project_id)
					)
			)
		)
	);

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.budget_version FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.budget_version_item FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();
