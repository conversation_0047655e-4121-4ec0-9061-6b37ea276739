set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_vendor_creation_hierarchy () RETURNS TABLE (
	entity_type text,
	entity_id uuid,
	entity_name text,
	parent_entity_type text,
	parent_entity_id uuid,
	parent_entity_name text,
	grandparent_entity_type text,
	grandparent_entity_id uuid,
	grandparent_entity_name text,
	user_role text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	current_user_id uuid;
BEGIN
	-- Get the current user ID once
	current_user_id := auth.uid();
	-- Return organizations where user has admin role
	RETURN QUERY
	SELECT
		'organization'::text as entity_type,
		o.org_id as entity_id,
		o.name as entity_name,
		NULL::text as parent_entity_type,
		NULL::uuid as parent_entity_id,
		NULL::text as parent_entity_name,
		NULL::text as grandparent_entity_type,
		NULL::uuid as grandparent_entity_id,
		NULL::text as grandparent_entity_name,
		m.role::text as user_role
	FROM public.organization o
	JOIN public.membership m ON m.entity_id = o.org_id
	WHERE m.user_id = current_user_id
		AND m.entity_type = 'organization'
		AND m.role >= 'admin'
	ORDER BY o.name;

	-- Return clients where user has admin role OR belongs to organization where user is admin
	RETURN QUERY
	SELECT
		'client'::text as entity_type,
		c.client_id as entity_id,
		c.name as entity_name,
		'organization'::text as parent_entity_type,
		o.org_id as parent_entity_id,
		o.name as parent_entity_name,
		NULL::text as grandparent_entity_type,
		NULL::uuid as grandparent_entity_id,
		NULL::text as grandparent_entity_name,
		COALESCE(cm.role, om.role)::text as user_role
	FROM public.client c
	JOIN public.organization o ON o.org_id = c.org_id
	LEFT JOIN public.membership cm ON cm.entity_id = c.client_id
		AND cm.user_id = current_user_id
		AND cm.entity_type = 'client'
		AND cm.role >= 'admin'
	LEFT JOIN public.membership om ON om.entity_id = o.org_id
		AND om.user_id = current_user_id
		AND om.entity_type = 'organization'
		AND om.role >= 'admin'
	WHERE (cm.user_id IS NOT NULL OR om.user_id IS NOT NULL)
	ORDER BY o.name, c.name;

	-- Return projects where user has editor+ role OR belongs to client/organization where user has appropriate access
	RETURN QUERY
	SELECT
		'project'::text as entity_type,
		p.project_id as entity_id,
		p.name as entity_name,
		'client'::text as parent_entity_type,
		c.client_id as parent_entity_id,
		c.name as parent_entity_name,
		'organization'::text as grandparent_entity_type,
		o.org_id as grandparent_entity_id,
		o.name as grandparent_entity_name,
		COALESCE(pm.role, cm.role, om.role)::text as user_role
	FROM public.project p
	JOIN public.client c ON c.client_id = p.client_id
	JOIN public.organization o ON o.org_id = c.org_id
	LEFT JOIN public.membership pm ON pm.entity_id = p.project_id
		AND pm.user_id = current_user_id
		AND pm.entity_type = 'project'
		AND pm.role >= 'editor'
	LEFT JOIN public.membership cm ON cm.entity_id = c.client_id
		AND cm.user_id = current_user_id
		AND cm.entity_type = 'client'
		AND cm.role >= 'admin'
	LEFT JOIN public.membership om ON om.entity_id = o.org_id
		AND om.user_id = current_user_id
		AND om.entity_type = 'organization'
		AND om.role >= 'admin'
	WHERE (pm.user_id IS NOT NULL OR cm.user_id IS NOT NULL OR om.user_id IS NOT NULL)
	ORDER BY o.name, c.name, p.name;
END;
$function$;
