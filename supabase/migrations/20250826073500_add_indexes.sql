CREATE INDEX budget_snapshot_created_by_user_id_idx ON public.budget_snapshot USING btree (created_by_user_id);

CREATE INDEX budget_snapshot_project_stage_id_idx ON public.budget_snapshot USING btree (project_stage_id);

CREATE INDEX client_org_id_idx ON public.client USING btree (org_id);

CREATE INDEX gateway_checklist_item_project_stage_id_idx ON public.gateway_checklist_item USING btree (project_stage_id);

CREATE INDEX invite_inviter_id_idx ON public.invite USING btree (inviter_id);

CREATE INDEX invite_updated_by_idx ON public.invite USING btree (updated_by)
WHERE
	(updated_by IS NOT NULL);

CREATE INDEX invoice_purchase_order_id_invoice_date_idx ON public.invoice USING btree (purchase_order_id, invoice_date DESC);

CREATE INDEX project_client_id_idx ON public.project USING btree (client_id);

CREATE INDEX project_created_by_user_id_idx ON public.project USING btree (created_by_user_id);

CREATE INDEX project_wbs_library_id_idx ON public.project USING btree (wbs_library_id);

CREATE INDEX risk_register_risk_owner_user_id_idx ON public.risk_register USING btree (risk_owner_user_id)
WHERE
	(risk_owner_user_id IS NOT NULL);
