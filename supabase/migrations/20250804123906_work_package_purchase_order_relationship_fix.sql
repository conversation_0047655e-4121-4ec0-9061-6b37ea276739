alter table "public"."work_package"
drop constraint "work_package_purchase_order_id_fkey";

drop function if exists "public"."get_purchase_orders_for_work_package" (project_id_param uuid);

drop function if exists "public"."get_accessible_work_packages" (project_id_param uuid);

drop index if exists "public"."work_package_purchase_order_id_idx";

alter table "public"."purchase_order"
add column "work_package_id" uuid not null;

alter table "public"."purchase_order_audit"
add column "work_package_id" uuid;

alter table "public"."work_package"
drop column "purchase_order_id";

alter table "public"."work_package_audit"
drop column "purchase_order_id";

CREATE INDEX purchase_order_work_package_id_idx ON public.purchase_order USING btree (work_package_id);

alter table "public"."purchase_order"
add constraint "purchase_order_work_package_id_fkey" FOREIGN KEY (work_package_id) REFERENCES work_package (work_package_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."purchase_order" validate constraint "purchase_order_work_package_id_fkey";

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_work_packages_for_project (project_id_param uuid) RETURNS TABLE (
	work_package_id uuid,
	name text,
	description text,
	wbs_code text
) LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return work packages for the project with WBS information
	RETURN QUERY
	SELECT
		wp.work_package_id,
		wp.name,
		wp.description,
		wli.code AS wbs_code
	FROM public.work_package wp
	LEFT JOIN public.wbs_library_item wli ON wp.wbs_library_item_id = wli.wbs_library_item_id
	WHERE wp.project_id = project_id_param
	ORDER BY wp.name;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_purchase_order_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-********0000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.purchase_order_audit (
            operation_type, changed_by, changed_at, old_values,
            purchase_order_id, po_number, description, po_date, project_id, vendor_id,
            account, original_amount, co_amount, freight, tax, other,
            notes, created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.purchase_order_id, OLD.po_number, OLD.description, OLD.po_date, OLD.project_id, OLD.vendor_id,
            OLD.account, OLD.original_amount, OLD.co_amount, OLD.freight, OLD.tax, OLD.other,
            OLD.notes, OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.purchase_order_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            purchase_order_id, po_number, description, po_date, project_id, vendor_id, work_package_id,
            account, original_amount, co_amount, freight, tax, other,
            notes, created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.purchase_order_id, NEW.po_number, NEW.description, NEW.po_date, NEW.project_id, NEW.vendor_id, NEW.work_package_id,
            NEW.account, NEW.original_amount, NEW.co_amount, NEW.freight, NEW.tax, NEW.other,
            NEW.notes, NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.purchase_order_audit (
            operation_type, changed_by, changed_at, new_values,
            purchase_order_id, po_number, description, po_date, project_id, vendor_id, work_package_id,
            account, original_amount, co_amount, freight, tax, other,
            notes, created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.purchase_order_id, NEW.po_number, NEW.description, NEW.po_date, NEW.project_id, NEW.vendor_id, NEW.work_package_id,
            NEW.account, NEW.original_amount, NEW.co_amount, NEW.freight, NEW.tax, NEW.other,
            NEW.notes, NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_work_package_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-********0000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, old_values,
            work_package_id, name, description, project_id,
            wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.work_package_id, OLD.name, OLD.description, OLD.project_id,
            OLD.wbs_library_item_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            work_package_id, name, description, project_id,
            wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.work_package_id, NEW.name, NEW.description, NEW.project_id,
            NEW.wbs_library_item_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, new_values,
            work_package_id, name, description, project_id,
            wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.work_package_id, NEW.name, NEW.description, NEW.project_id,
            NEW.wbs_library_item_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_accessible_work_packages (project_id_param uuid) RETURNS TABLE (
	work_package_id uuid,
	name text,
	description text,
	project_id uuid,
	purchase_order_count integer,
	wbs_library_item_id uuid,
	wbs_code text,
	wbs_description text,
	created_at timestamp with time zone,
	updated_at timestamp with time zone
) LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return work packages for the project with related information
	RETURN QUERY
	SELECT
		wp.work_package_id,
		wp.name,
		wp.description,
		wp.project_id,
		COALESCE(po_agg.purchase_order_count, 0) AS purchase_order_count,
		wp.wbs_library_item_id,
		wli.code AS wbs_code,
		wli.description AS wbs_description,
		wp.created_at,
		wp.updated_at
	FROM public.work_package wp
	LEFT JOIN public.wbs_library_item wli ON wp.wbs_library_item_id = wli.wbs_library_item_id
	LEFT JOIN (
		SELECT
			po.work_package_id,
			COUNT(po.purchase_order_id)::integer AS purchase_order_count
		FROM public.purchase_order po
		WHERE po.project_id = project_id_param
		GROUP BY po.work_package_id
	) po_agg ON wp.work_package_id = po_agg.work_package_id
	WHERE wp.project_id = project_id_param
	ORDER BY wp.name;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_cost_detail_data (project_id_param uuid) RETURNS TABLE (
	wbs_library_item_id uuid,
	wbs_code text,
	wbs_description text,
	wbs_level integer,
	parent_item_id uuid,
	budget_amount numeric,
	quantity numeric,
	unit_rate numeric,
	factor numeric,
	work_packages jsonb,
	purchase_orders jsonb
) LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
	v_client_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Get the client_id for the project
	SELECT p.client_id INTO v_client_id
	FROM public.project p
	WHERE p.project_id = project_id_param;

	-- Return cost detail data with hierarchical WBS structure
	RETURN QUERY
	WITH wbs_budget AS (
		-- Get all WBS items for the project with their budget data
		SELECT
			wli.wbs_library_item_id,
			wli.code AS wbs_code,
			wli.description AS wbs_description,
			wli.level AS wbs_level,
			wli.parent_item_id,
			bli.quantity,
			bli.unit_rate,
			bli.factor,
			CASE
				WHEN bli.quantity IS NOT NULL AND bli.unit_rate IS NOT NULL THEN
					bli.quantity * bli.unit_rate * COALESCE(bli.factor, 1)
				ELSE 0
			END AS budget_amount
		FROM public.wbs_library_item wli
		LEFT JOIN public.budget_line_item_current bli ON wli.wbs_library_item_id = bli.wbs_library_item_id
			AND bli.project_id = project_id_param
		WHERE wli.wbs_library_id = (
			SELECT p.wbs_library_id
			FROM public.project p
			WHERE p.project_id = project_id_param
		)
		AND (
			wli.item_type = 'Standard'::public.wbs_item_type
			OR (wli.item_type = 'Custom'::public.wbs_item_type AND (
				wli.client_id = v_client_id OR wli.project_id = project_id_param
			))
		)
	),
	work_package_data AS (
		-- Get work packages grouped by WBS item
		SELECT
			wp.wbs_library_item_id,
			jsonb_agg(
				jsonb_build_object(
					'work_package_id', wp.work_package_id,
					'name', wp.name,
					'description', wp.description,
					'purchase_order_count', COALESCE(po_count.count, 0)
				)
			) AS work_packages
		FROM public.work_package wp
		LEFT JOIN (
			SELECT
				po.work_package_id,
				COUNT(po.purchase_order_id) AS count
			FROM public.purchase_order po
			WHERE po.project_id = project_id_param
			GROUP BY po.work_package_id
		) po_count ON wp.work_package_id = po_count.work_package_id
		WHERE wp.project_id = project_id_param
		GROUP BY wp.wbs_library_item_id
	),
	purchase_order_data AS (
		-- Get purchase orders for each WBS item through work packages
		SELECT
			wp.wbs_library_item_id,
			jsonb_agg(
				DISTINCT jsonb_build_object(
					'purchase_order_id', po.purchase_order_id,
					'po_number', po.po_number,
					'description', po.description,
					'vendor_name', v.name,
					'original_amount', po.original_amount,
					'co_amount', po.co_amount,
					'work_package_id', po.work_package_id
				)
			) FILTER (WHERE po.purchase_order_id IS NOT NULL) AS purchase_orders
		FROM public.work_package wp
		LEFT JOIN public.purchase_order po ON wp.work_package_id = po.work_package_id
		LEFT JOIN public.vendor v ON po.vendor_id = v.vendor_id
		WHERE wp.project_id = project_id_param
		GROUP BY wp.wbs_library_item_id
	)
	SELECT
		wb.wbs_library_item_id,
		wb.wbs_code,
		wb.wbs_description,
		wb.wbs_level,
		wb.parent_item_id,
		wb.budget_amount,
		wb.quantity,
		wb.unit_rate,
		wb.factor,
		COALESCE(wpd.work_packages, '[]'::jsonb) AS work_packages,
		COALESCE(pod.purchase_orders, '[]'::jsonb) AS purchase_orders
	FROM wbs_budget wb
	LEFT JOIN work_package_data wpd ON wb.wbs_library_item_id = wpd.wbs_library_item_id
	LEFT JOIN purchase_order_data pod ON wb.wbs_library_item_id = pod.wbs_library_item_id
	ORDER BY wb.wbs_code;
END;
$function$;
