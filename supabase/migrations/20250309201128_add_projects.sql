-- Project Table
create table "public"."project" (
	project_id uuid not null default gen_random_uuid() primary key,
	name text not null,
	description text,
	created_by_user_id uuid default auth.uid () not null references profile (user_id) on update restrict on delete restrict,
	client_id uuid not null references client (client_id) on update restrict on delete restrict,
	wbs_library_id uuid not null references wbs_library (wbs_library_id) on update restrict on delete restrict,
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null,
	unique (name, client_id)
);

comment on table "public"."project" is 'Project containing stages, WBS items, and other project-related data';

-- Create trigger on project to call the update function before any update
create trigger update_updated_at before
update on public.project for each row
execute function public.update_updated_at_column ();

-- Enable Row Level Security
alter table project enable row level security;

-- Grant access to service_role
grant
select
,
	insert,
update,
delete,
references,
trigger,
truncate on public.project to service_role;
