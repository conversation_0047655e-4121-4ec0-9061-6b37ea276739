-- Manual backfill migration: create bootstrap budget versions and set active pointers
-- Idempotent: only affects projects without an active budget version
DO $$
DECLARE
  rec RECORD;
  v_id uuid;
BEGIN
  -- Create a stage-kind version per project if none is active
  FOR rec IN
    SELECT
      p.project_id,
      p.created_by_user_id,
      (
        SELECT ps.project_stage_id
        FROM public.project_stage ps
        WHERE ps.project_id = p.project_id
        ORDER BY ps.date_started DESC NULLS LAST, ps.stage_order DESC
        LIMIT 1
      ) AS stage_id
    FROM public.project p
    WHERE p.active_budget_version_id IS NULL
  LOOP
    -- Insert version; require a stage if available, else fall back to manual kind
    IF rec.stage_id IS NOT NULL THEN
      INSERT INTO public.budget_version (
        project_id, label, kind, stage_id, prev_version_id, created_by_user_id
      ) VALUES (
        rec.project_id,
        'Backfill: initial stage version',
        'stage',
        rec.stage_id,
        NULL,
        rec.created_by_user_id
      ) RETURNING budget_version_id INTO v_id;
    ELSE
      INSERT INTO public.budget_version (
        project_id, label, kind, stage_id, prev_version_id, created_by_user_id
      ) VALUES (
        rec.project_id,
        'Backfill: initial manual version',
        'manual',
        NULL,
        NULL,
        rec.created_by_user_id
      ) RETURNING budget_version_id INTO v_id;
    END IF;

    -- Populate version items from current budget items
    INSERT INTO public.budget_version_item (
      budget_version_id,
      wbs_library_item_id,
      quantity,
      unit,
      material_rate,
      labor_rate,
      productivity_per_hour,
      unit_rate_manual_override,
      unit_rate,
      factor,
      remarks,
      cost_certainty,
      design_certainty,
      created_at,
      updated_at
    )
    SELECT
      v_id,
      bli.wbs_library_item_id,
      bli.quantity,
      bli.unit,
      bli.material_rate,
      bli.labor_rate,
      bli.productivity_per_hour,
      bli.unit_rate_manual_override,
      bli.unit_rate,
      bli.factor,
      bli.remarks,
      bli.cost_certainty,
      bli.design_certainty,
      bli.created_at,
      bli.updated_at
    FROM public.budget_line_item_current bli
    WHERE bli.project_id = rec.project_id;

    -- Set active pointer on project
    UPDATE public.project
    SET active_budget_version_id = v_id
    WHERE project_id = rec.project_id;
  END LOOP;

  -- Link any existing snapshots to the active version for their project
  UPDATE public.budget_snapshot bs
  SET budget_version_id = p.active_budget_version_id
  FROM public.project_stage ps
  JOIN public.project p ON p.project_id = ps.project_id
  WHERE bs.project_stage_id = ps.project_stage_id
    AND bs.budget_version_id IS NULL
    AND p.active_budget_version_id IS NOT NULL;
END $$;
