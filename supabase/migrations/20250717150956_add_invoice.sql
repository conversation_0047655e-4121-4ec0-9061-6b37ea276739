create table "public"."invoice" (
	"invoice_id" uuid not null default gen_random_uuid(),
	"po_number" text not null,
	"vendor_id" uuid not null,
	"description" text,
	"invoice_date" date not null,
	"account" text not null,
	"amount" numeric(15, 2) not null,
	"period" text,
	"post_date" date not null,
	"notes" text,
	"project_id" uuid not null,
	"created_by_user_id" uuid not null default auth.uid (),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."invoice" enable row level security;

create table "public"."invoice_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default now(),
	"old_values" jsonb,
	"new_values" jsonb,
	"invoice_id" uuid,
	"po_number" text,
	"vendor_id" uuid,
	"description" text,
	"invoice_date" date,
	"account" text,
	"amount" numeric(15, 2),
	"period" text,
	"post_date" date,
	"notes" text,
	"project_id" uuid,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."invoice_audit" enable row level security;

CREATE INDEX invoice_account_idx ON public.invoice USING btree (account);

CREATE INDEX invoice_audit_changed_at_idx ON public.invoice_audit USING btree (changed_at);

CREATE INDEX invoice_audit_changed_by_idx ON public.invoice_audit USING btree (changed_by);

CREATE INDEX invoice_audit_invoice_id_idx ON public.invoice_audit USING btree (invoice_id);

CREATE INDEX invoice_audit_operation_type_idx ON public.invoice_audit USING btree (operation_type);

CREATE UNIQUE INDEX invoice_audit_pkey ON public.invoice_audit USING btree (audit_id);

CREATE INDEX invoice_audit_project_id_idx ON public.invoice_audit USING btree (project_id);

CREATE INDEX invoice_created_by_user_id_idx ON public.invoice USING btree (created_by_user_id);

CREATE INDEX invoice_invoice_date_idx ON public.invoice USING btree (invoice_date);

CREATE INDEX invoice_period_idx ON public.invoice USING btree (period);

CREATE UNIQUE INDEX invoice_pkey ON public.invoice USING btree (invoice_id);

CREATE INDEX invoice_post_date_idx ON public.invoice USING btree (post_date);

CREATE INDEX invoice_project_id_idx ON public.invoice USING btree (project_id);

CREATE INDEX invoice_vendor_id_idx ON public.invoice USING btree (vendor_id);

alter table "public"."invoice"
add constraint "invoice_pkey" PRIMARY KEY using index "invoice_pkey";

alter table "public"."invoice_audit"
add constraint "invoice_audit_pkey" PRIMARY KEY using index "invoice_audit_pkey";

alter table "public"."invoice"
add constraint "invoice_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."invoice" validate constraint "invoice_created_by_user_id_fkey";

alter table "public"."invoice"
add constraint "invoice_project_id_fkey" FOREIGN KEY (project_id) REFERENCES project (project_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."invoice" validate constraint "invoice_project_id_fkey";

alter table "public"."invoice"
add constraint "invoice_vendor_id_fkey" FOREIGN KEY (vendor_id) REFERENCES vendor (vendor_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."invoice" validate constraint "invoice_vendor_id_fkey";

alter table "public"."invoice_audit"
add constraint "invoice_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."invoice_audit" validate constraint "invoice_audit_changed_by_fkey";

alter table "public"."invoice_audit"
add constraint "invoice_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."invoice_audit" validate constraint "invoice_audit_operation_type_check";

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.audit_invoice_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-********0000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.invoice_audit (
            operation_type, changed_by, changed_at, old_values,
            invoice_id, po_number, vendor_id, description, invoice_date,
            account, amount, period, post_date, notes, project_id,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.invoice_id, OLD.po_number, OLD.vendor_id, OLD.description, OLD.invoice_date,
            OLD.account, OLD.amount, OLD.period, OLD.post_date, OLD.notes, OLD.project_id,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.invoice_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            invoice_id, po_number, vendor_id, description, invoice_date,
            account, amount, period, post_date, notes, project_id,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.invoice_id, NEW.po_number, NEW.vendor_id, NEW.description, NEW.invoice_date,
            NEW.account, NEW.amount, NEW.period, NEW.post_date, NEW.notes, NEW.project_id,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.invoice_audit (
            operation_type, changed_by, changed_at, new_values,
            invoice_id, po_number, vendor_id, description, invoice_date,
            account, amount, period, post_date, notes, project_id,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.invoice_id, NEW.po_number, NEW.vendor_id, NEW.description, NEW.invoice_date,
            NEW.account, NEW.amount, NEW.period, NEW.post_date, NEW.notes, NEW.project_id,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_accessible_invoices (project_id_param uuid) RETURNS TABLE (
	invoice_id uuid,
	po_number text,
	description text,
	invoice_date date,
	vendor_name text,
	vendor_id uuid,
	account text,
	amount numeric,
	period text,
	post_date date,
	notes text,
	created_at timestamp with time zone,
	created_by_name text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RAISE EXCEPTION 'Access denied to project';
	END IF;

	-- Return invoices for the project with vendor and creator information
	RETURN QUERY
	SELECT
		i.invoice_id,
		i.po_number,
		i.description,
		i.invoice_date,
		v.name AS vendor_name,
		i.vendor_id,
		i.account,
		i.amount,
		i.period,
		i.post_date,
		i.notes,
		i.created_at,
		p.full_name AS created_by_name
	FROM public.invoice i
	LEFT JOIN public.vendor v ON i.vendor_id = v.vendor_id
	LEFT JOIN public.profile p ON i.created_by_user_id = p.user_id
	WHERE i.project_id = project_id_param
	ORDER BY i.invoice_date DESC, i.po_number;
END;
$function$;

grant delete on table "public"."invoice" to "authenticated";

grant insert on table "public"."invoice" to "authenticated";

grant references on table "public"."invoice" to "authenticated";

grant
select
	on table "public"."invoice" to "authenticated";

grant trigger on table "public"."invoice" to "authenticated";

grant
update on table "public"."invoice" to "authenticated";

grant delete on table "public"."invoice" to "service_role";

grant insert on table "public"."invoice" to "service_role";

grant references on table "public"."invoice" to "service_role";

grant
select
	on table "public"."invoice" to "service_role";

grant trigger on table "public"."invoice" to "service_role";

grant
truncate on table "public"."invoice" to "service_role";

grant
update on table "public"."invoice" to "service_role";

grant delete on table "public"."invoice_audit" to "authenticated";

grant insert on table "public"."invoice_audit" to "authenticated";

grant references on table "public"."invoice_audit" to "authenticated";

grant
select
	on table "public"."invoice_audit" to "authenticated";

grant trigger on table "public"."invoice_audit" to "authenticated";

grant
update on table "public"."invoice_audit" to "authenticated";

grant delete on table "public"."invoice_audit" to "service_role";

grant insert on table "public"."invoice_audit" to "service_role";

grant references on table "public"."invoice_audit" to "service_role";

grant
select
	on table "public"."invoice_audit" to "service_role";

grant trigger on table "public"."invoice_audit" to "service_role";

grant
truncate on table "public"."invoice_audit" to "service_role";

grant
update on table "public"."invoice_audit" to "service_role";

create policy "Users can create invoices for projects they can edit" on "public"."invoice" as permissive for insert to authenticated
with
	check (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	);

create policy "Users can delete invoices for projects they can edit" on "public"."invoice" as permissive for delete to authenticated using (
	current_user_has_entity_role (
		'project'::entity_type,
		project_id,
		'editor'::membership_role
	)
);

create policy "Users can update invoices for projects they can edit" on "public"."invoice" as permissive
for update
	to authenticated using (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	)
with
	check (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	);

create policy "Users can view invoices for accessible projects" on "public"."invoice" as permissive for
select
	to authenticated using (
		current_user_has_entity_access ('project'::entity_type, project_id)
	);

create policy "System can insert invoice audit records" on "public"."invoice_audit" as permissive for insert to service_role
with
	check (true);

create policy "Users can view invoice audit for accessible projects" on "public"."invoice_audit" as permissive for
select
	to authenticated using (
		current_user_has_entity_access ('project'::entity_type, project_id)
	);

CREATE TRIGGER audit_invoice_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.invoice FOR EACH ROW
EXECUTE FUNCTION audit_invoice_changes ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.invoice FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();
