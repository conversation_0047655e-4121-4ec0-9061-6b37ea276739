create type "public"."risk_status" as enum('risk', 'pending', 'approved');

drop trigger if exists "audit_approved_changes_trigger" on "public"."approved_changes";

drop trigger if exists "update_updated_at" on "public"."approved_changes";

drop policy "Project Editors and Owners can insert approved changes" on "public"."approved_changes";

drop policy "Project Editors and Owners can update approved changes" on "public"."approved_changes";

drop policy "Project Owners can delete approved changes" on "public"."approved_changes";

drop policy "Users can view approved changes for projects they have access t" on "public"."approved_changes";

drop policy "System can insert approved changes audit records" on "public"."approved_changes_audit";

drop policy "Users can view approved changes audit for accessible projects" on "public"."approved_changes_audit";

revoke delete on table "public"."approved_changes"
from
	"anon";

revoke insert on table "public"."approved_changes"
from
	"anon";

revoke references on table "public"."approved_changes"
from
	"anon";

revoke
select
	on table "public"."approved_changes"
from
	"anon";

revoke trigger on table "public"."approved_changes"
from
	"anon";

revoke
truncate on table "public"."approved_changes"
from
	"anon";

revoke
update on table "public"."approved_changes"
from
	"anon";

revoke delete on table "public"."approved_changes"
from
	"authenticated";

revoke insert on table "public"."approved_changes"
from
	"authenticated";

revoke references on table "public"."approved_changes"
from
	"authenticated";

revoke
select
	on table "public"."approved_changes"
from
	"authenticated";

revoke trigger on table "public"."approved_changes"
from
	"authenticated";

revoke
truncate on table "public"."approved_changes"
from
	"authenticated";

revoke
update on table "public"."approved_changes"
from
	"authenticated";

revoke delete on table "public"."approved_changes"
from
	"service_role";

revoke insert on table "public"."approved_changes"
from
	"service_role";

revoke references on table "public"."approved_changes"
from
	"service_role";

revoke
select
	on table "public"."approved_changes"
from
	"service_role";

revoke trigger on table "public"."approved_changes"
from
	"service_role";

revoke
truncate on table "public"."approved_changes"
from
	"service_role";

revoke
update on table "public"."approved_changes"
from
	"service_role";

revoke delete on table "public"."approved_changes_audit"
from
	"anon";

revoke insert on table "public"."approved_changes_audit"
from
	"anon";

revoke references on table "public"."approved_changes_audit"
from
	"anon";

revoke
select
	on table "public"."approved_changes_audit"
from
	"anon";

revoke trigger on table "public"."approved_changes_audit"
from
	"anon";

revoke
truncate on table "public"."approved_changes_audit"
from
	"anon";

revoke
update on table "public"."approved_changes_audit"
from
	"anon";

revoke delete on table "public"."approved_changes_audit"
from
	"authenticated";

revoke insert on table "public"."approved_changes_audit"
from
	"authenticated";

revoke references on table "public"."approved_changes_audit"
from
	"authenticated";

revoke
select
	on table "public"."approved_changes_audit"
from
	"authenticated";

revoke trigger on table "public"."approved_changes_audit"
from
	"authenticated";

revoke
truncate on table "public"."approved_changes_audit"
from
	"authenticated";

revoke
update on table "public"."approved_changes_audit"
from
	"authenticated";

revoke delete on table "public"."approved_changes_audit"
from
	"service_role";

revoke insert on table "public"."approved_changes_audit"
from
	"service_role";

revoke references on table "public"."approved_changes_audit"
from
	"service_role";

revoke
select
	on table "public"."approved_changes_audit"
from
	"service_role";

revoke trigger on table "public"."approved_changes_audit"
from
	"service_role";

revoke
truncate on table "public"."approved_changes_audit"
from
	"service_role";

revoke
update on table "public"."approved_changes_audit"
from
	"service_role";

alter table "public"."approved_changes"
drop constraint "approved_changes_approved_by_user_id_fkey";

alter table "public"."approved_changes"
drop constraint "approved_changes_original_risk_id_fkey";

alter table "public"."approved_changes"
drop constraint "approved_changes_project_id_fkey";

alter table "public"."approved_changes"
drop constraint "approved_changes_risk_owner_user_id_fkey";

alter table "public"."approved_changes"
drop constraint "approved_changes_wbs_library_item_id_fkey";

alter table "public"."approved_changes"
drop constraint "one_owner_type";

alter table "public"."approved_changes_audit"
drop constraint "approved_changes_audit_changed_by_fkey";

alter table "public"."approved_changes_audit"
drop constraint "approved_changes_audit_operation_type_check";

drop function if exists "public"."audit_approved_changes_changes" ();

alter table "public"."approved_changes"
drop constraint "approved_changes_pkey";

alter table "public"."approved_changes_audit"
drop constraint "approved_changes_audit_pkey";

drop index if exists "public"."approved_changes_audit_approved_change_id_idx";

drop index if exists "public"."approved_changes_audit_changed_at_idx";

drop index if exists "public"."approved_changes_audit_changed_by_idx";

drop index if exists "public"."approved_changes_audit_pkey";

drop index if exists "public"."approved_changes_audit_project_id_idx";

drop index if exists "public"."approved_changes_original_risk_id_idx";

drop index if exists "public"."approved_changes_pkey";

drop index if exists "public"."approved_changes_project_id_idx";

drop index if exists "public"."risk_register_status_idx";

drop table "public"."approved_changes";

drop table "public"."approved_changes_audit";

alter table "public"."risk_register"
drop column "potential_impact";

alter table "public"."risk_register"
drop column "probability";

alter table "public"."risk_register"
add column "construction_total" numeric(15, 2);

alter table "public"."risk_register"
add column "design_fees" numeric(15, 2);

alter table "public"."risk_register"
alter column "status"
set default 'risk'::risk_status;

alter table "public"."risk_register"
alter column "status"
set data type risk_status using "status"::risk_status;

CREATE INDEX risk_register_status_idx ON public.risk_register USING btree (status);
