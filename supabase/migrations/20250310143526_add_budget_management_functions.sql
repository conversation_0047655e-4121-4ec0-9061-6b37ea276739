-- Budget Management Functions
-- These functions handle budget item management, snapshots, and reversion
-- Function to calculate the cost of a budget line item
CREATE OR REPLACE FUNCTION public.calculate_unit_item_cost (
	p_material_rate NUMERIC,
	p_labor_rate NUMERIC,
	p_productivity_per_hour NUMERIC
) RETURNS NUMERIC
SET
	search_path = '' AS $$
DECLARE v_cost NUMERIC;
BEGIN -- Simple calculation for now - can be enhanced later
v_cost := COALESCE(p_material_rate, 0);
-- Add labor costs if applicable
IF p_labor_rate IS NOT NULL
AND p_productivity_per_hour IS NOT NULL
AND p_productivity_per_hour > 0 THEN v_cost := v_cost + COALESCE(p_labor_rate, 0) / COALESCE(p_productivity_per_hour, 1);
END IF;
RETURN v_cost;
END;
$$ LANGUAGE plpgsql;

-- Function to create or update a budget line item
-- Note: A WBS item can have multiple budget line items associated with it
CREATE OR REPLACE FUNCTION public.upsert_budget_line_item (
	p_project_id UUID,
	p_wbs_library_item_id UUID,
	p_quantity NUMERIC,
	-- Optional parameters for the function
	p_unit TEXT DEFAULT NULL,
	p_material_rate NUMERIC default 0,
	p_labor_rate NUMERIC DEFAULT NULL,
	p_productivity_per_hour NUMERIC DEFAULT NULL,
	p_unit_rate_manual_override BOOLEAN DEFAULT FALSE,
	p_unit_rate NUMERIC DEFAULT 0,
	p_factor NUMERIC DEFAULT NULL,
	p_remarks TEXT DEFAULT NULL,
	p_cost_certainty NUMERIC DEFAULT NULL,
	p_design_certainty NUMERIC DEFAULT NULL,
	p_change_reason TEXT DEFAULT NULL,
	p_budget_line_item_id UUID DEFAULT NULL
) RETURNS UUID
SET
	search_path = '' AS $$
DECLARE v_calculated_cost NUMERIC;
v_cost_to_use NUMERIC;
v_exists BOOLEAN;
v_budget_line_item_id UUID;
BEGIN -- Calculate the cost (always calculate regardless of override)
v_calculated_cost := public.calculate_unit_item_cost(
	p_material_rate,
	p_labor_rate,
	p_productivity_per_hour
);
-- Determine which cost to use based on override flag
IF COALESCE(p_unit_rate_manual_override, FALSE) = TRUE THEN -- When manual override is enabled, use the provided value or fall back to calculated
v_cost_to_use := COALESCE(p_unit_rate, v_calculated_cost);
ELSE -- When manual override is disabled or NULL, always use the calculated value
v_cost_to_use := v_calculated_cost;
END IF;
-- Check if we are updating an existing line item by ID
IF p_budget_line_item_id IS NOT NULL THEN -- Check if the budget line item exists
SELECT EXISTS (
		SELECT 1
		FROM public.budget_line_item_current
		WHERE budget_line_item_id = p_budget_line_item_id
	) INTO v_exists;
IF v_exists THEN -- Update existing record by ID (trigger will handle audit logging)
UPDATE public.budget_line_item_current
SET quantity = p_quantity,
	unit = p_unit,
	material_rate = p_material_rate,
	labor_rate = p_labor_rate,
	productivity_per_hour = p_productivity_per_hour,
	unit_rate_manual_override = p_unit_rate_manual_override,
	unit_rate = v_cost_to_use,
	factor = p_factor,
	remarks = p_remarks,
	cost_certainty = p_cost_certainty,
	design_certainty = p_design_certainty,
	updated_at = now()
WHERE budget_line_item_id = p_budget_line_item_id
RETURNING budget_line_item_id INTO v_budget_line_item_id;
RETURN v_budget_line_item_id;
END IF;
END IF;
-- Insert new record
INSERT INTO public.budget_line_item_current (
		project_id,
		wbs_library_item_id,
		quantity,
		unit,
		material_rate,
		labor_rate,
		productivity_per_hour,
		unit_rate_manual_override,
		unit_rate,
		factor,
		remarks,
		cost_certainty,
		design_certainty
	)
VALUES (
		p_project_id,
		p_wbs_library_item_id,
		p_quantity,
		p_unit,
		p_material_rate,
		p_labor_rate,
		p_productivity_per_hour,
		p_unit_rate_manual_override,
		v_cost_to_use,
		p_factor,
		p_remarks,
		p_cost_certainty,
		p_design_certainty
	)
RETURNING budget_line_item_id INTO v_budget_line_item_id;
RETURN v_budget_line_item_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create a budget snapshot for a project stage
CREATE OR REPLACE FUNCTION public.create_budget_snapshot (
	p_project_stage_id UUID,
	p_freeze_reason TEXT DEFAULT NULL
) RETURNS UUID
SET
	search_path = '' AS $$
DECLARE v_project_id UUID;
v_snapshot_id UUID;
v_item RECORD;
BEGIN -- Get the project_id from the stage
SELECT project_id INTO v_project_id
FROM public.project_stage
WHERE project_stage_id = p_project_stage_id;
IF v_project_id IS NULL THEN RAISE EXCEPTION 'Project stage not found';
END IF;
-- Create the snapshot
INSERT INTO public.budget_snapshot (
		project_stage_id,
		freeze_date,
		freeze_reason,
		created_by_user_id
	)
VALUES (
		p_project_stage_id,
		now(),
		p_freeze_reason,
		auth.uid()
	)
RETURNING budget_snapshot_id INTO v_snapshot_id;
-- Copy all current budget line items for this project to the snapshot
FOR v_item IN (
	SELECT *
	FROM public.budget_line_item_current
	WHERE project_id = v_project_id
) LOOP
INSERT INTO public.budget_snapshot_line_item (
		budget_snapshot_id,
		wbs_library_item_id,
		quantity,
		unit,
		material_rate,
		labor_rate,
		productivity_per_hour,
		unit_rate_manual_override,
		unit_rate,
		factor,
		remarks,
		cost_certainty,
		design_certainty
	)
VALUES (
		v_snapshot_id,
		v_item.wbs_library_item_id,
		v_item.quantity,
		v_item.unit,
		v_item.material_rate,
		v_item.labor_rate,
		v_item.productivity_per_hour,
		v_item.unit_rate_manual_override,
		v_item.unit_rate,
		v_item.factor,
		v_item.remarks,
		v_item.cost_certainty,
		v_item.design_certainty
	);
END LOOP;
-- Update the project stage to mark it as completed
UPDATE public.project_stage
SET date_completed = now()
WHERE project_stage_id = p_project_stage_id;
RETURN v_snapshot_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to revert budget to a snapshot state
CREATE OR REPLACE FUNCTION public.revert_to_budget_snapshot (
	p_budget_snapshot_id UUID,
	p_revert_reason TEXT DEFAULT 'Reverted to snapshot'
) RETURNS BOOLEAN
SET
	search_path = '' AS $$
DECLARE v_project_id UUID;
v_item RECORD;
BEGIN -- Get the project_id from the snapshot and stage
SELECT project_id INTO v_project_id
FROM public.project_stage ps
	JOIN public.budget_snapshot bs ON ps.project_stage_id = bs.project_stage_id
WHERE bs.budget_snapshot_id = p_budget_snapshot_id;
IF v_project_id IS NULL THEN RAISE EXCEPTION 'Budget snapshot not found or not linked to a valid project';
END IF;
-- Loop through all snapshot items and update/insert into current budget
FOR v_item IN (
	SELECT *
	FROM public.budget_snapshot_line_item
	WHERE budget_snapshot_id = p_budget_snapshot_id
) LOOP -- Use the upsert function for each item
PERFORM public.upsert_budget_line_item(
	v_project_id,
	v_item.wbs_library_item_id,
	v_item.quantity,
	v_item.unit,
	v_item.material_rate,
	v_item.labor_rate,
	v_item.productivity_per_hour,
	v_item.unit_rate_manual_override,
	v_item.unit_rate,
	v_item.factor,
	v_item.remarks,
	v_item.cost_certainty,
	v_item.design_certainty,
	p_revert_reason,
	NULL -- We want to create new records when reverting, not update existing ones
);
END LOOP;
RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to compare snapshots
CREATE OR REPLACE FUNCTION public.compare_budget_snapshots (p_snapshot_id_1 UUID, p_snapshot_id_2 UUID) RETURNS TABLE (
	wbs_library_item_id UUID,
	snapshot_1_quantity NUMERIC,
	snapshot_1_cost NUMERIC,
	snapshot_2_quantity NUMERIC,
	snapshot_2_cost NUMERIC,
	quantity_diff NUMERIC,
	cost_diff NUMERIC,
	percent_change NUMERIC
)
SET
	search_path = '' AS $$ BEGIN RETURN QUERY
SELECT COALESCE(s1.wbs_library_item_id, s2.wbs_library_item_id) AS wbs_library_item_id,
	s1.quantity AS snapshot_1_quantity,
	s1.unit_rate AS snapshot_1_cost,
	s1.factor AS snapshot_1_factor,
	s2.quantity AS snapshot_2_quantity,
	s2.unit_rate AS snapshot_2_cost,
	s2.factor AS snapshot_2_factor,
	COALESCE(s2.quantity, 0) - COALESCE(s1.quantity, 0) AS quantity_diff,
	COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0) AS cost_diff,
	CASE
		WHEN COALESCE(s1.unit_rate, 0) = 0 THEN CASE
			WHEN COALESCE(s2.unit_rate, 0) = 0 THEN 0
			ELSE NULL -- Can't calculate percent change from zero
		END
		ELSE (
			COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0)
		) / COALESCE(s1.unit_rate, 1) * COALESCE(s1.factor, 1) / COALESCE(s2.factor, 1) * 100
	END AS percent_change
FROM (
		SELECT *
		FROM public.budget_snapshot_line_item
		WHERE budget_snapshot_id = p_snapshot_id_1
	) s1
	FULL OUTER JOIN (
		SELECT *
		FROM public.budget_snapshot_line_item
		WHERE budget_snapshot_id = p_snapshot_id_2
	) s2 ON s1.wbs_library_item_id = s2.wbs_library_item_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate if a stage is ready for completion
-- Checks if all gateway checklist items for a stage have a status of 'Complete'
-- Uses the gateway_checklist_item_status_log table to get the latest status for each item
CREATE OR REPLACE FUNCTION public.is_stage_ready_for_completion (p_project_stage_id UUID) RETURNS BOOLEAN
SET
	search_path = '' AS $$
DECLARE
    v_checklist_count INTEGER;
    v_completed_count INTEGER;
BEGIN
    -- Count total checklist items and completed items
    -- Only consider the most recent status for each item (where latest = true)
    SELECT
        COUNT(*),
        COUNT(*) FILTER (
            WHERE status = 'Complete'
        )
    INTO
        v_checklist_count,
        v_completed_count
    FROM
        public.gateway_checklist_item gci
    JOIN
        public.gateway_checklist_item_status_log gcisl
        ON gci.gateway_checklist_item_id = gcisl.gateway_checklist_item_id
    WHERE
        gci.project_stage_id = p_project_stage_id
        AND gcisl.latest = TRUE;

    -- If there are no checklist items, stage is ready for completion
    IF v_checklist_count = 0 THEN
        RETURN TRUE;
    END IF;

    -- Stage is ready if all checklist items have a status of 'Complete'
    RETURN v_checklist_count = v_completed_count;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error and return false to prevent stage completion
        RAISE WARNING 'Error checking if stage is ready for completion: %', SQLERRM;
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to complete a stage with snapshot
CREATE OR REPLACE FUNCTION public.complete_project_stage (
	p_project_stage_id UUID,
	p_completion_notes TEXT DEFAULT NULL
) RETURNS UUID
SET
	search_path = '' AS $$
DECLARE v_snapshot_id UUID;
v_is_ready BOOLEAN;
BEGIN -- Check if all checklist items are completed
SELECT public.is_stage_ready_for_completion(p_project_stage_id) INTO v_is_ready;
IF NOT v_is_ready THEN RAISE EXCEPTION 'Cannot complete stage: not all checklist items are completed';
END IF;
-- Create a budget snapshot
SELECT public.create_budget_snapshot(p_project_stage_id, p_completion_notes) INTO v_snapshot_id;
RETURN v_snapshot_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant access to service_role and authenticated users
GRANT
EXECUTE ON FUNCTION public.calculate_unit_item_cost TO service_role,
authenticated;

GRANT
EXECUTE ON FUNCTION public.upsert_budget_line_item TO service_role,
authenticated;

GRANT
EXECUTE ON FUNCTION public.create_budget_snapshot TO service_role,
authenticated;

GRANT
EXECUTE ON FUNCTION public.revert_to_budget_snapshot TO service_role,
authenticated;

GRANT
EXECUTE ON FUNCTION public.compare_budget_snapshots TO service_role,
authenticated;

GRANT
EXECUTE ON FUNCTION public.is_stage_ready_for_completion TO service_role,
authenticated;

GRANT
EXECUTE ON FUNCTION public.complete_project_stage TO service_role,
authenticated;
