set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.is_construction_stage (project_id_param uuid) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_construction_stage_id UUID;
	v_current_stage_id UUID;
BEGIN
	-- Check if user can access this project
	IF NOT public.can_access_project(project_id_param) THEN
		RAISE EXCEPTION 'Insufficient permissions to access this project';
	END IF;
	
	-- Get the construction stage ID for this project
	SELECT construction_stage_id INTO v_construction_stage_id
	FROM public.project
	WHERE project_id = project_id_param;
	
	-- If no construction stage is set, return false
	IF v_construction_stage_id IS NULL THEN
		RETURN FALSE;
	END IF;
	
	-- Find the first incomplete stage (ordered by stage_order)
	-- If this returns NULL, then all stages are completed (no current stage)
	SELECT project_stage_id INTO v_current_stage_id
	FROM public.project_stage
	WHERE project_id = project_id_param
	AND date_completed IS NULL
	ORDER BY stage_order
	LIMIT 1;
	
	-- If no incomplete stages found, all stages are completed (no current stage)
	IF v_current_stage_id IS NULL THEN
		RETURN FALSE;
	END IF;
	
	-- Return true if current stage is the construction stage
	RETURN v_current_stage_id = v_construction_stage_id;
END;
$function$;
