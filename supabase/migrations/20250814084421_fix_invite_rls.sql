drop policy "Admins and invitees can read invites" on "public"."invite";

create policy "Admins and invitees can read invites" on "public"."invite" as permissive for
select
	to authenticated using (
		(
			(
				(
					resource_type = 'organization'::invite_resource_type
				)
				AND current_user_has_entity_role (
					'organization'::entity_type,
					resource_id,
					'admin'::membership_role
				)
			)
			OR (
				(resource_type = 'client'::invite_resource_type)
				AND current_user_has_entity_role (
					'client'::entity_type,
					resource_id,
					'admin'::membership_role
				)
			)
			OR (
				(resource_type = 'project'::invite_resource_type)
				AND can_modify_project (resource_id)
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						profile p
					WHERE
						(p.email = invite.invitee_email)
				)
			)
		)
	);
