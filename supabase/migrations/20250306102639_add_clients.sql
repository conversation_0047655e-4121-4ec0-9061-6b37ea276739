-- Client Table
create table public.client (
	client_id uuid not null default gen_random_uuid() primary key,
	name text not null,
	description text,
	internal_url text,
	-- internal_url_description: Teams, etc.
	internal_url_description text,
	client_url text,
	created_by_user_id uuid not null references profile (user_id) on update restrict on delete restrict,
	logo_url text,
	org_id uuid not null references organization (org_id) on update restrict on delete restrict,
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null,
	unique (name, org_id)
);

comment on table public.client is 'Client contains all the clients of an organization';

-- Enable Row Level Security
alter table public.client enable row level security;

-- The service_role is granted full privileges for administrative purposes.
grant
select
,
	insert,
update,
delete,
references,
trigger,
truncate on public.client to service_role;

-- <PERSON>reate trigger on client to call the update function before any update
create trigger update_updated_at before
update on public.client for each row
execute function public.update_updated_at_column ();

-- Setup policies
-- Client can be inserted by authenticated users as long as the org_id matches the user's organization
create policy "Organization members can insert a clients" on public.client for insert to authenticated
with
	check (
		exists (
			select
				1
			from
				public.membership m
			where
				m.entity_type = 'organization'
				and m.entity_id = client.org_id
				and m.user_id = auth.uid ()
		)
		and client.created_by_user_id = auth.uid ()
	);

-- Organization members can update client
create policy "Organization members can update clients" on public.client
for update
	to authenticated using (
		public.current_user_has_entity_access ('organization', client.org_id)
	);

-- Organization admins can delete clients
create policy "Organization admins can delete clients" on public.client for delete to authenticated using (
	public.current_user_has_entity_role ('organization', client.org_id, 'admin')
);

-- -- Create bucket for logos
-- insert into
-- 	storage.buckets (id, name)
-- values
-- 	('client_logos', 'client_logos');
-- -- RLS already enabled on storage.objects table
-- -- alter table storage.objects enable row level security;
-- -- Temporarily commented out due to storage being disabled
-- -- Policy 1: Anyone can upload to client_logos bucket
-- create policy "Anyone can upload client logos" on storage.objects for insert to authenticated
-- with
-- 	check (bucket_id = 'client_logos');
-- -- Policy 2: Organization members can view client logos
-- create policy "Organization members can view client logos" on storage.objects for
-- select
-- 	to authenticated using (
-- 		bucket_id = 'client_logos'
-- 		and exists (
-- 			select
-- 				1
-- 			from
-- 				public.client c
-- 				join public.membership m on m.entity_type = 'organization'
-- 				and m.entity_id = c.org_id
-- 			where
-- 				m.user_id = auth.uid ()
-- 		)
-- 	);
-- -- Policy 3: Object owners or organization admins can update client logos
-- create policy "Object owners or organization admins can update client logos" on storage.objects
-- for update
-- 	to authenticated using (
-- 		bucket_id = 'client_logos'
-- 		and (
-- 			owner_id = (
-- 				select
-- 					auth.uid ()
-- 			)
-- 			or -- Organization admin check
-- 			exists (
-- 				select
-- 					1
-- 				from
-- 					public.client c
-- 					join public.membership m on m.entity_type = 'organization'
-- 					and m.entity_id = c.org_id
-- 				where -- Verify the user is an admin of the organization
-- 					m.role = 'admin'
-- 					and m.user_id = (
-- 						select
-- 							auth.uid ()
-- 					)
-- 			)
-- 		)
-- 	);
-- -- Policy 4: Object owners or organization admins can delete client logos
-- create policy "Object owners or organization admins can delete client logos" on storage.objects for delete to authenticated using (
-- 	bucket_id = 'client_logos'
-- 	and (
-- 		owner_id = (
-- 			select
-- 				auth.uid ()
-- 		)
-- 		or -- Organization admin check
-- 		exists (
-- 			select
-- 				1
-- 			from
-- 				public.client c
-- 				join public.membership m on m.entity_type = 'organization'
-- 				and m.entity_id = c.org_id
-- 			where -- Verify the user is an admin of the organization
-- 				m.role = 'admin'
-- 				and m.user_id = (
-- 					select
-- 						auth.uid ()
-- 				)
-- 		)
-- 	)
-- );
