drop policy "Editors can delete items for active stage version" on "public"."budget_version_item";

drop policy "Editors can insert items for active stage version" on "public"."budget_version_item";

drop policy "Editors can update items for active stage version" on "public"."budget_version_item";

create table "public"."budget_import_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default now(),
	"old_values" jsonb,
	"new_values" jsonb,
	"budget_import_id" uuid,
	"project_id" uuid,
	"source_filename" text,
	"source_hash" text,
	"pre_version_id" uuid,
	"new_version_id" uuid,
	"is_undone" boolean,
	"undone_at" timestamp with time zone,
	"undone_by_user_id" uuid,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone
);

alter table "public"."budget_import_audit" enable row level security;

create table "public"."budget_version_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default now(),
	"old_values" jsonb,
	"new_values" jsonb,
	"budget_version_id" uuid,
	"project_id" uuid,
	"label" text,
	"kind" budget_version_kind,
	"stage_id" uuid,
	"prev_version_id" uuid,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."budget_version_audit" enable row level security;

create table "public"."budget_version_item_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default now(),
	"budget_version_item_id" uuid,
	"budget_version_id" uuid,
	"wbs_library_item_id" uuid,
	"project_id" uuid,
	"notes" jsonb
);

alter table "public"."budget_version_item_audit" enable row level security;

CREATE INDEX budget_import_audit_budget_import_id_idx ON public.budget_import_audit USING btree (budget_import_id);

CREATE INDEX budget_import_audit_changed_at_idx ON public.budget_import_audit USING btree (changed_at);

CREATE UNIQUE INDEX budget_import_audit_pkey ON public.budget_import_audit USING btree (audit_id);

CREATE INDEX budget_import_audit_project_id_idx ON public.budget_import_audit USING btree (project_id);

CREATE INDEX budget_version_audit_budget_version_id_idx ON public.budget_version_audit USING btree (budget_version_id);

CREATE INDEX budget_version_audit_changed_at_idx ON public.budget_version_audit USING btree (changed_at);

CREATE UNIQUE INDEX budget_version_audit_pkey ON public.budget_version_audit USING btree (audit_id);

CREATE INDEX budget_version_audit_project_id_idx ON public.budget_version_audit USING btree (project_id);

CREATE INDEX budget_version_item_audit_budget_version_id_idx ON public.budget_version_item_audit USING btree (budget_version_id);

CREATE INDEX budget_version_item_audit_budget_version_item_id_idx ON public.budget_version_item_audit USING btree (budget_version_item_id);

CREATE INDEX budget_version_item_audit_changed_at_idx ON public.budget_version_item_audit USING btree (changed_at);

CREATE UNIQUE INDEX budget_version_item_audit_pkey ON public.budget_version_item_audit USING btree (audit_id);

CREATE INDEX budget_version_item_audit_project_id_idx ON public.budget_version_item_audit USING btree (project_id);

alter table "public"."budget_import_audit"
add constraint "budget_import_audit_pkey" PRIMARY KEY using index "budget_import_audit_pkey";

alter table "public"."budget_version_audit"
add constraint "budget_version_audit_pkey" PRIMARY KEY using index "budget_version_audit_pkey";

alter table "public"."budget_version_item_audit"
add constraint "budget_version_item_audit_pkey" PRIMARY KEY using index "budget_version_item_audit_pkey";

alter table "public"."budget_import_audit"
add constraint "budget_import_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_import_audit" validate constraint "budget_import_audit_changed_by_fkey";

alter table "public"."budget_import_audit"
add constraint "budget_import_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."budget_import_audit" validate constraint "budget_import_audit_operation_type_check";

alter table "public"."budget_version_audit"
add constraint "budget_version_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_version_audit" validate constraint "budget_version_audit_changed_by_fkey";

alter table "public"."budget_version_audit"
add constraint "budget_version_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."budget_version_audit" validate constraint "budget_version_audit_operation_type_check";

alter table "public"."budget_version_item_audit"
add constraint "budget_version_item_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_version_item_audit" validate constraint "budget_version_item_audit_changed_by_fkey";

alter table "public"."budget_version_item_audit"
add constraint "budget_version_item_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."budget_version_item_audit" validate constraint "budget_version_item_audit_operation_type_check";

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.audit_budget_import_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.budget_import_audit (
            operation_type, changed_by, changed_at, old_values,
            budget_import_id, project_id, source_filename, source_hash, pre_version_id, new_version_id,
            is_undone, undone_at, undone_by_user_id, created_by_user_id, created_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.budget_import_id, OLD.project_id, OLD.source_filename, OLD.source_hash, OLD.pre_version_id, OLD.new_version_id,
            OLD.is_undone, OLD.undone_at, OLD.undone_by_user_id, OLD.created_by_user_id, OLD.created_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.budget_import_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            budget_import_id, project_id, source_filename, source_hash, pre_version_id, new_version_id,
            is_undone, undone_at, undone_by_user_id, created_by_user_id, created_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.budget_import_id, NEW.project_id, NEW.source_filename, NEW.source_hash, NEW.pre_version_id, NEW.new_version_id,
            NEW.is_undone, NEW.undone_at, NEW.undone_by_user_id, NEW.created_by_user_id, NEW.created_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.budget_import_audit (
            operation_type, changed_by, changed_at, new_values,
            budget_import_id, project_id, source_filename, source_hash, pre_version_id, new_version_id,
            is_undone, undone_at, undone_by_user_id, created_by_user_id, created_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.budget_import_id, NEW.project_id, NEW.source_filename, NEW.source_hash, NEW.pre_version_id, NEW.new_version_id,
            NEW.is_undone, NEW.undone_at, NEW.undone_by_user_id, NEW.created_by_user_id, NEW.created_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_budget_version_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.budget_version_audit (
            operation_type, changed_by, changed_at, old_values,
            budget_version_id, project_id, label, kind, stage_id, prev_version_id,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.budget_version_id, OLD.project_id, OLD.label, OLD.kind, OLD.stage_id, OLD.prev_version_id,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.budget_version_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            budget_version_id, project_id, label, kind, stage_id, prev_version_id,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.budget_version_id, NEW.project_id, NEW.label, NEW.kind, NEW.stage_id, NEW.prev_version_id,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.budget_version_audit (
            operation_type, changed_by, changed_at, new_values,
            budget_version_id, project_id, label, kind, stage_id, prev_version_id,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.budget_version_id, NEW.project_id, NEW.label, NEW.kind, NEW.stage_id, NEW.prev_version_id,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_budget_version_item_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
    v_project_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    -- Get project_id from budget_version for easier querying
    IF TG_OP = 'DELETE' THEN
        SELECT v.project_id INTO v_project_id
        FROM public.budget_version v
        WHERE v.budget_version_id = OLD.budget_version_id;
        
        INSERT INTO public.budget_version_item_audit (
            operation_type, changed_by, changed_at,
            budget_version_item_id, budget_version_id, wbs_library_item_id, project_id
        ) VALUES (
            'DELETE', v_user_id, NOW(),
            OLD.budget_version_item_id, OLD.budget_version_id, OLD.wbs_library_item_id, v_project_id
        );
        RETURN OLD;
    ELSE
        SELECT v.project_id INTO v_project_id
        FROM public.budget_version v
        WHERE v.budget_version_id = NEW.budget_version_id;
        
        IF TG_OP = 'UPDATE' THEN
            INSERT INTO public.budget_version_item_audit (
                operation_type, changed_by, changed_at,
                budget_version_item_id, budget_version_id, wbs_library_item_id, project_id
            ) VALUES (
                'UPDATE', v_user_id, NOW(),
                NEW.budget_version_item_id, NEW.budget_version_id, NEW.wbs_library_item_id, v_project_id
            );
        ELSIF TG_OP = 'INSERT' THEN
            INSERT INTO public.budget_version_item_audit (
                operation_type, changed_by, changed_at,
                budget_version_item_id, budget_version_id, wbs_library_item_id, project_id
            ) VALUES (
                'INSERT', v_user_id, NOW(),
                NEW.budget_version_item_id, NEW.budget_version_id, NEW.wbs_library_item_id, v_project_id
            );
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.apply_budget_import (
	p_project_id uuid,
	p_source_filename text,
	p_items jsonb,
	p_source_hash text DEFAULT NULL::text,
	p_notes text DEFAULT NULL::text
) RETURNS jsonb LANGUAGE plpgsql SECURITY INVOKER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id uuid;
    v_wbs_library_id uuid;
    v_client_id uuid;
    v_active_version_id uuid;
    v_new_version_id uuid;
    v_import_id uuid;
    v_item jsonb;
    v_wbs_code text;
    v_description text;
    v_quantity numeric;
    v_unit text;
    v_material_rate numeric;
    v_factor numeric;
    v_labor_rate numeric;
    v_productivity_per_hour numeric;
    v_unit_rate numeric;
    v_unit_rate_manual_override boolean;
    v_remarks text;
    v_wbs_item_id uuid;
    v_parent_code text;
    v_parent_item_id uuid;
    v_level integer;
    v_in_level_code text;
    v_inserted_count integer := 0;
    v_wbs_created_count integer := 0;
    v_start timestamptz;
    v_duration_ms numeric;
    v_map jsonb := '{}'::jsonb;
    v_existing_version_id uuid;
BEGIN
    -- Ensure authenticated user
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Permission check
    IF NOT public.can_modify_project(p_project_id) THEN
        RAISE EXCEPTION 'Forbidden: insufficient permissions to modify project %', p_project_id;
    END IF;

    v_start := clock_timestamp();

    -- Project context
    SELECT wbs_library_id, client_id, active_budget_version_id
      INTO v_wbs_library_id, v_client_id, v_active_version_id
    FROM public.project
    WHERE project_id = p_project_id;

    IF v_wbs_library_id IS NULL THEN
        RAISE EXCEPTION 'Project % not found or missing wbs_library_id', p_project_id;
    END IF;

    -- Idempotency: if source_hash is provided and exists, reuse that version
    IF p_source_hash IS NOT NULL THEN
        SELECT new_version_id INTO v_existing_version_id
        FROM public.budget_import
        WHERE project_id = p_project_id
          AND source_hash = p_source_hash
        LIMIT 1;

        IF v_existing_version_id IS NOT NULL THEN
            RETURN jsonb_build_object(
                'reused', true,
                'new_version_id', v_existing_version_id,
                'inserted_count', NULL,
                'wbs_created_count', NULL,
                'duration_ms', NULL
            );
        END IF;
    END IF;

    -- Create empty version for import
    INSERT INTO public.budget_version (
        project_id, label, kind, stage_id, prev_version_id, created_by_user_id
    ) VALUES (
        p_project_id,
        COALESCE(p_source_filename, 'Import') || ' ' || to_char(timezone('utc', now()), 'YYYY-MM-DD HH24:MI:SS'),
        'import'::public.budget_version_kind,
        NULL,
        v_active_version_id,
        v_user_id
    ) RETURNING budget_version_id INTO v_new_version_id;

    -- Walk items and ensure WBS exists, then insert into version items
    FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
    LOOP
        v_wbs_code := v_item->>'code';
        v_description := v_item->>'description';
        v_quantity := (v_item->>'quantity')::numeric;
        v_unit := v_item->>'unit';
        v_material_rate := (v_item->>'material_rate')::numeric;
        v_factor := CASE WHEN v_item->>'factor' IS NOT NULL THEN (v_item->>'factor')::numeric ELSE NULL END;
        v_labor_rate := CASE WHEN v_item->>'labor_rate' IS NOT NULL THEN (v_item->>'labor_rate')::numeric ELSE NULL END;
        v_productivity_per_hour := CASE WHEN v_item->>'productivity_per_hour' IS NOT NULL THEN (v_item->>'productivity_per_hour')::numeric ELSE NULL END;
        v_unit_rate := CASE WHEN v_item->>'unit_rate' IS NOT NULL THEN (v_item->>'unit_rate')::numeric ELSE NULL END;
        v_unit_rate_manual_override := COALESCE((v_item->>'unit_rate_manual_override')::boolean, false);
        v_remarks := v_item->>'remarks';

        IF v_wbs_code IS NULL OR v_wbs_code = '' THEN
            RAISE EXCEPTION 'WBS code is required for all items';
        END IF;
        IF v_description IS NULL OR v_description = '' THEN
            RAISE EXCEPTION 'Description is required for all items';
        END IF;

        -- Lookup or create WBS item
        v_wbs_item_id := NULL;
        IF v_map ? v_wbs_code THEN
            v_wbs_item_id := (v_map->>v_wbs_code)::uuid;
        ELSE
            SELECT wli.wbs_library_item_id INTO v_wbs_item_id
            FROM public.wbs_library_item wli
            WHERE wli.code = v_wbs_code
              AND wli.wbs_library_id = v_wbs_library_id
              AND wli.project_id = p_project_id
            LIMIT 1;
        END IF;

        IF v_wbs_item_id IS NULL THEN
            -- Parse hierarchy to find parent
            WITH parts AS (
                SELECT string_to_array(v_wbs_code, '.') AS a
            )
            SELECT array_length(a, 1) AS lvl,
                   a[array_length(a,1)] AS in_level,
                   CASE WHEN array_length(a,1) > 1 THEN array_to_string(a[1:array_length(a,1)-1], '.') ELSE NULL END AS parent_code
            INTO v_level, v_in_level_code, v_parent_code
            FROM parts;

            v_parent_item_id := NULL;
            IF v_parent_code IS NOT NULL THEN
                IF v_map ? v_parent_code THEN
                    v_parent_item_id := (v_map->>v_parent_code)::uuid;
                ELSE
                    SELECT wli.wbs_library_item_id INTO v_parent_item_id
                    FROM public.wbs_library_item wli
                    WHERE wli.code = v_parent_code
                      AND wli.wbs_library_id = v_wbs_library_id
                      AND (wli.project_id = p_project_id OR wli.client_id = v_client_id OR wli.item_type = 'Standard')
                    LIMIT 1;

                    IF v_parent_item_id IS NULL THEN
                        -- Create a parent placeholder (non-recursive for grandparents)
                        WITH parts2 AS (
                            SELECT string_to_array(v_parent_code, '.') AS a
                        )
                        INSERT INTO public.wbs_library_item (
                            wbs_library_id, level, in_level_code, parent_item_id,
                            code, description, cost_scope, item_type, client_id, project_id
                        )
                        SELECT v_wbs_library_id,
                               v_level - 1,
                               a[array_length(a,1)],
                               NULL,
                               v_parent_code,
                               'Auto-created parent for ' || v_parent_code,
                               NULL,
                               'Custom',
                               v_client_id,
                               p_project_id
                        FROM parts2
                        RETURNING wbs_library_item_id INTO v_parent_item_id;
                        v_wbs_created_count := v_wbs_created_count + 1;
                    END IF;
                    v_map := v_map || jsonb_build_object(v_parent_code, v_parent_item_id);
                END IF;
            END IF;

            INSERT INTO public.wbs_library_item (
                wbs_library_id, level, in_level_code, parent_item_id,
                code, description, cost_scope, item_type, client_id, project_id
            ) VALUES (
                v_wbs_library_id,
                v_level,
                v_in_level_code,
                v_parent_item_id,
                v_wbs_code,
                v_description,
                v_description,
                'Custom',
                v_client_id,
                p_project_id
            ) RETURNING wbs_library_item_id INTO v_wbs_item_id;

            v_wbs_created_count := v_wbs_created_count + 1;
        END IF;

        v_map := v_map || jsonb_build_object(v_wbs_code, v_wbs_item_id);

        -- Calculate unit_rate if not manual override
        IF COALESCE(v_unit_rate_manual_override, false) THEN
            -- keep provided v_unit_rate (may be NULL)
        ELSE
            v_unit_rate := public.calculate_unit_item_cost(v_material_rate, v_labor_rate, v_productivity_per_hour);
        END IF;

        INSERT INTO public.budget_version_item (
            budget_version_id,
            wbs_library_item_id,
            quantity,
            unit,
            material_rate,
            labor_rate,
            productivity_per_hour,
            unit_rate_manual_override,
            unit_rate,
            factor,
            remarks,
            cost_certainty,
            design_certainty
        ) VALUES (
            v_new_version_id,
            v_wbs_item_id,
            v_quantity,
            v_unit,
            v_material_rate,
            v_labor_rate,
            v_productivity_per_hour,
            COALESCE(v_unit_rate_manual_override, false),
            COALESCE(v_unit_rate, 0),
            v_factor,
            v_remarks,
            NULL,
            NULL
        );

        v_inserted_count := v_inserted_count + 1;
    END LOOP;

    -- Record import
    INSERT INTO public.budget_import (
        project_id, source_filename, source_hash, pre_version_id, new_version_id,
        created_by_user_id
    ) VALUES (
        p_project_id,
        p_source_filename,
        p_source_hash,
        v_active_version_id,
        v_new_version_id,
        v_user_id
    ) RETURNING budget_import_id INTO v_import_id;

    v_duration_ms := EXTRACT(EPOCH FROM (clock_timestamp() - v_start)) * 1000;

    RETURN jsonb_build_object(
        'reused', false,
        'budget_import_id', v_import_id,
        'new_version_id', v_new_version_id,
        'pre_version_id', v_active_version_id,
        'inserted_count', v_inserted_count,
        'wbs_created_count', v_wbs_created_count,
        'duration_ms', v_duration_ms
    );
END;
$function$;

CREATE OR REPLACE FUNCTION public.create_budget_version (
	p_project_id uuid,
	p_label text DEFAULT NULL::text,
	p_kind budget_version_kind DEFAULT 'manual'::budget_version_kind,
	p_stage_id uuid DEFAULT NULL::uuid,
	p_prev_version_id uuid DEFAULT NULL::uuid
) RETURNS uuid LANGUAGE plpgsql SECURITY INVOKER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id uuid;
    v_prev_version_id uuid;
    v_new_version_id uuid;
    v_active_version_id uuid;
BEGIN
    -- Ensure authenticated user
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Permissions: must be able to modify the project
    IF NOT public.can_modify_project(p_project_id) THEN
        RAISE EXCEPTION 'Forbidden: insufficient permissions to modify project %', p_project_id;
    END IF;

    -- Resolve previous version: default to current active version if not provided
    SELECT active_budget_version_id INTO v_active_version_id
    FROM public.project WHERE project_id = p_project_id;

    v_prev_version_id := COALESCE(p_prev_version_id, v_active_version_id);

    -- Create the version row
    INSERT INTO public.budget_version (
        project_id,
        label,
        kind,
        stage_id,
        prev_version_id,
        created_by_user_id
    ) VALUES (
        p_project_id,
        p_label,
        p_kind,
        CASE WHEN p_kind = 'stage'::public.budget_version_kind THEN p_stage_id ELSE NULL END,
        v_prev_version_id,
        v_user_id
    ) RETURNING budget_version_id INTO v_new_version_id;

    -- Clone items from previous version by default for non-import kinds
    IF v_prev_version_id IS NOT NULL AND p_kind <> 'import'::public.budget_version_kind THEN
        INSERT INTO public.budget_version_item (
            budget_version_id,
            wbs_library_item_id,
            quantity,
            unit,
            material_rate,
            labor_rate,
            productivity_per_hour,
            unit_rate_manual_override,
            unit_rate,
            factor,
            remarks,
            cost_certainty,
            design_certainty
        )
        SELECT
            v_new_version_id,
            i.wbs_library_item_id,
            i.quantity,
            i.unit,
            i.material_rate,
            i.labor_rate,
            i.productivity_per_hour,
            i.unit_rate_manual_override,
            i.unit_rate,
            i.factor,
            i.remarks,
            i.cost_certainty,
            i.design_certainty
        FROM public.budget_version_item i
        WHERE i.budget_version_id = v_prev_version_id;
    END IF;

    RETURN v_new_version_id;
END;
$function$;

grant delete on table "public"."budget_import_audit" to "anon";

grant insert on table "public"."budget_import_audit" to "anon";

grant references on table "public"."budget_import_audit" to "anon";

grant
select
	on table "public"."budget_import_audit" to "anon";

grant trigger on table "public"."budget_import_audit" to "anon";

grant
truncate on table "public"."budget_import_audit" to "anon";

grant
update on table "public"."budget_import_audit" to "anon";

grant delete on table "public"."budget_import_audit" to "authenticated";

grant insert on table "public"."budget_import_audit" to "authenticated";

grant references on table "public"."budget_import_audit" to "authenticated";

grant
select
	on table "public"."budget_import_audit" to "authenticated";

grant trigger on table "public"."budget_import_audit" to "authenticated";

grant
truncate on table "public"."budget_import_audit" to "authenticated";

grant
update on table "public"."budget_import_audit" to "authenticated";

grant delete on table "public"."budget_import_audit" to "service_role";

grant insert on table "public"."budget_import_audit" to "service_role";

grant references on table "public"."budget_import_audit" to "service_role";

grant
select
	on table "public"."budget_import_audit" to "service_role";

grant trigger on table "public"."budget_import_audit" to "service_role";

grant
truncate on table "public"."budget_import_audit" to "service_role";

grant
update on table "public"."budget_import_audit" to "service_role";

grant delete on table "public"."budget_version_audit" to "anon";

grant insert on table "public"."budget_version_audit" to "anon";

grant references on table "public"."budget_version_audit" to "anon";

grant
select
	on table "public"."budget_version_audit" to "anon";

grant trigger on table "public"."budget_version_audit" to "anon";

grant
truncate on table "public"."budget_version_audit" to "anon";

grant
update on table "public"."budget_version_audit" to "anon";

grant delete on table "public"."budget_version_audit" to "authenticated";

grant insert on table "public"."budget_version_audit" to "authenticated";

grant references on table "public"."budget_version_audit" to "authenticated";

grant
select
	on table "public"."budget_version_audit" to "authenticated";

grant trigger on table "public"."budget_version_audit" to "authenticated";

grant
truncate on table "public"."budget_version_audit" to "authenticated";

grant
update on table "public"."budget_version_audit" to "authenticated";

grant delete on table "public"."budget_version_audit" to "service_role";

grant insert on table "public"."budget_version_audit" to "service_role";

grant references on table "public"."budget_version_audit" to "service_role";

grant
select
	on table "public"."budget_version_audit" to "service_role";

grant trigger on table "public"."budget_version_audit" to "service_role";

grant
truncate on table "public"."budget_version_audit" to "service_role";

grant
update on table "public"."budget_version_audit" to "service_role";

grant delete on table "public"."budget_version_item_audit" to "anon";

grant insert on table "public"."budget_version_item_audit" to "anon";

grant references on table "public"."budget_version_item_audit" to "anon";

grant
select
	on table "public"."budget_version_item_audit" to "anon";

grant trigger on table "public"."budget_version_item_audit" to "anon";

grant
truncate on table "public"."budget_version_item_audit" to "anon";

grant
update on table "public"."budget_version_item_audit" to "anon";

grant delete on table "public"."budget_version_item_audit" to "authenticated";

grant insert on table "public"."budget_version_item_audit" to "authenticated";

grant references on table "public"."budget_version_item_audit" to "authenticated";

grant
select
	on table "public"."budget_version_item_audit" to "authenticated";

grant trigger on table "public"."budget_version_item_audit" to "authenticated";

grant
truncate on table "public"."budget_version_item_audit" to "authenticated";

grant
update on table "public"."budget_version_item_audit" to "authenticated";

grant delete on table "public"."budget_version_item_audit" to "service_role";

grant insert on table "public"."budget_version_item_audit" to "service_role";

grant references on table "public"."budget_version_item_audit" to "service_role";

grant
select
	on table "public"."budget_version_item_audit" to "service_role";

grant trigger on table "public"."budget_version_item_audit" to "service_role";

grant
truncate on table "public"."budget_version_item_audit" to "service_role";

grant
update on table "public"."budget_version_item_audit" to "service_role";

create policy "System can insert budget import audit records" on "public"."budget_import_audit" as permissive for insert to service_role
with
	check (true);

create policy "Users can view budget import audit for accessible projects" on "public"."budget_import_audit" as permissive for
select
	to authenticated using (can_access_project (project_id));

create policy "System can insert budget version audit records" on "public"."budget_version_audit" as permissive for insert to service_role
with
	check (true);

create policy "Users can view budget version audit for accessible projects" on "public"."budget_version_audit" as permissive for
select
	to authenticated using (can_access_project (project_id));

create policy "Editors can delete items for accessible versions" on "public"."budget_version_item" as permissive for delete to authenticated using (
	(
		EXISTS (
			SELECT
				1
			FROM
				(
					budget_version v
					LEFT JOIN project p ON ((p.project_id = v.project_id))
				)
			WHERE
				(
					(
						v.budget_version_id = budget_version_item.budget_version_id
					)
					AND can_modify_project (v.project_id)
					AND (
						(
							(v.kind = 'stage'::budget_version_kind)
							AND (p.active_budget_version_id = v.budget_version_id)
						)
						OR (v.kind = 'import'::budget_version_kind)
						OR (v.kind = 'manual'::budget_version_kind)
					)
				)
		)
	)
);

create policy "Editors can insert items for accessible versions" on "public"."budget_version_item" as permissive for insert to authenticated
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						budget_version v
						LEFT JOIN project p ON ((p.project_id = v.project_id))
					)
				WHERE
					(
						(
							v.budget_version_id = budget_version_item.budget_version_id
						)
						AND can_modify_project (v.project_id)
						AND (
							(
								(v.kind = 'stage'::budget_version_kind)
								AND (p.active_budget_version_id = v.budget_version_id)
							)
							OR (v.kind = 'import'::budget_version_kind)
							OR (v.kind = 'manual'::budget_version_kind)
						)
					)
			)
		)
	);

create policy "Editors can update items for accessible versions" on "public"."budget_version_item" as permissive
for update
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						budget_version v
						LEFT JOIN project p ON ((p.project_id = v.project_id))
					)
				WHERE
					(
						(
							v.budget_version_id = budget_version_item.budget_version_id
						)
						AND can_modify_project (v.project_id)
						AND (
							(
								(v.kind = 'stage'::budget_version_kind)
								AND (p.active_budget_version_id = v.budget_version_id)
							)
							OR (v.kind = 'import'::budget_version_kind)
							OR (v.kind = 'manual'::budget_version_kind)
						)
					)
			)
		)
	)
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						budget_version v
						LEFT JOIN project p ON ((p.project_id = v.project_id))
					)
				WHERE
					(
						(
							v.budget_version_id = budget_version_item.budget_version_id
						)
						AND can_modify_project (v.project_id)
						AND (
							(
								(v.kind = 'stage'::budget_version_kind)
								AND (p.active_budget_version_id = v.budget_version_id)
							)
							OR (v.kind = 'import'::budget_version_kind)
							OR (v.kind = 'manual'::budget_version_kind)
						)
					)
			)
		)
	);

create policy "System can insert budget version item audit records" on "public"."budget_version_item_audit" as permissive for insert to service_role
with
	check (true);

create policy "Users can view budget version item audit for accessible project" on "public"."budget_version_item_audit" as permissive for
select
	to authenticated using (can_access_project (project_id));

CREATE TRIGGER audit_budget_import_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.budget_import FOR EACH ROW
EXECUTE FUNCTION audit_budget_import_changes ();

CREATE TRIGGER audit_budget_version_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.budget_version FOR EACH ROW
EXECUTE FUNCTION audit_budget_version_changes ();

CREATE TRIGGER audit_budget_version_item_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.budget_version_item FOR EACH ROW
EXECUTE FUNCTION audit_budget_version_item_changes ();
