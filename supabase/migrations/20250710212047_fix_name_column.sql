set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_accessible_purchase_orders (project_id_param uuid) RETURNS TABLE (
	purchase_order_id uuid,
	po_number text,
	description text,
	po_date date,
	vendor_name text,
	vendor_id uuid,
	original_amount numeric,
	co_amount numeric,
	total_amount numeric,
	created_at timestamp with time zone,
	created_by_name text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return purchase orders for the project with vendor and creator information
	RETURN QUERY
	SELECT
		po.purchase_order_id,
		po.po_number,
		po.description,
		po.po_date,
		v.name AS vendor_name,
		po.vendor_id,
		po.original_amount,
		po.co_amount,
		COALESCE(po.original_amount, 0) + COALESCE(po.co_amount, 0) + COALESCE(po.freight, 0) + COALESCE(po.tax, 0) + COALESCE(po.other, 0) AS total_amount,
		po.created_at,
		p.full_name AS created_by_name
	FROM public.purchase_order po
	LEFT JOIN public.vendor v ON po.vendor_id = v.vendor_id
	LEFT JOIN public.profile p ON po.created_by_user_id = p.user_id
	WHERE po.project_id = project_id_param
	ORDER BY po.po_date DESC, po.po_number;
END;
$function$;
