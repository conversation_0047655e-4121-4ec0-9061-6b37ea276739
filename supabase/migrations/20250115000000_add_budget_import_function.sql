-- Budget Import Function
-- This function handles importing budget data from Excel files
CREATE OR REPLACE FUNCTION public.import_budget_data (p_project_id UUID, p_items JSONB) RETURNS JSONB
SET
	search_path = '' LANGUAGE plpgsql AS $$
DECLARE
    v_item JSONB;
    v_wbs_code TEXT;
    v_description TEXT;
    v_quantity NUMERIC;
    v_unit TEXT;
    v_material_rate NUMERIC;
    v_factor NUMERIC;
    v_labor_rate NUMERIC;
    v_productivity_per_hour NUMERIC;
    v_remarks TEXT;
    
    v_wbs_library_id UUID;
    v_client_id UUID;
    v_wbs_item_id UUID;
    v_parent_code TEXT;
    v_parent_item_id UUID;
    v_level INTEGER;
    v_in_level_code TEXT;
    
    v_inserted_count INTEGER := 0;
    v_wbs_created_count INTEGER := 0;
    v_start_time TIMESTAMP;
    v_duration_ms INTEGER;
    
    v_wbs_map JSONB := '{}';
BEGIN
    v_start_time := clock_timestamp();
    
    -- Get project details
    SELECT p.wbs_library_id, p.client_id
    INTO v_wbs_library_id, v_client_id
    FROM public.project p
    WHERE p.project_id = p_project_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Project not found: %', p_project_id;
    END IF;
    
    -- Check permissions
    IF NOT public.can_modify_project(p_project_id) THEN
        RAISE EXCEPTION 'Insufficient permissions to modify project';
    END IF;
    
    -- Process each item
    FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
    LOOP
        -- Extract values from JSON
        v_wbs_code := v_item->>'code';
        v_description := v_item->>'description';
        v_quantity := (v_item->>'quantity')::NUMERIC;
        v_unit := v_item->>'unit';
        v_material_rate := (v_item->>'material_rate')::NUMERIC;
        v_factor := CASE WHEN v_item->>'factor' IS NOT NULL THEN (v_item->>'factor')::NUMERIC ELSE NULL END;
        v_labor_rate := CASE WHEN v_item->>'labor_rate' IS NOT NULL THEN (v_item->>'labor_rate')::NUMERIC ELSE NULL END;
        v_productivity_per_hour := CASE WHEN v_item->>'productivity_per_hour' IS NOT NULL THEN (v_item->>'productivity_per_hour')::NUMERIC ELSE NULL END;
        v_remarks := v_item->>'remarks';
        
        -- Validate required fields
        IF v_wbs_code IS NULL OR v_wbs_code = '' THEN
            RAISE EXCEPTION 'WBS code is required for all items';
        END IF;
        
        IF v_description IS NULL OR v_description = '' THEN
            RAISE EXCEPTION 'Description is required for all items';
        END IF;
        
        -- Check for duplicate codes in this project
        IF EXISTS (
            SELECT 1 FROM public.wbs_library_item wli
            WHERE wli.code = v_wbs_code 
            AND (wli.project_id = p_project_id)
        ) THEN
            RAISE EXCEPTION 'duplicate_code' USING DETAIL = format('WBS code %s already exists in this project', v_wbs_code);
        END IF;
        
        -- Parse WBS code hierarchy
        WITH wbs_parts AS (
            SELECT string_to_array(v_wbs_code, '.') AS parts
        )
        SELECT
            array_length(parts, 1),
            parts[array_length(parts, 1)],
            CASE
                WHEN array_length(parts, 1) > 1
                THEN array_to_string(parts[1:array_length(parts, 1)-1], '.')
                ELSE NULL
            END
        INTO v_level, v_in_level_code, v_parent_code
        FROM wbs_parts;
        
        -- Find or create parent WBS item if needed
        v_parent_item_id := NULL;
        IF v_parent_code IS NOT NULL THEN
            -- Check if parent exists in our map first
            IF v_wbs_map ? v_parent_code THEN
                v_parent_item_id := (v_wbs_map->>v_parent_code)::UUID;
            ELSE
                -- Look for existing parent
                SELECT wli.wbs_library_item_id
                INTO v_parent_item_id
                FROM public.wbs_library_item wli
                WHERE wli.code = v_parent_code 
                AND wli.wbs_library_id = v_wbs_library_id
                AND (wli.project_id = p_project_id OR wli.client_id = v_client_id OR wli.item_type = 'Standard');
                
                -- Create parent if it doesn't exist
                IF v_parent_item_id IS NULL THEN
                    WITH parent_parts AS (
                        SELECT string_to_array(v_parent_code, '.') AS parts
                    )
                    INSERT INTO public.wbs_library_item (
                        wbs_library_id,
                        level,
                        in_level_code,
                        parent_item_id,
                        code,
                        description,
                        cost_scope,
                        item_type,
                        client_id,
                        project_id
                    )
                    SELECT
                        v_wbs_library_id,
                        v_level - 1,
                        parts[array_length(parts, 1)],
                        NULL, -- We'd need to recursively create grandparents
                        v_parent_code,
                        'Auto-created parent for ' || v_parent_code,
                        NULL,
                        'Custom',
                        v_client_id,
                        p_project_id
                    FROM parent_parts
                    RETURNING wbs_library_item_id INTO v_parent_item_id;
                    
                    v_wbs_created_count := v_wbs_created_count + 1;
                END IF;
                
                -- Add to map
                v_wbs_map := v_wbs_map || jsonb_build_object(v_parent_code, v_parent_item_id);
            END IF;
        END IF;
        
        -- Create WBS library item
        INSERT INTO public.wbs_library_item (
            wbs_library_id,
            level,
            in_level_code,
            parent_item_id,
            code,
            description,
            cost_scope,
            item_type,
            client_id,
            project_id
        ) VALUES (
            v_wbs_library_id,
            v_level,
            v_in_level_code,
            v_parent_item_id,
            v_wbs_code,
            v_description,
            v_description, -- Use description as cost_scope for imported items
            'Custom',
            v_client_id,
            p_project_id
        ) RETURNING wbs_library_item_id INTO v_wbs_item_id;
        
        v_wbs_created_count := v_wbs_created_count + 1;
        
        -- Add to map
        v_wbs_map := v_wbs_map || jsonb_build_object(v_wbs_code, v_wbs_item_id);
        
        -- Create budget line item using existing RPC
        PERFORM public.upsert_budget_line_item(
            p_project_id := p_project_id,
            p_wbs_library_item_id := v_wbs_item_id,
            p_quantity := v_quantity,
            p_unit := v_unit,
            p_material_rate := v_material_rate,
            p_labor_rate := v_labor_rate,
            p_productivity_per_hour := v_productivity_per_hour,
            p_unit_rate_manual_override := FALSE,
            p_unit_rate := NULL, -- Let the function calculate it
            p_factor := v_factor,
            p_remarks := v_remarks,
            p_cost_certainty := NULL,
            p_design_certainty := NULL,
            p_change_reason := 'Imported from Excel',
            p_budget_line_item_id := NULL
        );
        
        v_inserted_count := v_inserted_count + 1;
    END LOOP;
    
    v_duration_ms := EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000;
    
    RETURN jsonb_build_object(
        'inserted_count', v_inserted_count,
        'wbs_created_count', v_wbs_created_count,
        'duration_ms', v_duration_ms
    );
    
EXCEPTION
    WHEN OTHERS THEN
        -- Re-raise the exception to rollback the transaction
        RAISE;
END;
$$;

-- Grant execute permission to authenticated users
GRANT
EXECUTE ON FUNCTION public.import_budget_data (UUID, JSONB) TO authenticated;
