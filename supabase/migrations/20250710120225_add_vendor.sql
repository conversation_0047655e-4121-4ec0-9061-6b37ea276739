create table "public"."vendor" (
	"vendor_id" uuid not null default gen_random_uuid(),
	"name" text not null,
	"description" text,
	"org_id" uuid,
	"client_id" uuid,
	"project_id" uuid,
	"contact_name" text,
	"contact_email" text,
	"contact_phone" text,
	"contact_address" text,
	"website" text,
	"vendor_type" text,
	"tax_id" text,
	"payment_terms" text,
	"payment_terms_days" integer,
	"credit_limit" numeric(15, 2),
	"currency" text default 'USD'::text,
	"is_active" boolean default true,
	"certification_info" jsonb,
	"insurance_info" jsonb,
	"additional_data" jsonb,
	"created_by_user_id" uuid not null default auth.uid (),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."vendor" enable row level security;

create table "public"."vendor_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default now(),
	"old_values" jsonb,
	"new_values" jsonb,
	"vendor_id" uuid,
	"name" text,
	"description" text,
	"org_id" uuid,
	"client_id" uuid,
	"project_id" uuid,
	"contact_name" text,
	"contact_email" text,
	"contact_phone" text,
	"contact_address" text,
	"website" text,
	"vendor_type" text,
	"tax_id" text,
	"payment_terms" text,
	"payment_terms_days" integer,
	"credit_limit" numeric(15, 2),
	"currency" text,
	"is_active" boolean,
	"certification_info" jsonb,
	"insurance_info" jsonb,
	"additional_data" jsonb,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."vendor_audit" enable row level security;

CREATE INDEX vendor_audit_changed_at_idx ON public.vendor_audit USING btree (changed_at);

CREATE INDEX vendor_audit_changed_by_idx ON public.vendor_audit USING btree (changed_by);

CREATE INDEX vendor_audit_operation_type_idx ON public.vendor_audit USING btree (operation_type);

CREATE UNIQUE INDEX vendor_audit_pkey ON public.vendor_audit USING btree (audit_id);

CREATE INDEX vendor_audit_vendor_id_idx ON public.vendor_audit USING btree (vendor_id);

CREATE INDEX vendor_client_id_idx ON public.vendor USING btree (client_id)
WHERE
	(client_id IS NOT NULL);

CREATE INDEX vendor_created_by_user_id_idx ON public.vendor USING btree (created_by_user_id);

CREATE INDEX vendor_is_active_idx ON public.vendor USING btree (is_active);

CREATE INDEX vendor_name_idx ON public.vendor USING btree (name);

CREATE UNIQUE INDEX vendor_name_scope_unique ON public.vendor USING btree (name, org_id, client_id, project_id);

CREATE INDEX vendor_org_id_idx ON public.vendor USING btree (org_id)
WHERE
	(org_id IS NOT NULL);

CREATE UNIQUE INDEX vendor_pkey ON public.vendor USING btree (vendor_id);

CREATE INDEX vendor_project_id_idx ON public.vendor USING btree (project_id)
WHERE
	(project_id IS NOT NULL);

CREATE INDEX vendor_vendor_type_idx ON public.vendor USING btree (vendor_type)
WHERE
	(vendor_type IS NOT NULL);

alter table "public"."vendor"
add constraint "vendor_pkey" PRIMARY KEY using index "vendor_pkey";

alter table "public"."vendor_audit"
add constraint "vendor_audit_pkey" PRIMARY KEY using index "vendor_audit_pkey";

alter table "public"."vendor"
add constraint "vendor_client_id_fkey" FOREIGN KEY (client_id) REFERENCES client (client_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."vendor" validate constraint "vendor_client_id_fkey";

alter table "public"."vendor"
add constraint "vendor_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."vendor" validate constraint "vendor_created_by_user_id_fkey";

alter table "public"."vendor"
add constraint "vendor_name_scope_unique" UNIQUE using index "vendor_name_scope_unique";

alter table "public"."vendor"
add constraint "vendor_org_id_fkey" FOREIGN KEY (org_id) REFERENCES organization (org_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."vendor" validate constraint "vendor_org_id_fkey";

alter table "public"."vendor"
add constraint "vendor_project_id_fkey" FOREIGN KEY (project_id) REFERENCES project (project_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."vendor" validate constraint "vendor_project_id_fkey";

alter table "public"."vendor"
add constraint "vendor_single_scope_check" CHECK (
	(
		(
			(
				((org_id IS NOT NULL))::integer + ((client_id IS NOT NULL))::integer
			) + ((project_id IS NOT NULL))::integer
		) = 1
	)
) not valid;

alter table "public"."vendor" validate constraint "vendor_single_scope_check";

alter table "public"."vendor_audit"
add constraint "vendor_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."vendor_audit" validate constraint "vendor_audit_changed_by_fkey";

alter table "public"."vendor_audit"
add constraint "vendor_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."vendor_audit" validate constraint "vendor_audit_operation_type_check";

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.audit_vendor_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.vendor_audit (
            operation_type, changed_by, changed_at, old_values,
            vendor_id, name, description, org_id, client_id, project_id,
            contact_name, contact_email, contact_phone, contact_address, website,
            vendor_type, tax_id, payment_terms, payment_terms_days, credit_limit,
            currency, is_active, certification_info, insurance_info, additional_data,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.vendor_id, OLD.name, OLD.description, OLD.org_id, OLD.client_id, OLD.project_id,
            OLD.contact_name, OLD.contact_email, OLD.contact_phone, OLD.contact_address, OLD.website,
            OLD.vendor_type, OLD.tax_id, OLD.payment_terms, OLD.payment_terms_days, OLD.credit_limit,
            OLD.currency, OLD.is_active, OLD.certification_info, OLD.insurance_info, OLD.additional_data,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.vendor_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            vendor_id, name, description, org_id, client_id, project_id,
            contact_name, contact_email, contact_phone, contact_address, website,
            vendor_type, tax_id, payment_terms, payment_terms_days, credit_limit,
            currency, is_active, certification_info, insurance_info, additional_data,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.vendor_id, NEW.name, NEW.description, NEW.org_id, NEW.client_id, NEW.project_id,
            NEW.contact_name, NEW.contact_email, NEW.contact_phone, NEW.contact_address, NEW.website,
            NEW.vendor_type, NEW.tax_id, NEW.payment_terms, NEW.payment_terms_days, NEW.credit_limit,
            NEW.currency, NEW.is_active, NEW.certification_info, NEW.insurance_info, NEW.additional_data,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.vendor_audit (
            operation_type, changed_by, changed_at, new_values,
            vendor_id, name, description, org_id, client_id, project_id,
            contact_name, contact_email, contact_phone, contact_address, website,
            vendor_type, tax_id, payment_terms, payment_terms_days, credit_limit,
            currency, is_active, certification_info, insurance_info, additional_data,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.vendor_id, NEW.name, NEW.description, NEW.org_id, NEW.client_id, NEW.project_id,
            NEW.contact_name, NEW.contact_email, NEW.contact_phone, NEW.contact_address, NEW.website,
            NEW.vendor_type, NEW.tax_id, NEW.payment_terms, NEW.payment_terms_days, NEW.credit_limit,
            NEW.currency, NEW.is_active, NEW.certification_info, NEW.insurance_info, NEW.additional_data,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_accessible_vendors (
	user_id_param uuid,
	entity_type_param entity_type,
	entity_id_param uuid
) RETURNS TABLE (
	vendor_id uuid,
	name text,
	description text,
	vendor_type text,
	contact_name text,
	contact_email text,
	contact_phone text,
	is_active boolean,
	access_level text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
	-- Check if user has access to the specified entity
	IF NOT public.has_entity_access(user_id_param, entity_type_param, entity_id_param) THEN
		RETURN;
	END IF;

	-- Return vendors based on hierarchy
	IF entity_type_param = 'project' THEN
		-- For projects, return project-level, client-level, and organization-level vendors
		RETURN QUERY
		SELECT
			v.vendor_id, v.name, v.description, v.vendor_type,
			v.contact_name, v.contact_email, v.contact_phone, v.is_active,
			CASE
				WHEN v.project_id IS NOT NULL THEN 'project'
				WHEN v.client_id IS NOT NULL THEN 'client'
				WHEN v.org_id IS NOT NULL THEN 'organization'
			END as access_level
		FROM public.vendor v
		JOIN public.project p ON p.project_id = entity_id_param
		JOIN public.client c ON p.client_id = c.client_id
		WHERE (
			v.project_id = entity_id_param
			OR v.client_id = p.client_id
			OR v.org_id = c.org_id
		)
		AND v.is_active = true
		ORDER BY
			CASE
				WHEN v.project_id IS NOT NULL THEN 1
				WHEN v.client_id IS NOT NULL THEN 2
				WHEN v.org_id IS NOT NULL THEN 3
			END,
			v.name;

	ELSIF entity_type_param = 'client' THEN
		-- For clients, return client-level and organization-level vendors
		RETURN QUERY
		SELECT
			v.vendor_id, v.name, v.description, v.vendor_type,
			v.contact_name, v.contact_email, v.contact_phone, v.is_active,
			CASE
				WHEN v.client_id IS NOT NULL THEN 'client'
				WHEN v.org_id IS NOT NULL THEN 'organization'
			END as access_level
		FROM public.vendor v
		JOIN public.client c ON c.client_id = entity_id_param
		WHERE (
			v.client_id = entity_id_param
			OR v.org_id = c.org_id
		)
		AND v.is_active = true
		ORDER BY
			CASE
				WHEN v.client_id IS NOT NULL THEN 1
				WHEN v.org_id IS NOT NULL THEN 2
			END,
			v.name;

	ELSIF entity_type_param = 'organization' THEN
		-- For organizations, return only organization-level vendors
		RETURN QUERY
		SELECT
			v.vendor_id, v.name, v.description, v.vendor_type,
			v.contact_name, v.contact_email, v.contact_phone, v.is_active,
			'organization'::text as access_level
		FROM public.vendor v
		WHERE v.org_id = entity_id_param
		AND v.is_active = true
		ORDER BY v.name;
	END IF;
END;
$function$;

CREATE OR REPLACE FUNCTION public.validate_vendor_scope () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
	-- Ensure exactly one scope is set (this is also enforced by check constraint)
	IF (
		(NEW.org_id IS NOT NULL)::integer +
		(NEW.client_id IS NOT NULL)::integer +
		(NEW.project_id IS NOT NULL)::integer
	) != 1 THEN
		RAISE EXCEPTION 'Vendor must be associated with exactly one scope (organization, client, or project)';
	END IF;

	-- Validate hierarchy consistency
	IF NEW.client_id IS NOT NULL THEN
		-- Ensure client exists
		IF NOT EXISTS (SELECT 1 FROM public.client WHERE client_id = NEW.client_id) THEN
			RAISE EXCEPTION 'Client does not exist';
		END IF;
	END IF;

	IF NEW.project_id IS NOT NULL THEN
		-- Ensure project exists
		IF NOT EXISTS (SELECT 1 FROM public.project WHERE project_id = NEW.project_id) THEN
			RAISE EXCEPTION 'Project does not exist';
		END IF;
	END IF;

	RETURN NEW;
END;
$function$;

grant delete on table "public"."vendor" to "authenticated";

grant insert on table "public"."vendor" to "authenticated";

grant references on table "public"."vendor" to "authenticated";

grant
select
	on table "public"."vendor" to "authenticated";

grant trigger on table "public"."vendor" to "authenticated";

grant
update on table "public"."vendor" to "authenticated";

grant delete on table "public"."vendor" to "service_role";

grant insert on table "public"."vendor" to "service_role";

grant references on table "public"."vendor" to "service_role";

grant
select
	on table "public"."vendor" to "service_role";

grant trigger on table "public"."vendor" to "service_role";

grant
truncate on table "public"."vendor" to "service_role";

grant
update on table "public"."vendor" to "service_role";

grant delete on table "public"."vendor_audit" to "authenticated";

grant insert on table "public"."vendor_audit" to "authenticated";

grant references on table "public"."vendor_audit" to "authenticated";

grant
select
	on table "public"."vendor_audit" to "authenticated";

grant trigger on table "public"."vendor_audit" to "authenticated";

grant
update on table "public"."vendor_audit" to "authenticated";

grant delete on table "public"."vendor_audit" to "service_role";

grant insert on table "public"."vendor_audit" to "service_role";

grant references on table "public"."vendor_audit" to "service_role";

grant
select
	on table "public"."vendor_audit" to "service_role";

grant trigger on table "public"."vendor_audit" to "service_role";

grant
truncate on table "public"."vendor_audit" to "service_role";

grant
update on table "public"."vendor_audit" to "service_role";

create policy "Users can create vendors at levels they have admin access to" on "public"."vendor" as permissive for insert to authenticated
with
	check (
		(
			(
				(
					(org_id IS NOT NULL)
					AND current_user_has_entity_role (
						'organization'::entity_type,
						org_id,
						'admin'::membership_role
					)
				)
				OR (
					(client_id IS NOT NULL)
					AND current_user_has_entity_role (
						'client'::entity_type,
						client_id,
						'admin'::membership_role
					)
				)
				OR (
					(project_id IS NOT NULL)
					AND current_user_has_entity_role (
						'project'::entity_type,
						project_id,
						'editor'::membership_role
					)
				)
			)
			AND (created_by_user_id = auth.uid ())
		)
	);

create policy "Users can delete vendors at levels they have admin access to" on "public"."vendor" as permissive for delete to authenticated using (
	(
		(
			(org_id IS NOT NULL)
			AND current_user_has_entity_role (
				'organization'::entity_type,
				org_id,
				'admin'::membership_role
			)
		)
		OR (
			(client_id IS NOT NULL)
			AND current_user_has_entity_role (
				'client'::entity_type,
				client_id,
				'admin'::membership_role
			)
		)
		OR (
			(project_id IS NOT NULL)
			AND current_user_has_entity_role (
				'project'::entity_type,
				project_id,
				'admin'::membership_role
			)
		)
	)
);

create policy "Users can update vendors at levels they have admin access to" on "public"."vendor" as permissive
for update
	to authenticated using (
		(
			(
				(org_id IS NOT NULL)
				AND current_user_has_entity_role (
					'organization'::entity_type,
					org_id,
					'admin'::membership_role
				)
			)
			OR (
				(client_id IS NOT NULL)
				AND current_user_has_entity_role (
					'client'::entity_type,
					client_id,
					'admin'::membership_role
				)
			)
			OR (
				(project_id IS NOT NULL)
				AND current_user_has_entity_role (
					'project'::entity_type,
					project_id,
					'editor'::membership_role
				)
			)
		)
	)
with
	check (
		(
			(
				(org_id IS NOT NULL)
				AND current_user_has_entity_role (
					'organization'::entity_type,
					org_id,
					'admin'::membership_role
				)
			)
			OR (
				(client_id IS NOT NULL)
				AND current_user_has_entity_role (
					'client'::entity_type,
					client_id,
					'admin'::membership_role
				)
			)
			OR (
				(project_id IS NOT NULL)
				AND current_user_has_entity_role (
					'project'::entity_type,
					project_id,
					'editor'::membership_role
				)
			)
		)
	);

create policy "Users can view vendors they have access to" on "public"."vendor" as permissive for
select
	to authenticated using (
		(
			(
				(org_id IS NOT NULL)
				AND current_user_has_entity_access ('organization'::entity_type, org_id)
			)
			OR (
				(client_id IS NOT NULL)
				AND (
					current_user_has_entity_access ('client'::entity_type, client_id)
					OR (
						EXISTS (
							SELECT
								1
							FROM
								client c
							WHERE
								(
									(c.client_id = vendor.client_id)
									AND current_user_has_entity_access ('organization'::entity_type, c.org_id)
								)
						)
					)
				)
			)
			OR (
				(project_id IS NOT NULL)
				AND (
					current_user_has_entity_access ('project'::entity_type, project_id)
					OR (
						EXISTS (
							SELECT
								1
							FROM
								(
									project p
									JOIN client c ON ((p.client_id = c.client_id))
								)
							WHERE
								(
									(p.project_id = vendor.project_id)
									AND (
										current_user_has_entity_access ('client'::entity_type, p.client_id)
										OR current_user_has_entity_access ('organization'::entity_type, c.org_id)
									)
								)
						)
					)
				)
			)
		)
	);

create policy "System can insert vendor audit records" on "public"."vendor_audit" as permissive for insert to service_role
with
	check (true);

create policy "Users can view vendor audit for accessible vendors" on "public"."vendor_audit" as permissive for
select
	to authenticated using (
		(
			(
				(org_id IS NOT NULL)
				AND current_user_has_entity_access ('organization'::entity_type, org_id)
			)
			OR (
				(client_id IS NOT NULL)
				AND (
					current_user_has_entity_access ('client'::entity_type, client_id)
					OR (
						EXISTS (
							SELECT
								1
							FROM
								client c
							WHERE
								(
									(c.client_id = vendor_audit.client_id)
									AND current_user_has_entity_access ('organization'::entity_type, c.org_id)
								)
						)
					)
				)
			)
			OR (
				(project_id IS NOT NULL)
				AND (
					current_user_has_entity_access ('project'::entity_type, project_id)
					OR (
						EXISTS (
							SELECT
								1
							FROM
								(
									project p
									JOIN client c ON ((p.client_id = c.client_id))
								)
							WHERE
								(
									(p.project_id = vendor_audit.project_id)
									AND (
										current_user_has_entity_access ('client'::entity_type, p.client_id)
										OR current_user_has_entity_access ('organization'::entity_type, c.org_id)
									)
								)
						)
					)
				)
			)
		)
	);

CREATE TRIGGER audit_vendor_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.vendor FOR EACH ROW
EXECUTE FUNCTION audit_vendor_changes ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.vendor FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();

CREATE TRIGGER validate_vendor_scope_trigger BEFORE INSERT
OR
UPDATE ON public.vendor FOR EACH ROW
EXECUTE FUNCTION validate_vendor_scope ();
