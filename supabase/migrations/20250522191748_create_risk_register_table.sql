-- Create risk register table
CREATE TABLE IF NOT EXISTS "public"."risk_register" (
	risk_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	project_id UUID NOT NULL REFERENCES project (project_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	title TEXT NOT NULL,
	description TEXT NOT NULL,
	status TEXT NOT NULL DEFAULT 'identified',
	wbs_library_item_id UUID REFERENCES wbs_library_item (wbs_library_item_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	date_identified DATE NOT NULL DEFAULT CURRENT_DATE,
	cause TEXT,
	effect TEXT,
	program_impact TEXT,
	probability NUMERIC(5, 2) NOT NULL DEFAULT 50,
	potential_impact NUMERIC(15, 2),
	mitigation_plan TEXT,
	date_for_review DATE,
	risk_owner_user_id UUID REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	risk_owner_name TEXT,
	risk_owner_email TEXT,
	created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
	updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
	-- Ensure you never have both a user_id and a free-text owner
	CONSTRAINT one_owner_type CHECK (
		(risk_owner_user_id IS NOT NULL)::int + (
			(risk_owner_name IS NOT NULL)::int + (risk_owner_email IS NOT NULL)::int
		) <= 1
	)
);

-- Add comment to the table
COMMENT ON TABLE "public"."risk_register" IS 'Project risk register for tracking and managing project risks';

-- Add comments to columns
COMMENT ON COLUMN "public"."risk_register"."risk_id" IS 'Primary key for the risk register (UUID)';

COMMENT ON COLUMN "public"."risk_register"."project_id" IS 'The project this risk belongs to';

COMMENT ON COLUMN "public"."risk_register"."title" IS 'Short title for the risk';

COMMENT ON COLUMN "public"."risk_register"."description" IS 'Detailed description of the risk';

COMMENT ON COLUMN "public"."risk_register"."status" IS 'Current status of the risk (identified, assessed, mitigated, occurred, closed)';

COMMENT ON COLUMN "public"."risk_register"."wbs_library_item_id" IS 'Optional link to a specific WBS item';

COMMENT ON COLUMN "public"."risk_register"."date_identified" IS 'Date the risk was recorded';

COMMENT ON COLUMN "public"."risk_register"."cause" IS 'How the risk might arise';

COMMENT ON COLUMN "public"."risk_register"."effect" IS 'Potential impact of the risk';

COMMENT ON COLUMN "public"."risk_register"."program_impact" IS 'Impact on project schedule';

COMMENT ON COLUMN "public"."risk_register"."probability" IS 'Chance of occurrence (0-100%)';

COMMENT ON COLUMN "public"."risk_register"."potential_impact" IS 'Estimated financial impact';

COMMENT ON COLUMN "public"."risk_register"."mitigation_plan" IS 'Steps to reduce or eliminate the risk';

COMMENT ON COLUMN "public"."risk_register"."date_for_review" IS 'When to revisit or reassess this risk';

COMMENT ON COLUMN "public"."risk_register"."risk_owner_user_id" IS 'User responsible for monitoring/mitigating the risk';

COMMENT ON COLUMN "public"."risk_register"."risk_owner_name" IS 'Name of external risk owner (if not a system user)';

COMMENT ON COLUMN "public"."risk_register"."risk_owner_email" IS 'Email of external risk owner (if not a system user)';

-- Create trigger to update the updated_at timestamp
CREATE TRIGGER update_risk_register_updated_at BEFORE
UPDATE ON public.risk_register FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column ();

-- Enable Row Level Security
ALTER TABLE risk_register ENABLE ROW LEVEL SECURITY;

-- Grant access to service_role
GRANT
SELECT
,
	INSERT,
UPDATE,
DELETE,
REFERENCES,
TRIGGER,
TRUNCATE ON public.risk_register TO service_role;

-- Setup RLS policies
-- Users can view risks for projects they have access to
CREATE POLICY "Users can view risks for projects they have access to" ON public.risk_register FOR
SELECT
	TO authenticated USING (public.can_access_project (project_id));

-- Project Editors and Owners can insert risks
CREATE POLICY "Project Editors and Owners can insert risks" ON public.risk_register FOR INSERT TO authenticated
WITH
	CHECK (
		public.current_user_has_entity_role ('project', project_id, 'editor')
	);

-- Project Editors and Owners can update risks
CREATE POLICY "Project Editors and Owners can update risks" ON public.risk_register
FOR UPDATE
	TO authenticated USING (
		public.current_user_has_entity_role ('project', project_id, 'editor')
	)
WITH
	CHECK (
		public.current_user_has_entity_role ('project', project_id, 'editor')
	);

-- Project Owners can delete risks
CREATE POLICY "Project Owners can delete risks" ON public.risk_register FOR DELETE TO authenticated USING (
	public.current_user_has_entity_role ('project', project_id, 'owner')
);

-- Create index for faster RLS policy evaluation
CREATE INDEX IF NOT EXISTS risk_register_project_idx ON public.risk_register USING btree (project_id);
