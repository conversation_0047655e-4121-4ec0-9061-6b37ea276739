drop policy "Project editors can insert gateway checklist item" on "public"."gateway_checklist_item";

drop policy "Project editors can update gateway checklist item" on "public"."gateway_checklist_item";

drop policy "Project editors can insert gateway checklist item status log" on "public"."gateway_checklist_item_status_log";

drop policy "Users can insert gateway stage info for projects they can edit" on "public"."project_gateway_stage_info";

drop policy "Users can update gateway stage info for projects they can edit" on "public"."project_gateway_stage_info";

create policy "Project editors can insert gateway checklist item" on "public"."gateway_checklist_item" as permissive for insert to authenticated
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					project_stage ps
				WHERE
					(
						(
							ps.project_stage_id = gateway_checklist_item.project_stage_id
						)
						AND can_modify_project (ps.project_id)
						AND (ps.date_completed IS NULL)
					)
			)
		)
	);

create policy "Project editors can update gateway checklist item" on "public"."gateway_checklist_item" as permissive
for update
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					project_stage ps
				WHERE
					(
						(
							ps.project_stage_id = gateway_checklist_item.project_stage_id
						)
						AND can_modify_project (ps.project_id)
						AND (ps.date_completed IS NULL)
					)
			)
		)
	)
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					project_stage ps
				WHERE
					(
						(
							ps.project_stage_id = gateway_checklist_item.project_stage_id
						)
						AND can_modify_project (ps.project_id)
						AND (ps.date_completed IS NULL)
					)
			)
		)
	);

create policy "Project editors can insert gateway checklist item status log" on "public"."gateway_checklist_item_status_log" as permissive for insert to authenticated
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						gateway_checklist_item gci
						JOIN project_stage ps ON ((ps.project_stage_id = gci.project_stage_id))
					)
				WHERE
					(
						(
							gci.gateway_checklist_item_id = gateway_checklist_item_status_log.gateway_checklist_item_id
						)
						AND can_modify_project (ps.project_id)
						AND (ps.date_completed IS NULL)
					)
			)
		)
	);

create policy "Users can insert gateway stage info for projects they can edit" on "public"."project_gateway_stage_info" as permissive for insert to authenticated
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					project_stage ps
				WHERE
					(
						(
							ps.project_stage_id = project_gateway_stage_info.project_stage_id
						)
						AND can_modify_project (ps.project_id)
						AND (ps.date_completed IS NULL)
					)
			)
		)
	);

create policy "Users can update gateway stage info for projects they can edit" on "public"."project_gateway_stage_info" as permissive
for update
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					project_stage ps
				WHERE
					(
						(
							ps.project_stage_id = project_gateway_stage_info.project_stage_id
						)
						AND can_modify_project (ps.project_id)
						AND (ps.date_completed IS NULL)
					)
			)
		)
	)
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					project_stage ps
				WHERE
					(
						(
							ps.project_stage_id = project_gateway_stage_info.project_stage_id
						)
						AND can_modify_project (ps.project_id)
						AND (ps.date_completed IS NULL)
					)
			)
		)
	);
