drop function if exists "public"."get_accessible_invoices" (project_id_param uuid);

drop function if exists "public"."get_accessible_purchase_orders" (project_id_param uuid);

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_accessible_invoices (project_id_param uuid) RETURNS TABLE (
	invoice_id uuid,
	purchase_order_id uuid,
	po_number text,
	description text,
	invoice_date date,
	vendor_name text,
	account text,
	amount numeric,
	period text,
	post_date date,
	notes text,
	created_at timestamp with time zone
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RAISE EXCEPTION 'Access denied to project';
	END IF;

	-- Return invoices for the project with vendor, purchase order, and creator information
	RETURN QUERY
	SELECT
		i.invoice_id,
		i.purchase_order_id,
		po.po_number,
		i.description,
		i.invoice_date,
		v.name AS vendor_name,
		i.account,
		i.amount,
		i.period,
		i.post_date,
		i.notes,
		i.created_at
	FROM public.invoice i
	INNER JOIN public.purchase_order po ON i.purchase_order_id = po.purchase_order_id
	LEFT JOIN public.vendor v ON po.vendor_id = v.vendor_id
	WHERE po.project_id = project_id_param
	ORDER BY i.invoice_date DESC, po.po_number;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_accessible_purchase_orders (project_id_param uuid) RETURNS TABLE (
	purchase_order_id uuid,
	po_number text,
	description text,
	po_date date,
	vendor_name text,
	vendor_id uuid,
	original_amount numeric,
	co_amount numeric,
	total_amount numeric,
	created_at timestamp with time zone
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return purchase orders for the project with vendor and creator information
	RETURN QUERY
	SELECT
		po.purchase_order_id,
		po.po_number,
		po.description,
		po.po_date,
		v.name AS vendor_name,
		po.vendor_id,
		po.original_amount,
		po.co_amount,
		COALESCE(po.original_amount, 0) + COALESCE(po.co_amount, 0) + COALESCE(po.freight, 0) + COALESCE(po.tax, 0) + COALESCE(po.other, 0) AS total_amount,
		po.created_at
	FROM public.purchase_order po
	LEFT JOIN public.vendor v ON po.vendor_id = v.vendor_id
	WHERE po.project_id = project_id_param
	ORDER BY po.po_date DESC, po.po_number;
END;
$function$;
