-- Organization Table
create table "public"."organization" (
	org_id uuid not null default gen_random_uuid() primary key,
	name text not null unique,
	description text,
	logo_url text,
	created_by_user_id uuid default auth.uid () not null references profile (user_id) on update restrict on delete restrict,
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null
);

comment on table "public"."organization" is 'Organization that group users and clients together';

-- Create trigger on organization to call the update function before any update
create trigger update_updated_at before
update on public.organization for each row
execute function public.update_updated_at_column ();

-- Create trigger to add creator as admin
create trigger add_creator_as_admin_organization
after insert on public.organization for each row
execute function public.add_creator_as_admin ();

-- Setup RLS policies for organization
alter table organization enable row level security;

-- Grant access to service_role
grant
select
,
	insert,
update,
delete,
references,
trigger,
truncate on public.organization to service_role;

-- Organization members can view the organization
create policy "Organization members can view the organization" on public.organization for
select
	to authenticated using (
		public.current_user_has_entity_access ('organization', org_id)
	);

create policy "Users can create organizations" on public.organization for insert to authenticated
with
	check (created_by_user_id = auth.uid ());

create policy "Users can update their own organization" on public.organization
for update
	to authenticated using (
		public.current_user_has_entity_role ('organization', org_id, 'admin')
	);

create policy "Users can delete their own organization" on public.organization for delete to authenticated using (
	public.current_user_has_entity_role ('organization', org_id, 'admin')
);

-- Create RPC function to handle organization creation
create or replace function public.create_organization (
	name text,
	description text default null,
	logo_url text default null
) returns json language plpgsql security definer
set
	search_path = '' as $$
declare new_org public.organization;
user_id uuid := auth.uid();
begin -- Ensure user is authenticated
if user_id is null then raise exception 'Not authenticated';
end if;
-- Create organization
insert into public.organization(name, description, logo_url, created_by_user_id)
values (name, description, logo_url, user_id)
returning * into new_org;
-- The creator will be automatically added as admin via the add_creator_as_admin trigger
return json_build_object(
	'org_id',
	new_org.org_id,
	'name',
	new_org.name,
	'description',
	new_org.description,
	'logo_url',
	new_org.logo_url
);
end;
$$;

comment on function public.create_organization (text, text, text) is 'Creates a new organization and adds the creator as an admin';

-- Grant execute privileges on create_organization function
grant
execute on function public.create_organization (text, text, text) to authenticated;
