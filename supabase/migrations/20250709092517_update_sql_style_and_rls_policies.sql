drop policy "Users can update their own organization" on "public"."organization";

drop policy "Users can update their own profile" on "public"."profile";

drop policy "Users can delete gateway stage info for projects they can edit" on "public"."project_gateway_stage_info";

drop policy "Users can update gateway stage info for projects they can edit" on "public"."project_gateway_stage_info";

drop policy "Users can view stage info for projects they can view" on "public"."project_gateway_stage_info";

drop policy "Service role can update WBS library" on "public"."wbs_library";

drop policy "Service role can update standard WBS library item" on "public"."wbs_library_item";

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.audit_approved_changes_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, old_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.approved_change_id, OLD.project_id, OLD.title, OLD.description, OLD.status, OLD.wbs_library_item_id,
            OLD.date_identified, OLD.date_approved, OLD.cause, OLD.effect, OLD.program_impact, OLD.potential_impact,
            OLD.mitigation_plan, OLD.date_for_review, OLD.risk_owner_user_id, OLD.risk_owner_name, OLD.risk_owner_email,
            OLD.approved_by_user_id, OLD.original_risk_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.approved_change_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.date_approved, NEW.cause, NEW.effect, NEW.program_impact, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.approved_by_user_id, NEW.original_risk_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, new_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.approved_change_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.date_approved, NEW.cause, NEW.effect, NEW.program_impact, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.approved_by_user_id, NEW.original_risk_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_gateway_checklist_item_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, old_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.gateway_checklist_item_id, OLD.project_stage_id, OLD.name, OLD.description,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.gateway_checklist_item_id, NEW.project_stage_id, NEW.name, NEW.description,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, new_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.gateway_checklist_item_id, NEW.project_stage_id, NEW.name, NEW.description,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_project_gateway_stage_info_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, old_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.project_gateway_stage_info_id, OLD.project_stage_id, OLD.basement_floors, OLD.ground_floor, OLD.upper_floors,
            OLD.total_gross_internal_floor_area, OLD.usable_area, OLD.circulation_area, OLD.ancillary_areas, OLD.internal_divisions,
            OLD.spaces_not_enclosed, OLD.total_gross_internal_floor_area_2, OLD.internal_cube, OLD.area_of_lowest_floor,
            OLD.site_area, OLD.number_of_units, OLD.nr_of_storeys, OLD.nr_of_storeys_primary, OLD.nr_of_storeys_secondary,
            OLD.basement_storeys_included_above, OLD.average_storey_height, OLD.below_ground_floors, OLD.ground_floor_height,
            OLD.above_ground_floors, OLD.external_vertical_envelope, OLD.additional_data, OLD.created_by_user_id,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.project_gateway_stage_info_id, NEW.project_stage_id, NEW.basement_floors, NEW.ground_floor, NEW.upper_floors,
            NEW.total_gross_internal_floor_area, NEW.usable_area, NEW.circulation_area, NEW.ancillary_areas, NEW.internal_divisions,
            NEW.spaces_not_enclosed, NEW.total_gross_internal_floor_area_2, NEW.internal_cube, NEW.area_of_lowest_floor,
            NEW.site_area, NEW.number_of_units, NEW.nr_of_storeys, NEW.nr_of_storeys_primary, NEW.nr_of_storeys_secondary,
            NEW.basement_storeys_included_above, NEW.average_storey_height, NEW.below_ground_floors, NEW.ground_floor_height,
            NEW.above_ground_floors, NEW.external_vertical_envelope, NEW.additional_data, NEW.created_by_user_id,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, new_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.project_gateway_stage_info_id, NEW.project_stage_id, NEW.basement_floors, NEW.ground_floor, NEW.upper_floors,
            NEW.total_gross_internal_floor_area, NEW.usable_area, NEW.circulation_area, NEW.ancillary_areas, NEW.internal_divisions,
            NEW.spaces_not_enclosed, NEW.total_gross_internal_floor_area_2, NEW.internal_cube, NEW.area_of_lowest_floor,
            NEW.site_area, NEW.number_of_units, NEW.nr_of_storeys, NEW.nr_of_storeys_primary, NEW.nr_of_storeys_secondary,
            NEW.basement_storeys_included_above, NEW.average_storey_height, NEW.below_ground_floors, NEW.ground_floor_height,
            NEW.above_ground_floors, NEW.external_vertical_envelope, NEW.additional_data, NEW.created_by_user_id,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_risk_register_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, old_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.risk_id, OLD.project_id, OLD.title, OLD.description, OLD.status, OLD.wbs_library_item_id,
            OLD.date_identified, OLD.cause, OLD.effect, OLD.program_impact, OLD.probability, OLD.potential_impact,
            OLD.mitigation_plan, OLD.date_for_review, OLD.risk_owner_user_id, OLD.risk_owner_name, OLD.risk_owner_email,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.risk_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.cause, NEW.effect, NEW.program_impact, NEW.probability, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, new_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.risk_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.cause, NEW.effect, NEW.program_impact, NEW.probability, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.create_budget_snapshot (
	p_project_stage_id uuid,
	p_freeze_reason text DEFAULT NULL::text
) RETURNS uuid LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_project_id UUID;
	v_snapshot_id UUID;
	v_item RECORD;
	v_user_id UUID;
BEGIN
	-- Get the current user ID, fallback to a system user if not authenticated
	v_user_id := auth.uid();
	IF v_user_id IS NULL THEN
		-- Use a system user ID for operations not performed by authenticated users
		v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
	END IF;

	-- Get the project_id from the stage
	SELECT project_id INTO v_project_id
	FROM public.project_stage
	WHERE project_stage_id = p_project_stage_id;

	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Project stage not found';
	END IF;

	INSERT INTO public.budget_snapshot (
		project_stage_id,
		freeze_date,
		freeze_reason,
		created_by_user_id
	)
	VALUES (
		p_project_stage_id,
		now(),
		p_freeze_reason,
		v_user_id
	)
	RETURNING budget_snapshot_id INTO v_snapshot_id;
	
	FOR v_item IN (
		SELECT *
		FROM public.budget_line_item_current
		WHERE project_id = v_project_id
	) LOOP
		INSERT INTO public.budget_snapshot_line_item (
			budget_snapshot_id,
			wbs_library_item_id,
			quantity,
			unit,
			material_rate,
			labor_rate,
			productivity_per_hour,
			unit_rate_manual_override,
			unit_rate,
			factor,
			remarks,
			cost_certainty,
			design_certainty
		)
		VALUES (
			v_snapshot_id,
			v_item.wbs_library_item_id,
			v_item.quantity,
			v_item.unit,
			v_item.material_rate,
			v_item.labor_rate,
			v_item.productivity_per_hour,
			v_item.unit_rate_manual_override,
			v_item.unit_rate,
			v_item.factor,
			v_item.remarks,
			v_item.cost_certainty,
			v_item.design_certainty
		);
	END LOOP;
	
	RETURN v_snapshot_id;
END;
$function$;

create policy "Project editors can delete gateway checklist item status log" on "public"."gateway_checklist_item_status_log" as permissive for delete to authenticated using (
	(
		SELECT
			can_modify_project (
				(
					SELECT
						project_stage.project_id
					FROM
						project_stage
					WHERE
						(
							project_stage.project_stage_id = (
								SELECT
									gateway_checklist_item.project_stage_id
								FROM
									gateway_checklist_item
								WHERE
									(
										gateway_checklist_item.gateway_checklist_item_id = gateway_checklist_item_status_log.gateway_checklist_item_id
									)
							)
						)
				)
			) AS can_modify_project
	)
);

create policy "Project editors can update gateway checklist item status log" on "public"."gateway_checklist_item_status_log" as permissive
for update
	to authenticated using (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_stage.project_id
						FROM
							project_stage
						WHERE
							(
								project_stage.project_stage_id = (
									SELECT
										gateway_checklist_item.project_stage_id
									FROM
										gateway_checklist_item
									WHERE
										(
											gateway_checklist_item.gateway_checklist_item_id = gateway_checklist_item_status_log.gateway_checklist_item_id
										)
								)
							)
					)
				) AS can_modify_project
		)
	)
with
	check (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_stage.project_id
						FROM
							project_stage
						WHERE
							(
								project_stage.project_stage_id = (
									SELECT
										gateway_checklist_item.project_stage_id
									FROM
										gateway_checklist_item
									WHERE
										(
											gateway_checklist_item.gateway_checklist_item_id = gateway_checklist_item_status_log.gateway_checklist_item_id
										)
								)
							)
					)
				) AS can_modify_project
		)
	);

create policy "Admins can create invites" on "public"."invite" as permissive for insert to authenticated
with
	check (
		(
			(
				(
					resource_type = 'organization'::invite_resource_type
				)
				AND current_user_has_entity_role (
					'organization'::entity_type,
					resource_id,
					'admin'::membership_role
				)
			)
			OR (
				(resource_type = 'client'::invite_resource_type)
				AND current_user_has_entity_role (
					'client'::entity_type,
					resource_id,
					'admin'::membership_role
				)
			)
			OR (
				(resource_type = 'project'::invite_resource_type)
				AND can_modify_project (resource_id)
			)
		)
	);

create policy "Users can update their own organization" on "public"."organization" as permissive
for update
	to authenticated using (
		current_user_has_entity_role (
			'organization'::entity_type,
			org_id,
			'admin'::membership_role
		)
	)
with
	check (
		current_user_has_entity_role (
			'organization'::entity_type,
			org_id,
			'admin'::membership_role
		)
	);

create policy "Users can update their own profile" on "public"."profile" as permissive
for update
	to authenticated using (
		(
			user_id = (
				SELECT
					auth.uid () AS uid
			)
		)
	)
with
	check (
		(
			user_id = (
				SELECT
					auth.uid () AS uid
			)
		)
	);

create policy "Users can delete gateway stage info for projects they can edit" on "public"."project_gateway_stage_info" as permissive for delete to authenticated using (
	(
		EXISTS (
			SELECT
				1
			FROM
				project_stage ps
			WHERE
				(
					(
						ps.project_stage_id = project_gateway_stage_info.project_stage_id
					)
					AND current_user_has_entity_role (
						'project'::entity_type,
						ps.project_id,
						'owner'::membership_role
					)
				)
		)
	)
);

create policy "Users can update gateway stage info for projects they can edit" on "public"."project_gateway_stage_info" as permissive
for update
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					project_stage ps
				WHERE
					(
						(
							ps.project_stage_id = project_gateway_stage_info.project_stage_id
						)
						AND can_modify_project (ps.project_id)
					)
			)
		)
	)
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					project_stage ps
				WHERE
					(
						(
							ps.project_stage_id = project_gateway_stage_info.project_stage_id
						)
						AND can_modify_project (ps.project_id)
					)
			)
		)
	);

create policy "Users can view stage info for projects they can view" on "public"."project_gateway_stage_info" as permissive for
select
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					project_stage ps
				WHERE
					(
						(
							ps.project_stage_id = project_gateway_stage_info.project_stage_id
						)
						AND can_access_project (ps.project_id)
					)
			)
		)
	);

create policy "Service role can update WBS library" on "public"."wbs_library" as permissive
for update
	to service_role using (true)
with
	check (true);

create policy "Service role can update standard WBS library item" on "public"."wbs_library_item" as permissive
for update
	to service_role using ((item_type = 'Standard'::wbs_item_type))
with
	check ((item_type = 'Standard'::wbs_item_type));
