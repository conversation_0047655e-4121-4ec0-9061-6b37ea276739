-- Create RLS policies for project table
-- Users can view projects they have access to (hierarchical access)
create policy "Users can view projects they have access to" on public.project for
select
	to authenticated using (
		public.current_user_has_entity_access ('project', project_id)
	);

-- Users with editor+ role on the client can create projects
create policy "Client editors and admins can insert projects" on public.project for insert to authenticated
with
	check (
		public.current_user_has_entity_role ('client', client_id, 'editor')
	);

-- Users with editor+ role on the project can update projects
create policy "Project editors and owners can update projects" on public.project
for update
	to authenticated using (
		public.current_user_has_entity_role ('project', project_id, 'editor')
	)
with
	check (
		public.current_user_has_entity_role ('project', project_id, 'editor')
	);

-- Users with owner role on the project can delete projects
create policy "Project owners can delete projects" on public.project for delete to authenticated using (
	public.current_user_has_entity_role ('project', project_id, 'owner')
);
