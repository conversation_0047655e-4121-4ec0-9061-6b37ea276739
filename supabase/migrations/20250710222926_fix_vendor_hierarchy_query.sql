set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_accessible_vendors (
	user_id_param uuid,
	entity_type_param entity_type,
	entity_id_param uuid
) RETURNS TABLE (
	vendor_id uuid,
	name text,
	description text,
	vendor_type text,
	contact_name text,
	contact_email text,
	contact_phone text,
	is_active boolean,
	access_level text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
	-- Check if user has access to the specified entity
	IF NOT public.has_entity_access(user_id_param, entity_type_param, entity_id_param) THEN
		RETURN;
	END IF;

	-- Return vendors based on hierarchy
	IF entity_type_param = 'project' THEN
		-- For projects, return project-level, client-level, and organization-level vendors
		RETURN QUERY
		SELECT
			v.vendor_id, v.name, v.description, v.vendor_type,
			v.contact_name, v.contact_email, v.contact_phone, v.is_active,
			CASE
				WHEN v.project_id IS NOT NULL THEN 'project'
				WHEN v.client_id IS NOT NULL THEN 'client'
				WHEN v.org_id IS NOT NULL THEN 'organization'
			END as access_level
		FROM public.vendor v
		JOIN public.project p ON p.project_id = entity_id_param
		JOIN public.client c ON p.client_id = c.client_id
		WHERE (
			v.project_id = entity_id_param
			OR v.client_id = p.client_id
			OR v.org_id = c.org_id
		)
		AND v.is_active = true
		ORDER BY
			CASE
				WHEN v.project_id IS NOT NULL THEN 1
				WHEN v.client_id IS NOT NULL THEN 2
				WHEN v.org_id IS NOT NULL THEN 3
			END,
			v.name;

	ELSIF entity_type_param = 'client' THEN
		-- For clients, return client-level and organization-level vendors
		RETURN QUERY
		SELECT
			v.vendor_id, v.name, v.description, v.vendor_type,
			v.contact_name, v.contact_email, v.contact_phone, v.is_active,
			CASE
				WHEN v.client_id IS NOT NULL THEN 'client'
				WHEN v.org_id IS NOT NULL THEN 'organization'
			END as access_level
		FROM public.vendor v
		JOIN public.client c ON c.client_id = entity_id_param
		WHERE (
			v.client_id = entity_id_param
			OR v.org_id = c.org_id
		)
		AND v.is_active = true
		ORDER BY
			CASE
				WHEN v.client_id IS NOT NULL THEN 1
				WHEN v.org_id IS NOT NULL THEN 2
			END,
			v.name;

	ELSIF entity_type_param = 'organization' THEN
		-- For organizations, return organization-level, client-level, and project-level vendors within the organization
		RETURN QUERY
		SELECT
			v.vendor_id, v.name, v.description, v.vendor_type,
			v.contact_name, v.contact_email, v.contact_phone, v.is_active,
			CASE
				WHEN v.project_id IS NOT NULL THEN 'project'
				WHEN v.client_id IS NOT NULL THEN 'client'
				WHEN v.org_id IS NOT NULL THEN 'organization'
			END as access_level
		FROM public.vendor v
		LEFT JOIN public.client c ON v.client_id = c.client_id
		LEFT JOIN public.project p ON v.project_id = p.project_id
		LEFT JOIN public.client pc ON p.client_id = pc.client_id
		WHERE (
			-- Organization-level vendors
			v.org_id = entity_id_param
			-- Client-level vendors within this organization
			OR (v.client_id IS NOT NULL AND c.org_id = entity_id_param)
			-- Project-level vendors within this organization
			OR (v.project_id IS NOT NULL AND pc.org_id = entity_id_param)
		)
		AND v.is_active = true
		ORDER BY
			CASE
				WHEN v.org_id IS NOT NULL THEN 1
				WHEN v.client_id IS NOT NULL THEN 2
				WHEN v.project_id IS NOT NULL THEN 3
			END,
			v.name;
	END IF;
END;
$function$;
