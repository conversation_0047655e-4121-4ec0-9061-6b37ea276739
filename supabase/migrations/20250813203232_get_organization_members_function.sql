set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_organization_members (_org_name text) RETURNS TABLE (
	user_id uuid,
	email text,
	full_name text,
	avatar_url text,
	created_at timestamp with time zone,
	updated_at timestamp with time zone,
	role text,
	membership_id uuid
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
declare
	_org_id uuid;
	_has_access boolean;
begin
	-- Get the org_id for the organization name
	select org_id into _org_id
	from public.organization
	where name = _org_name;

	if _org_id is null then
		raise exception 'Organization not found: %', _org_name;
	end if;

	-- Check if current user has access to this organization
	select public.current_user_has_entity_access('organization', _org_id) into _has_access;

	if not _has_access then
		raise exception 'Access denied: no access to organization';
	end if;

	-- Return organization members
	return query
	select
		p.user_id,
		p.email,
		p.full_name,
		p.avatar_url,
		p.created_at,
		p.updated_at,
		m.role::text as role,
		m.membership_id
	from public.profile p
	join public.membership m on p.user_id = m.user_id
	where m.entity_type = 'organization'
		and m.entity_id = _org_id
	order by m.role desc, m.created_at asc; -- Admin first, then by creation time
end;
$function$;
