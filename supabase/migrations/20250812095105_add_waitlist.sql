create table "public"."waitlist" (
	"waitlist_id" uuid not null default gen_random_uuid(),
	"email" text not null,
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."waitlist" enable row level security;

CREATE UNIQUE INDEX waitlist_email_key ON public.waitlist USING btree (email);

CREATE UNIQUE INDEX waitlist_pkey ON public.waitlist USING btree (waitlist_id);

alter table "public"."waitlist"
add constraint "waitlist_pkey" PRIMARY KEY using index "waitlist_pkey";

alter table "public"."waitlist"
add constraint "waitlist_email_key" UNIQUE using index "waitlist_email_key";

grant delete on table "public"."waitlist" to "anon";

grant insert on table "public"."waitlist" to "anon";

grant references on table "public"."waitlist" to "anon";

grant
select
	on table "public"."waitlist" to "anon";

grant trigger on table "public"."waitlist" to "anon";

grant
truncate on table "public"."waitlist" to "anon";

grant
update on table "public"."waitlist" to "anon";

grant delete on table "public"."waitlist" to "authenticated";

grant insert on table "public"."waitlist" to "authenticated";

grant references on table "public"."waitlist" to "authenticated";

grant
select
	on table "public"."waitlist" to "authenticated";

grant trigger on table "public"."waitlist" to "authenticated";

grant
truncate on table "public"."waitlist" to "authenticated";

grant
update on table "public"."waitlist" to "authenticated";

grant delete on table "public"."waitlist" to "service_role";

grant insert on table "public"."waitlist" to "service_role";

grant references on table "public"."waitlist" to "service_role";

grant
select
	on table "public"."waitlist" to "service_role";

grant trigger on table "public"."waitlist" to "service_role";

grant
truncate on table "public"."waitlist" to "service_role";

grant
update on table "public"."waitlist" to "service_role";

create policy "Anyone can insert waitlist" on "public"."waitlist" as permissive for insert to anon
with
	check (true);
