-- Function to add creator as admin when a new entity is created
create or replace function public.add_creator_as_admin () returns trigger language plpgsql security definer
set
	search_path = '' as $$
declare has_org_admin_access boolean := false;
org_id_var uuid;
begin -- Add the creator as an admin/owner
if TG_TABLE_NAME = 'organization' then -- For organizations, simply add the creator as admin
insert into public.membership(user_id, role, entity_type, entity_id)
values (
		NEW.created_by_user_id,
		'admin',
		'organization',
		NEW.org_id
	);
elsif TG_TABLE_NAME = 'client' then -- For clients, check if the creator already has admin access through the organization
select exists (
		select 1
		from public.membership m
		where m.user_id = NEW.created_by_user_id
			and m.entity_type = 'organization'
			and m.entity_id = NEW.org_id
			and m.role = 'admin'
	) into has_org_admin_access;
-- Only add client admin membership if they don't have org admin access
if not has_org_admin_access then
insert into public.membership(user_id, role, entity_type, entity_id)
values (
		NEW.created_by_user_id,
		'admin',
		'client',
		NEW.client_id
	);
end if;
elsif TG_TABLE_NAME = 'project' then -- For projects, get the client's org_id
select c.org_id into org_id_var
from public.client c
where c.client_id = NEW.client_id;
-- Check if the creator has admin access through the organization
select exists (
		select 1
		from public.membership m
		where m.user_id = NEW.created_by_user_id
			and m.entity_type = 'organization'
			and m.entity_id = org_id_var
			and m.role = 'admin'
	) into has_org_admin_access;
-- If they have org admin access, don't add redundant memberships
if has_org_admin_access then -- Skip adding project owner membership
null;
else -- Check if they have admin access through the client
select exists (
		select 1
		from public.membership m
		where m.user_id = NEW.created_by_user_id
			and m.entity_type = 'client'
			and m.entity_id = NEW.client_id
			and m.role = 'admin'
	) into has_org_admin_access;
-- Only add project owner membership if they don't have client admin access
if not has_org_admin_access then
insert into public.membership(user_id, role, entity_type, entity_id)
values (
		NEW.created_by_user_id,
		'owner',
		'project',
		NEW.project_id
	);
end if;
end if;
end if;
return NEW;
end;
$$;

comment on function public.add_creator_as_admin () is 'Automatically adds the creator as an admin/owner of the entity';

-- Grant execute privileges on add_creator_as_admin function
grant
execute on function public.add_creator_as_admin () to service_role;
