-- Add indexes used by RLS policies
create index if not exists membership_user_idx on public.membership using btree (user_id);

create index if not exists membership_entity_idx on public.membership using btree (entity_type, entity_id);

create index if not exists wbs_library_item_client_idx on public.wbs_library_item using btree (client_id);

create index if not exists wbs_library_item_project_idx on public.wbs_library_item using btree (project_id);

create index if not exists invite_resource_idx on public.invite using btree (resource_type, resource_id);

create index if not exists project_gateway_stage_info_stage_idx on public.project_gateway_stage_info using btree (project_stage_id);
