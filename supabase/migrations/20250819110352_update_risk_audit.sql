alter table "public"."risk_register_audit"
drop column "potential_impact";

alter table "public"."risk_register_audit"
drop column "probability";

alter table "public"."risk_register_audit"
add column "construction_total" numeric(15, 2);

alter table "public"."risk_register_audit"
add column "design_fees" numeric(15, 2);

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.audit_risk_register_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, old_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, design_fees, construction_total,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.risk_id, OLD.project_id, OLD.title, OLD.description, OLD.status, OLD.wbs_library_item_id,
            OLD.date_identified, OLD.cause, OLD.effect, OLD.program_impact, OLD.design_fees, OLD.construction_total,
            OLD.mitigation_plan, OLD.date_for_review, OLD.risk_owner_user_id, OLD.risk_owner_name, OLD.risk_owner_email,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, design_fees, construction_total,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.risk_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.cause, NEW.effect, NEW.program_impact, NEW.design_fees, NEW.construction_total,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, new_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, design_fees, construction_total,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.risk_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.cause, NEW.effect, NEW.program_impact, NEW.design_fees, NEW.construction_total,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;
