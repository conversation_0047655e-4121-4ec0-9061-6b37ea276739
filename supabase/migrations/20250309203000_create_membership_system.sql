-- Function to check if a membership is redundant
create or replace function public.check_membership_redundancy () returns trigger language plpgsql security definer
set
	search_path = '' as $$
declare ancestor_role public.membership_role;
begin -- Check if user already has equal or higher role through an ancestor
if NEW.entity_type = 'project' then -- Check client level
select public.get_effective_role(NEW.user_id, 'client', p.client_id) into ancestor_role
from public.project p
where p.project_id = NEW.entity_id;
if ancestor_role = 'admin'
or ancestor_role = 'owner' then raise exception 'User already has admin access to this project through client-level permissions';
end if;
-- Check organization level
select public.get_effective_role(NEW.user_id, 'organization', c.org_id) into ancestor_role
from public.project p
	join public.client c on p.client_id = c.client_id
where p.project_id = NEW.entity_id;
if ancestor_role = 'admin' then raise exception 'User already has admin access to this project through organization-level permissions';
end if;
end if;
if NEW.entity_type = 'client' then -- Check organization level
select public.get_effective_role(NEW.user_id, 'organization', c.org_id) into ancestor_role
from public.client c
where c.client_id = NEW.entity_id;
if ancestor_role = 'admin' then raise exception 'User already has admin access to this client through organization-level permissions';
end if;
end if;
return NEW;
end;
$$;

comment on function public.check_membership_redundancy () is 'Prevents redundant memberships when a user already has access through an ancestor entity';

-- Create trigger to check for redundant memberships
create trigger check_membership_redundancy_trigger before insert
or
update on public.membership for each row
execute function public.check_membership_redundancy ();

-- Function to add creator as admin has been moved to 20250304000000_add_creator_as_admin_function.sql
-- Enable RLS on membership table
alter table membership enable row level security;

-- Grant access to service_role
grant
select
,
	insert,
update,
delete,
references,
trigger,
truncate on public.membership to service_role;

-- Create policies for membership table
create policy "Users can view memberships they have access to" on public.membership for
select
	to authenticated using (
		public.current_user_has_entity_access (entity_type, entity_id)
	);

create policy "Admins can manage memberships" on public.membership for insert to authenticated
with
	check (
		public.current_user_has_entity_role (entity_type, entity_id, 'admin')
	);

create policy "Admins can update memberships" on public.membership
for update
	to authenticated using (
		public.current_user_has_entity_role (entity_type, entity_id, 'admin')
	)
with
	check (
		public.current_user_has_entity_role (entity_type, entity_id, 'admin')
	);

create policy "Admins can delete memberships" on public.membership for delete to authenticated using (
	public.current_user_has_entity_role (entity_type, entity_id, 'admin')
);

-- Create compatibility functions to replace the old access check functions
create or replace function public.can_access_project (project_id_param uuid) returns boolean language plpgsql security definer
set
	search_path = '' as $$ begin return public.current_user_has_entity_access('project', project_id_param);
end;
$$;

create or replace function public.can_modify_project (project_id_param uuid) returns boolean language plpgsql security definer
set
	search_path = '' as $$ begin return public.current_user_has_entity_role('project', project_id_param, 'editor');
end;
$$;

create or replace function public.is_project_owner (project_id_param uuid) returns boolean language plpgsql security definer
set
	search_path = '' as $$
declare client_id_var uuid;
org_id_var uuid;
begin -- Check if user has direct owner role on the project
if public.current_user_has_entity_role('project', project_id_param, 'owner') then return true;
end if;
-- Get the client_id for this project
select client_id into client_id_var
from public.project
where project_id = project_id_param;
-- Check if user has admin role on the client
if client_id_var is not null
and public.current_user_has_entity_role('client', client_id_var, 'admin') then return true;
end if;
-- Get the org_id for this client
select org_id into org_id_var
from public.client
where client_id = client_id_var;
-- Check if user has admin role on the organization
if org_id_var is not null
and public.current_user_has_entity_role('organization', org_id_var, 'admin') then return true;
end if;
return false;
end;
$$;

comment on function public.is_project_owner (uuid) is 'Checks if the current user is a project owner, client admin, or organization admin for the project';

create or replace function public.is_org_admin_for_project (project_id_param uuid) returns boolean language plpgsql security definer
set
	search_path = '' as $$
declare org_id_var uuid;
begin
select c.org_id into org_id_var
from public.project p
	join public.client c on p.client_id = c.client_id
where p.project_id = project_id_param;
return public.current_user_has_entity_role('organization', org_id_var, 'admin');
end;
$$;

create or replace function public.can_access_client (client_id_param uuid) returns boolean language plpgsql security definer
set
	search_path = '' as $$ begin return public.current_user_has_entity_access('client', client_id_param);
end;
$$;

create or replace function public.can_modify_client (client_id_param uuid) returns boolean language plpgsql security definer
set
	search_path = '' as $$ begin return public.current_user_has_entity_role('client', client_id_param, 'editor');
end;
$$;

create or replace function public.is_client_admin (client_id_param uuid) returns boolean language plpgsql security definer
set
	search_path = '' as $$ begin return public.current_user_has_entity_role('client', client_id_param, 'admin');
end;
$$;

create or replace function public.can_modify_client_wbs (client_id_param uuid) returns boolean language plpgsql security definer
set
	search_path = '' as $$ begin return public.current_user_has_entity_role('client', client_id_param, 'admin');
end;
$$;

-- Drop existing organization SELECT policy and create a new expanded one
DROP POLICY IF EXISTS "Organization members can view the organization" ON public.organization;

-- Create policy for organization table to expand SELECT access permissions
create policy "Users can view organizations they have access to" on public.organization for
select
	to authenticated using (
		-- Direct access to the organization
		public.current_user_has_entity_access ('organization', org_id)
		OR EXISTS (
			-- Access through a client that belongs to this organization
			SELECT
				1
			FROM
				public.client c
			WHERE
				c.org_id = organization.org_id
				AND public.current_user_has_entity_access ('client', c.client_id)
		)
		OR EXISTS (
			-- Access through a project where the project's client belongs to this organization
			SELECT
				1
			FROM
				public.project p
				JOIN public.client c ON p.client_id = c.client_id
			WHERE
				c.org_id = organization.org_id
				AND public.current_user_has_entity_access ('project', p.project_id)
		)
	);

-- Create policy for client table to expand SELECT access permissions
create policy "Users can view clients they have access to" on public.client for
select
	to authenticated using (
		-- Direct access to the client
		public.current_user_has_entity_access ('client', client_id)
		OR EXISTS (
			-- Access through a project of this client
			SELECT
				1
			FROM
				public.project p
			WHERE
				p.client_id = client.client_id
				AND public.current_user_has_entity_access ('project', p.project_id)
		)
	);
