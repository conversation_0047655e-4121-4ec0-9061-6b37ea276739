-- Update invoice period format from month names to YYYY-MM format
-- This migration converts existing period data and adds validation
-- First, update any existing period data from month names to YYYY-MM format
-- We'll use the current year for existing month-only periods
DO $$
DECLARE
    current_year TEXT := EXTRACT(YEAR FROM NOW())::TEXT;
    month_mapping JSONB := '{
        "January": "01",
        "February": "02",
        "March": "03",
        "April": "04",
        "May": "05",
        "June": "06",
        "July": "07",
        "August": "08",
        "September": "09",
        "October": "10",
        "November": "11",
        "December": "12"
    }';
    updated_count INTEGER;
BEGIN
    -- Update existing month names to YYYY-MM format
    UPDATE public.invoice
    SET period = current_year || '-' || (month_mapping ->> period)
    WHERE period IS NOT NULL
    AND period IN ('January', 'February', 'March', 'April', 'May', 'June',
                   'July', 'August', 'September', 'October', 'November', 'December');

    -- Get the number of updated records
    GET DIAGNOSTICS updated_count = ROW_COUNT;

    -- Log the number of updated records
    RAISE NOTICE 'Updated % invoice records with new period format', updated_count;
END $$;

-- Add constraint to ensure period follows YYYY-MM format
ALTER TABLE "public"."invoice"
ADD CONSTRAINT "invoice_period_format_check" CHECK (
	period IS NULL
	OR period ~ '^\d{4}-\d{2}$'
) NOT VALID;

-- Validate the constraint
ALTER TABLE "public"."invoice" VALIDATE CONSTRAINT "invoice_period_format_check";
