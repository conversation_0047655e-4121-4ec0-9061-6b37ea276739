-- Work Breakdown Structure Library Table - Standardized project templates from ICMS, UNIFORMAT, etc.
-- Include the library version number in the name
create table "public"."wbs_library" (
	wbs_library_id uuid primary key default gen_random_uuid(),
	name text not null,
	description text,
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null
);

comment on table "public"."wbs_library" is 'Work breakdown structure library containing standardized cost lines for projects.';

-- Create trigger on wbs_library to call the update function before any update
create trigger update_updated_at before
update on public.wbs_library for each row
execute function public.update_updated_at_column ();

alter table wbs_library enable row level security;

-- Grant access to service_role
grant
select
,
	insert,
update,
delete,
references,
trigger,
truncate on public.wbs_library to service_role;

-- WBS library policies
create policy "Users can view WBS library" on wbs_library for
select
	to authenticated using (true);

-- Only service_role can manage WBS library
create policy "Service role can insert WBS library" on wbs_library for insert to service_role
with
	check (true);

create policy "Service role can update WBS library" on wbs_library
for update
	to service_role using (true);

create policy "Service role can delete WBS library" on wbs_library for delete to service_role using (true);
