create extension if not exists "pgjwt"
with
	schema "extensions";

drop policy "Users can view custom WBS library item they have access to" on "public"."wbs_library_item";

drop policy "Users can view standard WBS library item" on "public"."wbs_library_item";

drop policy "Admins and the invitee can update invites" on "public"."invite";

drop policy "Users can create organizations" on "public"."organization";

drop policy "Users can create vendors at levels they have admin access to" on "public"."vendor";

create policy "Users can view WBS library items" on "public"."wbs_library_item" as permissive for
select
	to authenticated using (
		(
			(item_type = 'Standard'::wbs_item_type)
			OR (
				(item_type = 'Custom'::wbs_item_type)
				AND (
					current_user_has_entity_access ('client'::entity_type, client_id)
					OR (
						(project_id IS NOT NULL)
						AND current_user_has_entity_access ('project'::entity_type, project_id)
					)
				)
			)
		)
	);

create policy "Admins and the invitee can update invites" on "public"."invite" as permissive
for update
	to authenticated using (
		(
			(
				(
					resource_type = 'organization'::invite_resource_type
				)
				AND current_user_has_entity_role (
					'organization'::entity_type,
					resource_id,
					'admin'::membership_role
				)
			)
			OR (
				(resource_type = 'client'::invite_resource_type)
				AND current_user_has_entity_role (
					'client'::entity_type,
					resource_id,
					'admin'::membership_role
				)
			)
			OR (
				(resource_type = 'project'::invite_resource_type)
				AND can_modify_project (resource_id)
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						profile p
					WHERE
						(
							(
								p.user_id = (
									SELECT
										auth.uid () AS uid
								)
							)
							AND (p.email = invite.invitee_email)
						)
				)
			)
		)
	)
with
	check (
		(
			(
				(
					resource_type = 'organization'::invite_resource_type
				)
				AND current_user_has_entity_role (
					'organization'::entity_type,
					resource_id,
					'admin'::membership_role
				)
			)
			OR (
				(resource_type = 'client'::invite_resource_type)
				AND current_user_has_entity_role (
					'client'::entity_type,
					resource_id,
					'admin'::membership_role
				)
			)
			OR (
				(resource_type = 'project'::invite_resource_type)
				AND can_modify_project (resource_id)
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						profile p
					WHERE
						(
							(
								p.user_id = (
									SELECT
										auth.uid () AS uid
								)
							)
							AND (p.email = invite.invitee_email)
						)
				)
			)
		)
	);

create policy "Users can create organizations" on "public"."organization" as permissive for insert to authenticated
with
	check (
		(
			created_by_user_id = (
				SELECT
					auth.uid () AS uid
			)
		)
	);

create policy "Users can create vendors at levels they have admin access to" on "public"."vendor" as permissive for insert to authenticated
with
	check (
		(
			(
				(
					(org_id IS NOT NULL)
					AND current_user_has_entity_role (
						'organization'::entity_type,
						org_id,
						'admin'::membership_role
					)
				)
				OR (
					(client_id IS NOT NULL)
					AND current_user_has_entity_role (
						'client'::entity_type,
						client_id,
						'admin'::membership_role
					)
				)
				OR (
					(project_id IS NOT NULL)
					AND current_user_has_entity_role (
						'project'::entity_type,
						project_id,
						'editor'::membership_role
					)
				)
			)
			AND (
				created_by_user_id = (
					SELECT
						auth.uid () AS uid
				)
			)
		)
	);
