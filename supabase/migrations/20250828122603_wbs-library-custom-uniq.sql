ALTER TABLE public.wbs_library_item
DROP CONSTRAINT IF EXISTS wbs_library_item_wbs_library_id_code_key;

CREATE UNIQUE INDEX wbs_library_item_standard_uq ON public.wbs_library_item (wbs_library_id, code)
WHERE
	item_type = 'Standard';

CREATE UNIQUE INDEX wbs_library_item_project_uq ON public.wbs_library_item (project_id, code)
WHERE
	item_type = 'Custom'
	AND project_id IS NOT NULL;

CREATE UNIQUE INDEX wbs_library_item_client_uq ON public.wbs_library_item (client_id, code)
WHERE
	item_type = 'Custom'
	AND project_id IS NULL;
