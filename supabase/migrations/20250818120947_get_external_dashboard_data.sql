set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_external_dashboard_data () RETURNS json LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	current_user_id uuid := auth.uid();
	external_projects_data json;
	external_clients_data json;
BEGIN
	-- Ensure user is authenticated
	IF current_user_id IS NULL THEN
		RAISE EXCEPTION 'User ID cannot be null';
	END IF;

	-- Get projects where user has direct access but is not an organization member
	SELECT json_agg(
		json_build_object(
			'project_id', project_data.project_id,
			'name', project_data.name,
			'description', project_data.description,
			'created_at', project_data.created_at,
			'updated_at', project_data.updated_at,
			'client', json_build_object(
				'name', project_data.client_name,
				'organization', json_build_object(
					'name', project_data.org_name
				)
			),
			'is_project_admin', public.has_entity_role(current_user_id, 'project', project_data.project_id, 'admin'),
			'is_client_admin', public.has_entity_role(current_user_id, 'client', project_data.client_id, 'admin'),
			'is_org_admin', public.has_entity_role(current_user_id, 'organization', project_data.org_id, 'admin'),
			'is_external_access', true
		)
	) INTO external_projects_data
	FROM (
		SELECT DISTINCT p.project_id, p.name, p.description, p.created_at, p.updated_at,
			   c.client_id, c.name as client_name, o.org_id, o.name as org_name
		FROM public.project p
		INNER JOIN public.client c ON c.client_id = p.client_id
		INNER JOIN public.organization o ON o.org_id = c.org_id
		INNER JOIN public.membership m ON (
			(m.entity_type = 'project' AND m.entity_id = p.project_id) OR
			(m.entity_type = 'client' AND m.entity_id = c.client_id)
		)
		WHERE m.user_id = current_user_id
		-- Exclude projects where user is an organization member
		AND NOT EXISTS (
			SELECT 1 FROM public.membership om
			WHERE om.user_id = current_user_id
			AND om.entity_type = 'organization'
			AND om.entity_id = o.org_id
		)
		ORDER BY p.updated_at DESC
		LIMIT 5
	) project_data;

	-- Get clients where user has direct access but is not an organization member
	SELECT json_agg(
		json_build_object(
			'client_id', client_data.client_id,
			'name', client_data.name,
			'description', client_data.description,
			'logo_url', client_data.logo_url,
			'client_url', client_data.client_url,
			'internal_url', client_data.internal_url,
			'internal_url_description', client_data.internal_url_description,
			'org_id', client_data.org_id,
			'created_at', client_data.created_at,
			'updated_at', client_data.updated_at,
			'created_by_user_id', client_data.created_by_user_id,
			'organization', json_build_object(
				'name', client_data.org_name
			),
			'project_count', (
				SELECT COUNT(*)
				FROM public.project p2
				WHERE p2.client_id = client_data.client_id
				AND public.has_entity_access(current_user_id, 'project'::public.entity_type, p2.project_id)
			),
			'is_client_admin', public.has_entity_role(current_user_id, 'client', client_data.client_id, 'admin'),
			'is_org_admin', public.has_entity_role(current_user_id, 'organization', client_data.org_id, 'admin'),
			'is_external_access', true
		)
	) INTO external_clients_data
	FROM (
		SELECT DISTINCT c.client_id, c.name, c.description, c.logo_url, c.client_url, c.internal_url,
			   c.internal_url_description, c.org_id, c.created_at, c.updated_at, c.created_by_user_id,
			   o.name as org_name
		FROM public.client c
		INNER JOIN public.organization o ON o.org_id = c.org_id
		INNER JOIN public.membership m ON m.entity_type = 'client' AND m.entity_id = c.client_id
		WHERE m.user_id = current_user_id
		-- Exclude clients where user is an organization member
		AND NOT EXISTS (
			SELECT 1 FROM public.membership om
			WHERE om.user_id = current_user_id
			AND om.entity_type = 'organization'
			AND om.entity_id = o.org_id
		)
		ORDER BY c.updated_at DESC
		LIMIT 5
	) client_data;

	-- Return combined external data
	RETURN json_build_object(
		'external_projects', COALESCE(external_projects_data, '[]'::json),
		'external_clients', COALESCE(external_clients_data, '[]'::json)
	);
END;
$function$;
