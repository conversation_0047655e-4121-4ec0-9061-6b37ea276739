-- Add function to get organization by name
CREATE OR REPLACE FUNCTION public.get_organization_by_name (org_name_param TEXT) RETURNS TABLE (
	org_id UUID,
	name TEXT,
	description TEXT,
	logo_url TEXT,
	created_by_user_id UUID,
	created_at TIMESTAMPTZ,
	updated_at TIMESTAMPTZ
) LANGUAGE plpgsql
SET
	search_path = '' AS $$ BEGIN RETURN QUERY
SELECT o.org_id,
	o.name,
	o.description,
	o.logo_url,
	o.created_by_user_id,
	o.created_at,
	o.updated_at
FROM public.organization o
WHERE o.name = org_name_param;
END;
$$;
