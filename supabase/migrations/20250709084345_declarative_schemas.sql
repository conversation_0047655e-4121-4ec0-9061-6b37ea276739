drop trigger if exists "update_approved_changes_updated_at" on "public"."approved_changes";

drop trigger if exists "update_updated_at_blc" on "public"."budget_line_item_current";

drop trigger if exists "update_risk_register_updated_at" on "public"."risk_register";

drop policy "Organization members can insert a clients" on "public"."client";

drop policy "Organization members can update clients" on "public"."client";

drop policy "Project Editors and Owners can delete gateway checklist item" on "public"."gateway_checklist_item";

drop policy "Project Editors and Owners can insert gateway checklist item" on "public"."gateway_checklist_item";

drop policy "Project Editors and Owners can update gateway checklist item" on "public"."gateway_checklist_item";

drop policy "Users can view memberships they have access to" on "public"."membership";

drop policy "System can insert WBS library item audit records" on "public"."wbs_library_item_audit";

drop policy "Users can view custom WBS library item audit for accessible ite" on "public"."wbs_library_item_audit";

drop policy "Users can view standard WBS library item audit records" on "public"."wbs_library_item_audit";

drop policy "Project editors can update budget line item" on "public"."budget_line_item_current";

drop policy "Project editors can update budget snapshot" on "public"."budget_snapshot";

drop policy "Project editors can update budget snapshot line item" on "public"."budget_snapshot_line_item";

drop policy "Users can view gateway checklist item audit for accessible proj" on "public"."gateway_checklist_item_audit";

drop policy "Users can insert gateway stage info for projects they can edit" on "public"."project_gateway_stage_info";

drop policy "Users can view project gateway stage info audit for accessible " on "public"."project_gateway_stage_info_audit";

drop policy "Project Editors and Owners can update project stage" on "public"."project_stage";

alter table "public"."wbs_library_item"
drop constraint "wbs_library_item_client_id_fkey";

alter table "public"."wbs_library_item"
drop constraint "wbs_library_item_parent_item_id_fkey";

alter table "public"."wbs_library_item"
drop constraint "wbs_library_item_project_id_fkey";

drop function if exists "public"."compare_budget_snapshots" (p_snapshot_id_1 uuid, p_snapshot_id_2 uuid);

drop index if exists "public"."approved_changes_original_risk_idx";

drop index if exists "public"."approved_changes_project_idx";

drop index if exists "public"."idx_approved_changes_audit_approved_change_id";

drop index if exists "public"."idx_approved_changes_audit_changed_at";

drop index if exists "public"."idx_approved_changes_audit_changed_by";

drop index if exists "public"."idx_approved_changes_audit_project_id";

drop index if exists "public"."idx_budget_line_item_audit_changed_at";

drop index if exists "public"."idx_budget_line_item_audit_changed_by";

drop index if exists "public"."idx_budget_line_item_audit_operation_type";

drop index if exists "public"."idx_budget_line_item_audit_project_id";

drop index if exists "public"."idx_budget_line_item_current_project_id";

drop index if exists "public"."idx_budget_line_item_current_wbs_library_item_id";

drop index if exists "public"."idx_budget_snapshot_line_item_wbs_library_item_id";

drop index if exists "public"."idx_gateway_checklist_item_audit_changed_at";

drop index if exists "public"."idx_gateway_checklist_item_audit_changed_by";

drop index if exists "public"."idx_gateway_checklist_item_audit_project_stage_id";

drop index if exists "public"."idx_project_gateway_stage_info_audit_changed_at";

drop index if exists "public"."idx_project_gateway_stage_info_audit_changed_by";

drop index if exists "public"."idx_project_gateway_stage_info_audit_project_stage_id";

drop index if exists "public"."idx_project_stage_audit_changed_at";

drop index if exists "public"."idx_project_stage_audit_changed_by";

drop index if exists "public"."idx_project_stage_audit_operation_type";

drop index if exists "public"."idx_project_stage_audit_project_id";

drop index if exists "public"."idx_risk_register_audit_changed_at";

drop index if exists "public"."idx_risk_register_audit_changed_by";

drop index if exists "public"."idx_risk_register_audit_project_id";

drop index if exists "public"."idx_risk_register_audit_risk_id";

drop index if exists "public"."idx_wbs_library_item_audit_changed_at";

drop index if exists "public"."idx_wbs_library_item_audit_changed_by";

drop index if exists "public"."idx_wbs_library_item_audit_client_id";

drop index if exists "public"."idx_wbs_library_item_audit_operation_type";

drop index if exists "public"."idx_wbs_library_item_audit_project_id";

drop index if exists "public"."idx_wbs_library_item_audit_wbs_library_item_id";

drop index if exists "public"."risk_register_project_idx";

CREATE INDEX approved_changes_audit_approved_change_id_idx ON public.approved_changes_audit USING btree (approved_change_id);

CREATE INDEX approved_changes_audit_changed_at_idx ON public.approved_changes_audit USING btree (changed_at);

CREATE INDEX approved_changes_audit_changed_by_idx ON public.approved_changes_audit USING btree (changed_by);

CREATE INDEX approved_changes_audit_project_id_idx ON public.approved_changes_audit USING btree (project_id);

CREATE INDEX approved_changes_original_risk_id_idx ON public.approved_changes USING btree (original_risk_id);

CREATE INDEX approved_changes_project_id_idx ON public.approved_changes USING btree (project_id);

CREATE INDEX budget_line_item_audit_budget_line_item_id_idx ON public.budget_line_item_audit USING btree (budget_line_item_id);

CREATE INDEX budget_line_item_audit_changed_at_idx ON public.budget_line_item_audit USING btree (changed_at);

CREATE INDEX budget_line_item_audit_changed_by_idx ON public.budget_line_item_audit USING btree (changed_by);

CREATE INDEX budget_line_item_audit_operation_type_idx ON public.budget_line_item_audit USING btree (operation_type);

CREATE INDEX budget_line_item_audit_project_id_idx ON public.budget_line_item_audit USING btree (project_id);

CREATE INDEX budget_line_item_current_project_id_idx ON public.budget_line_item_current USING btree (project_id);

CREATE UNIQUE INDEX budget_line_item_current_project_id_wbs_library_item_id_key ON public.budget_line_item_current USING btree (project_id, wbs_library_item_id);

CREATE INDEX budget_line_item_current_wbs_library_item_id_idx ON public.budget_line_item_current USING btree (wbs_library_item_id);

CREATE INDEX budget_snapshot_line_item_budget_snapshot_id_idx ON public.budget_snapshot_line_item USING btree (budget_snapshot_id);

CREATE INDEX budget_snapshot_line_item_wbs_library_item_id_idx ON public.budget_snapshot_line_item USING btree (wbs_library_item_id);

CREATE INDEX gateway_checklist_item_audit_changed_at_idx ON public.gateway_checklist_item_audit USING btree (changed_at);

CREATE INDEX gateway_checklist_item_audit_changed_by_idx ON public.gateway_checklist_item_audit USING btree (changed_by);

CREATE INDEX gateway_checklist_item_audit_gateway_checklist_item_id_idx ON public.gateway_checklist_item_audit USING btree (gateway_checklist_item_id);

CREATE INDEX project_gateway_stage_info_audit_changed_at_idx ON public.project_gateway_stage_info_audit USING btree (changed_at);

CREATE INDEX project_gateway_stage_info_audit_changed_by_idx ON public.project_gateway_stage_info_audit USING btree (changed_by);

CREATE INDEX project_gateway_stage_info_audit_project_gateway_stage_info_id_ ON public.project_gateway_stage_info_audit USING btree (project_gateway_stage_info_id);

CREATE INDEX project_stage_audit_changed_at_idx ON public.project_stage_audit USING btree (changed_at);

CREATE INDEX project_stage_audit_changed_by_idx ON public.project_stage_audit USING btree (changed_by);

CREATE INDEX project_stage_audit_operation_type_idx ON public.project_stage_audit USING btree (operation_type);

CREATE INDEX project_stage_audit_project_id_idx ON public.project_stage_audit USING btree (project_id);

CREATE INDEX project_stage_audit_project_stage_id_idx ON public.project_stage_audit USING btree (project_stage_id);

CREATE UNIQUE INDEX project_stage_project_id_stage_order_key ON public.project_stage USING btree (project_id, stage_order);

CREATE INDEX risk_register_audit_changed_at_idx ON public.risk_register_audit USING btree (changed_at);

CREATE INDEX risk_register_audit_changed_by_idx ON public.risk_register_audit USING btree (changed_by);

CREATE INDEX risk_register_audit_project_id_idx ON public.risk_register_audit USING btree (project_id);

CREATE INDEX risk_register_audit_risk_id_idx ON public.risk_register_audit USING btree (risk_id);

CREATE INDEX risk_register_project_id_idx ON public.risk_register USING btree (project_id);

CREATE INDEX risk_register_status_idx ON public.risk_register USING btree (status);

CREATE INDEX risk_register_wbs_library_item_id_idx ON public.risk_register USING btree (wbs_library_item_id);

CREATE INDEX wbs_library_item_audit_changed_at_idx ON public.wbs_library_item_audit USING btree (changed_at);

CREATE INDEX wbs_library_item_audit_changed_by_idx ON public.wbs_library_item_audit USING btree (changed_by);

CREATE INDEX wbs_library_item_audit_client_id_idx ON public.wbs_library_item_audit USING btree (client_id);

CREATE INDEX wbs_library_item_audit_operation_type_idx ON public.wbs_library_item_audit USING btree (operation_type);

CREATE INDEX wbs_library_item_audit_project_id_idx ON public.wbs_library_item_audit USING btree (project_id);

CREATE INDEX wbs_library_item_audit_wbs_library_item_id_idx ON public.wbs_library_item_audit USING btree (wbs_library_item_id);

CREATE INDEX wbs_library_item_parent_item_id_idx ON public.wbs_library_item USING btree (parent_item_id);

CREATE UNIQUE INDEX wbs_library_item_wbs_library_id_code_key ON public.wbs_library_item USING btree (wbs_library_id, code);

CREATE INDEX wbs_library_item_wbs_library_id_idx ON public.wbs_library_item USING btree (wbs_library_id);

CREATE UNIQUE INDEX wbs_library_name_key ON public.wbs_library USING btree (name);

alter table "public"."budget_line_item_current"
add constraint "budget_line_item_current_project_id_wbs_library_item_id_key" UNIQUE using index "budget_line_item_current_project_id_wbs_library_item_id_key";

alter table "public"."project_stage"
add constraint "project_stage_project_id_stage_order_key" UNIQUE using index "project_stage_project_id_stage_order_key";

alter table "public"."wbs_library"
add constraint "wbs_library_name_key" UNIQUE using index "wbs_library_name_key";

alter table "public"."wbs_library_item"
add constraint "wbs_library_item_wbs_library_id_code_key" UNIQUE using index "wbs_library_item_wbs_library_id_code_key";

alter table "public"."wbs_library_item"
add constraint "wbs_library_item_client_id_fkey" FOREIGN KEY (client_id) REFERENCES client (client_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."wbs_library_item" validate constraint "wbs_library_item_client_id_fkey";

alter table "public"."wbs_library_item"
add constraint "wbs_library_item_parent_item_id_fkey" FOREIGN KEY (parent_item_id) REFERENCES wbs_library_item (wbs_library_item_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."wbs_library_item" validate constraint "wbs_library_item_parent_item_id_fkey";

alter table "public"."wbs_library_item"
add constraint "wbs_library_item_project_id_fkey" FOREIGN KEY (project_id) REFERENCES project (project_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."wbs_library_item" validate constraint "wbs_library_item_project_id_fkey";

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.accept_invite (token_param character) RETURNS json LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE 
	v_invite public.invite %rowtype;
	v_user_id uuid;
	v_entity_type public.entity_type;
	v_entity_id uuid;
	v_role public.membership_role;
	v_resource_type text;
BEGIN 
	-- Get the current user ID
	v_user_id := auth.uid();
	IF v_user_id IS NULL THEN 
		RAISE EXCEPTION 'Not authenticated';
	END IF;
	
	-- Get the invite
	SELECT * INTO v_invite
	FROM public.invite
	WHERE token_hash = token_param
	AND status = 'pending'
	AND expires_at > now();
	
	IF NOT FOUND THEN 
		RAISE EXCEPTION 'Invalid or expired invite token';
	END IF;
	
	-- Store resource_type as text to avoid casting issues
	v_resource_type := v_invite.resource_type::text;
	
	IF v_resource_type = 'organization' THEN
		v_entity_type := 'organization'::public.entity_type;
		v_entity_id := v_invite.resource_id;
		IF v_invite.role = 'member' THEN
			v_role := 'viewer'::public.membership_role;
		ELSE
			v_role := v_invite.role::public.membership_role;
		END IF;
	ELSIF v_resource_type = 'client' THEN
		v_entity_type := 'client'::public.entity_type;
		v_entity_id := v_invite.resource_id;
		v_role := v_invite.role::public.membership_role;
	ELSIF v_resource_type = 'project' THEN
		v_entity_type := 'project'::public.entity_type;
		v_entity_id := v_invite.resource_id;
		v_role := v_invite.role::public.membership_role;
	ELSE
		RAISE EXCEPTION 'Invalid resource type';
	END IF;
	
	-- Add membership
	INSERT INTO public.membership(user_id, role, entity_type, entity_id)
	VALUES (v_user_id, v_role, v_entity_type, v_entity_id)
	ON CONFLICT (entity_type, entity_id, user_id) DO NOTHING;
	
	-- Update invite status
	UPDATE public.invite
	SET status = 'accepted'::public.invite_status,
		updated_at = now(),
		updated_by = v_user_id
	WHERE invite_id = v_invite.invite_id;
	
	RETURN json_build_object(
		'success', true,
		'message', 'Invite accepted successfully',
		'resource_type', v_invite.resource_type,
		'resource_id', v_invite.resource_id
	);
	
EXCEPTION
	WHEN OTHERS THEN 
		RETURN json_build_object('success', false, 'message', SQLERRM);
END;
$function$;

CREATE OR REPLACE FUNCTION public.apply_pending_invites () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_invite public.invite %rowtype;
	v_entity_type public.entity_type;
	v_entity_id uuid;
	v_role public.membership_role;
	v_resource_type text;
BEGIN
	-- Loop through all pending invites for this email
	BEGIN
		FOR v_invite IN (
			SELECT *
			FROM public.invite
			WHERE lower(invitee_email) = lower(NEW.email)
				AND status = 'pending'
				AND expires_at > now()
		) LOOP
			BEGIN
				-- Store resource_type as text to avoid casting issues
				v_resource_type := v_invite.resource_type::text;

				IF v_resource_type = 'organization' THEN
					v_entity_type := 'organization'::public.entity_type;
					v_entity_id := v_invite.resource_id;
					IF v_invite.role = 'member' THEN
						v_role := 'viewer'::public.membership_role;
					ELSE
						v_role := v_invite.role::public.membership_role;
					END IF;
				ELSIF v_resource_type = 'client' THEN
					v_entity_type := 'client'::public.entity_type;
					v_entity_id := v_invite.resource_id;
					v_role := v_invite.role::public.membership_role;
				ELSIF v_resource_type = 'project' THEN
					v_entity_type := 'project'::public.entity_type;
					v_entity_id := v_invite.resource_id;
					v_role := v_invite.role::public.membership_role;
				ELSE
					CONTINUE;
				END IF;

				BEGIN
					INSERT INTO public.membership(user_id, role, entity_type, entity_id)
					VALUES (NEW.user_id, v_role, v_entity_type, v_entity_id)
					ON CONFLICT (entity_type, entity_id, user_id) DO NOTHING;

					UPDATE public.invite
					SET status = 'accepted'::public.invite_status,
						updated_at = now(),
						updated_by = NEW.user_id
					WHERE invite_id = v_invite.invite_id;
				EXCEPTION
					WHEN OTHERS THEN
						-- Log error but continue processing other invites
						RAISE NOTICE 'Error processing invite %: %', v_invite.invite_id, SQLERRM;
				END;
			EXCEPTION
				WHEN OTHERS THEN
					-- Log error but continue processing other invites
					RAISE NOTICE 'Error mapping invite %: %', v_invite.invite_id, SQLERRM;
			END;
		END LOOP;

		BEGIN
			UPDATE public.invite
			SET status = 'expired'::public.invite_status,
				updated_at = now(),
				updated_by = NEW.user_id
			WHERE lower(invitee_email) = lower(NEW.email)
				AND status = 'pending'
				AND expires_at <= now();
		EXCEPTION
			WHEN OTHERS THEN
				-- Log error but allow user creation to continue
				RAISE NOTICE 'Error expiring outdated invites: %', SQLERRM;
		END;
	EXCEPTION
		WHEN OTHERS THEN
			-- Log error but allow user creation to continue
			RAISE NOTICE 'Error in apply_pending_invites: %', SQLERRM;
	END;

	RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_budget_line_item_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        -- This handles cases like the generate_demo_budget_data() function
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, old_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.budget_line_item_id, OLD.project_id, OLD.wbs_library_item_id, OLD.quantity, OLD.unit,
            OLD.material_rate, OLD.labor_rate, OLD.productivity_per_hour, OLD.unit_rate_manual_override,
            OLD.unit_rate, OLD.factor, OLD.remarks, OLD.cost_certainty, OLD.design_certainty, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.budget_line_item_id, NEW.project_id, NEW.wbs_library_item_id, NEW.quantity, NEW.unit,
            NEW.material_rate, NEW.labor_rate, NEW.productivity_per_hour, NEW.unit_rate_manual_override,
            NEW.unit_rate, NEW.factor, NEW.remarks, NEW.cost_certainty, NEW.design_certainty, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, new_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.budget_line_item_id, NEW.project_id, NEW.wbs_library_item_id, NEW.quantity, NEW.unit,
            NEW.material_rate, NEW.labor_rate, NEW.productivity_per_hour, NEW.unit_rate_manual_override,
            NEW.unit_rate, NEW.factor, NEW.remarks, NEW.cost_certainty, NEW.design_certainty, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_wbs_library_item_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        -- This handles cases like seed data or system operations
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, old_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.wbs_library_item_id, OLD.wbs_library_id, OLD.level, OLD.in_level_code, OLD.parent_item_id,
            OLD.code, OLD.description, OLD.cost_scope, OLD.item_type, OLD.client_id, OLD.project_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.wbs_library_item_id, NEW.wbs_library_id, NEW.level, NEW.in_level_code, NEW.parent_item_id,
            NEW.code, NEW.description, NEW.cost_scope, NEW.item_type, NEW.client_id, NEW.project_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, new_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.wbs_library_item_id, NEW.wbs_library_id, NEW.level, NEW.in_level_code, NEW.parent_item_id,
            NEW.code, NEW.description, NEW.cost_scope, NEW.item_type, NEW.client_id, NEW.project_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.calculate_unit_item_cost (
	p_material_rate numeric,
	p_labor_rate numeric,
	p_productivity_per_hour numeric
) RETURNS numeric LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
	v_cost NUMERIC;
BEGIN
	-- Simple calculation for now - can be enhanced later
	v_cost := COALESCE(p_material_rate, 0);

	IF p_labor_rate IS NOT NULL
	AND p_productivity_per_hour IS NOT NULL
	AND p_productivity_per_hour > 0 THEN
		v_cost := v_cost + COALESCE(p_labor_rate, 0) / COALESCE(p_productivity_per_hour, 1);
	END IF;

	RETURN v_cost;
END;
$function$;

CREATE OR REPLACE FUNCTION public.can_access_client (client_id_param uuid) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
	RETURN public.current_user_has_entity_access('client', client_id_param);
END;
$function$;

CREATE OR REPLACE FUNCTION public.can_access_project (project_id_param uuid) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
	RETURN public.current_user_has_entity_access('project', project_id_param);
END;
$function$;

CREATE OR REPLACE FUNCTION public.can_modify_client (client_id_param uuid) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
	RETURN public.current_user_has_entity_role('client', client_id_param, 'editor');
END;
$function$;

CREATE OR REPLACE FUNCTION public.can_modify_client_wbs (client_id_param uuid) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
	RETURN public.current_user_has_entity_role('client', client_id_param, 'admin');
END;
$function$;

CREATE OR REPLACE FUNCTION public.can_modify_project (project_id_param uuid) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
	RETURN public.current_user_has_entity_role('project', project_id_param, 'editor');
END;
$function$;

CREATE OR REPLACE FUNCTION public.check_membership_redundancy () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE 
	ancestor_role public.membership_role;
BEGIN 
	-- Check if user already has equal or higher role through an ancestor
	IF NEW.entity_type = 'project' THEN 
		-- Check client level
		SELECT public.get_effective_role(NEW.user_id, 'client', p.client_id) INTO ancestor_role
		FROM public.project p
		WHERE p.project_id = NEW.entity_id;
		
		IF ancestor_role = 'admin' OR ancestor_role = 'owner' THEN 
			RAISE EXCEPTION 'User already has admin access to this project through client-level permissions';
		END IF;
		
		-- Check organization level
		SELECT public.get_effective_role(NEW.user_id, 'organization', c.org_id) INTO ancestor_role
		FROM public.project p
		JOIN public.client c ON p.client_id = c.client_id
		WHERE p.project_id = NEW.entity_id;
		
		IF ancestor_role = 'admin' THEN 
			RAISE EXCEPTION 'User already has admin access to this project through organization-level permissions';
		END IF;
		
	ELSIF NEW.entity_type = 'client' THEN 
		-- Check organization level
		SELECT public.get_effective_role(NEW.user_id, 'organization', c.org_id) INTO ancestor_role
		FROM public.client c
		WHERE c.client_id = NEW.entity_id;
		
		IF ancestor_role = 'admin' THEN 
			RAISE EXCEPTION 'User already has admin access to this client through organization-level permissions';
		END IF;
	END IF;
	
	RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.compare_budget_snapshots (p_snapshot_id_1 uuid, p_snapshot_id_2 uuid) RETURNS TABLE (
	wbs_library_item_id uuid,
	snapshot_1_quantity numeric,
	snapshot_1_cost numeric,
	snapshot_1_factor numeric,
	snapshot_2_quantity numeric,
	snapshot_2_cost numeric,
	snapshot_2_factor numeric,
	quantity_diff numeric,
	cost_diff numeric,
	factor_diff numeric,
	percent_change numeric
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
	RETURN QUERY
	SELECT
		COALESCE(s1.wbs_library_item_id, s2.wbs_library_item_id) AS wbs_library_item_id,
		s1.quantity AS snapshot_1_quantity,
		s1.unit_rate AS snapshot_1_cost,
		s1.factor AS snapshot_1_factor,
		s2.quantity AS snapshot_2_quantity,
		s2.unit_rate AS snapshot_2_cost,
		s2.factor AS snapshot_2_factor,
		COALESCE(s2.quantity, 0) - COALESCE(s1.quantity, 0) AS quantity_diff,
		COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0) AS cost_diff,
		COALESCE(s2.factor, 0) - COALESCE(s1.factor, 0) AS factor_diff,
		CASE
			WHEN COALESCE(s1.unit_rate, 0) = 0 THEN NULL
			ELSE ((COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0)) / s1.unit_rate) * 100
		END AS percent_change
	FROM public.budget_snapshot_line_item s1
	FULL OUTER JOIN public.budget_snapshot_line_item s2
		ON s1.wbs_library_item_id = s2.wbs_library_item_id
	WHERE s1.budget_snapshot_id = p_snapshot_id_1
	AND s2.budget_snapshot_id = p_snapshot_id_2;
END;
$function$;

CREATE OR REPLACE FUNCTION public.complete_project_stage (
	p_project_stage_id uuid,
	p_completion_notes text DEFAULT NULL::text
) RETURNS uuid LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_snapshot_id UUID;
	v_project_id UUID;
	v_is_ready BOOLEAN;
BEGIN
	-- Get project_id for permission check
	SELECT project_id INTO v_project_id
	FROM public.project_stage
	WHERE project_stage_id = p_project_stage_id;
	
	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Project stage not found';
	END IF;
	
	-- Check if user can modify this project
	IF NOT public.can_modify_project(v_project_id) THEN
		RAISE EXCEPTION 'Insufficient permissions to complete this project stage';
	END IF;
	
	-- Check if stage is ready for completion
	SELECT public.is_stage_ready_for_completion(p_project_stage_id) INTO v_is_ready;
	
	IF NOT v_is_ready THEN
		RAISE EXCEPTION 'Project stage has incomplete checklist items and cannot be completed';
	END IF;
	
	-- Update the stage
	UPDATE public.project_stage
	SET 
		status = 'Completed',
		completion_date = now(),
		completion_notes = p_completion_notes,
		updated_at = now()
	WHERE project_stage_id = p_project_stage_id;
	
	-- Create a budget snapshot
SELECT public.create_budget_snapshot(p_project_stage_id, p_completion_notes) INTO v_snapshot_id;
RETURN v_snapshot_id;
END;
$function$;

CREATE OR REPLACE FUNCTION public.create_budget_snapshot (
	p_project_stage_id uuid,
	p_freeze_reason text DEFAULT NULL::text
) RETURNS uuid LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE 
	v_project_id UUID;
	v_snapshot_id UUID;
	v_item RECORD;
BEGIN 
	-- Get the project_id from the stage
	SELECT project_id INTO v_project_id
	FROM public.project_stage
	WHERE project_stage_id = p_project_stage_id;
	
	IF v_project_id IS NULL THEN 
		RAISE EXCEPTION 'Project stage not found';
	END IF;
	
	INSERT INTO public.budget_snapshot (
		project_stage_id,
		freeze_date,
		freeze_reason,
		created_by_user_id
	)
	VALUES (
		p_project_stage_id,
		now(),
		p_freeze_reason,
		auth.uid()
	)
	RETURNING budget_snapshot_id INTO v_snapshot_id;
	
	FOR v_item IN (
		SELECT *
		FROM public.budget_line_item_current
		WHERE project_id = v_project_id
	) LOOP
		INSERT INTO public.budget_snapshot_line_item (
			budget_snapshot_id,
			wbs_library_item_id,
			quantity,
			unit,
			material_rate,
			labor_rate,
			productivity_per_hour,
			unit_rate_manual_override,
			unit_rate,
			factor,
			remarks,
			cost_certainty,
			design_certainty
		)
		VALUES (
			v_snapshot_id,
			v_item.wbs_library_item_id,
			v_item.quantity,
			v_item.unit,
			v_item.material_rate,
			v_item.labor_rate,
			v_item.productivity_per_hour,
			v_item.unit_rate_manual_override,
			v_item.unit_rate,
			v_item.factor,
			v_item.remarks,
			v_item.cost_certainty,
			v_item.design_certainty
		);
	END LOOP;
	
	RETURN v_snapshot_id;
END;
$function$;

CREATE OR REPLACE FUNCTION public.create_organization (
	name text,
	description text DEFAULT NULL::text,
	logo_url text DEFAULT NULL::text
) RETURNS json LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	new_org public.organization;
	user_id uuid := auth.uid();
BEGIN
	-- Ensure user is authenticated
	IF user_id IS NULL THEN
		RAISE EXCEPTION 'Not authenticated';
	END IF;

	-- Create organization
	INSERT INTO public.organization(name, description, logo_url, created_by_user_id)
	VALUES (name, description, logo_url, user_id)
	RETURNING * INTO new_org;

	-- Return JSON object
	RETURN json_build_object(
		'org_id', new_org.org_id,
		'name', new_org.name,
		'description', new_org.description,
		'logo_url', new_org.logo_url
	);
END;
$function$;

CREATE OR REPLACE FUNCTION public.current_user_has_entity_access (
	entity_type_param entity_type,
	entity_id_param uuid
) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$ 
BEGIN 
	RETURN public.has_entity_access(auth.uid(), entity_type_param, entity_id_param);
END;
$function$;

CREATE OR REPLACE FUNCTION public.current_user_has_entity_role (
	entity_type_param entity_type,
	entity_id_param uuid,
	min_role_param membership_role
) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
	RETURN public.has_entity_role(
		auth.uid(),
		entity_type_param,
		entity_id_param,
		min_role_param
	);
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_client_members (_client_name text) RETURNS TABLE (
	user_id uuid,
	email text,
	full_name text,
	avatar_url text,
	created_at timestamp with time zone,
	updated_at timestamp with time zone,
	role text,
	membership_id uuid,
	access_via text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
declare _client_id uuid;
_has_admin_access boolean;
begin -- Get the client_id for the client name
select client_id into _client_id
from public.client
where name = _client_name;
if _client_id is null then raise exception 'Client not found: %',
_client_name;
end if;
select public.current_user_has_entity_role('client', _client_id, 'admin') into _has_admin_access;
if not _has_admin_access then raise exception 'Access denied: must be a client admin';
end if;
return query
select *
from public.profiles_with_client_access(_client_name);
end;
$function$;

CREATE OR REPLACE FUNCTION public.get_clients_with_permissions (org_name_param text) RETURNS TABLE (
	client_id uuid,
	name text,
	description text,
	logo_url text,
	client_url text,
	internal_url text,
	internal_url_description text,
	org_id uuid,
	created_at timestamp with time zone,
	updated_at timestamp with time zone,
	created_by_user_id uuid,
	organization_name text,
	project_count bigint,
	is_client_admin boolean,
	is_org_admin boolean
) LANGUAGE sql SECURITY DEFINER
SET
	search_path TO '' AS $function$
SELECT c.client_id,
	c.name,
	c.description,
	c.logo_url,
	c.client_url,
	c.internal_url,
	c.internal_url_description,
	c.org_id,
	c.created_at,
	c.updated_at,
	c.created_by_user_id,
	o.name AS organization_name,
	(
		SELECT COUNT(*)
		FROM public.project p
		WHERE p.client_id = c.client_id
	) AS project_count,
	public.current_user_has_entity_role('client', c.client_id, 'admin') AS is_client_admin,
	public.current_user_has_entity_role('organization', c.org_id, 'admin') AS is_org_admin
FROM public.client c
	INNER JOIN public.organization o ON o.org_id = c.org_id
WHERE o.name = org_name_param
	AND public.current_user_has_entity_access('client'::public.entity_type, c.client_id)
ORDER BY c.name;
$function$;

CREATE OR REPLACE FUNCTION public.get_effective_role (
	user_id_param uuid,
	entity_type_param entity_type,
	entity_id_param uuid
) RETURNS membership_role LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	effective_role public.membership_role;
BEGIN
	-- Check for direct membership first
	SELECT role INTO effective_role
	FROM public.membership
	WHERE user_id = user_id_param
		AND entity_type = entity_type_param
		AND entity_id = entity_id_param;

	IF FOUND THEN
		RETURN effective_role;
	END IF;

	-- Check hierarchical access
	IF entity_type_param = 'project' THEN
		-- Check client level access
		SELECT m.role INTO effective_role
		FROM public.membership m
		JOIN public.project p ON p.project_id = entity_id_param
		WHERE m.user_id = user_id_param
			AND m.entity_type = 'client'
			AND m.entity_id = p.client_id;

		IF FOUND THEN
			RETURN effective_role;
		END IF;

		-- Check organization level access
		SELECT m.role INTO effective_role
		FROM public.membership m
		JOIN public.project p ON p.project_id = entity_id_param
		JOIN public.client c ON p.client_id = c.client_id
		WHERE m.user_id = user_id_param
			AND m.entity_type = 'organization'
			AND m.entity_id = c.org_id;

		IF FOUND THEN
			RETURN effective_role;
		END IF;

	ELSIF entity_type_param = 'client' THEN
		-- Check organization level access
		SELECT m.role INTO effective_role
		FROM public.membership m
		JOIN public.client c ON c.client_id = entity_id_param
		WHERE m.user_id = user_id_param
			AND m.entity_type = 'organization'
			AND m.entity_id = c.org_id;

		IF FOUND THEN
			RETURN effective_role;
		END IF;
	END IF;

	-- No access found
	RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_entity_ancestors (
	entity_type_param entity_type,
	entity_id_param uuid
) RETURNS TABLE (entity_type entity_type, entity_id uuid) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN 
	-- Return the entity itself
	RETURN QUERY SELECT entity_type_param, entity_id_param;
	
	-- Return ancestors based on entity type
	IF entity_type_param = 'project' THEN 
		-- Project -> Client -> Organization
		RETURN QUERY 
		SELECT 'client'::public.entity_type, p.client_id
		FROM public.project p
		WHERE p.project_id = entity_id_param;
		
		RETURN QUERY 
		SELECT 'organization'::public.entity_type, c.org_id
		FROM public.project p
		JOIN public.client c ON p.client_id = c.client_id
		WHERE p.project_id = entity_id_param;
		
	ELSIF entity_type_param = 'client' THEN 
		-- Client -> Organization
		RETURN QUERY 
		SELECT 'organization'::public.entity_type, c.org_id
		FROM public.client c
		WHERE c.client_id = entity_id_param;
	END IF;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_organization_by_name (org_name_param text) RETURNS TABLE (
	org_id uuid,
	name text,
	description text,
	logo_url text,
	created_by_user_id uuid,
	created_at timestamp with time zone,
	updated_at timestamp with time zone
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
	RETURN QUERY
	SELECT o.org_id, o.name, o.description, o.logo_url, o.created_by_user_id, o.created_at, o.updated_at
	FROM public.organization o
	WHERE o.name = org_name_param
	AND public.current_user_has_entity_access('organization'::public.entity_type, o.org_id);
END;
$function$;

CREATE OR REPLACE FUNCTION public.handle_new_user () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$ 
BEGIN
	INSERT INTO public.profile (user_id, email, full_name)
	VALUES (
		NEW.id,
		NEW.email,
		NEW.raw_user_meta_data->>'full_name'
	);
	RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.has_entity_access (
	user_id_param uuid,
	entity_type_param entity_type,
	entity_id_param uuid
) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$ 
BEGIN 
	RETURN EXISTS (
		SELECT 1
		FROM public.membership m
		JOIN LATERAL public.get_entity_ancestors(entity_type_param, entity_id_param) a ON TRUE
		WHERE m.user_id = user_id_param
			AND m.entity_type = a.entity_type
			AND m.entity_id = a.entity_id
	);
END;
$function$;

CREATE OR REPLACE FUNCTION public.has_entity_role (
	user_id_param uuid,
	entity_type_param entity_type,
	entity_id_param uuid,
	min_role_param membership_role
) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	user_role public.membership_role;
BEGIN
	user_role := public.get_effective_role(
		user_id_param,
		entity_type_param,
		entity_id_param
	);

	IF user_role IS NULL THEN
		RETURN FALSE;
	END IF;

	-- Role hierarchy: viewer < editor < admin < owner
	CASE min_role_param
		WHEN 'viewer' THEN
			RETURN user_role IN ('viewer', 'editor', 'admin', 'owner');
		WHEN 'editor' THEN
			RETURN user_role IN ('editor', 'admin', 'owner');
		WHEN 'admin' THEN
			RETURN user_role IN ('admin', 'owner');
		WHEN 'owner' THEN
			RETURN user_role = 'owner';
		ELSE
			RETURN FALSE;
	END CASE;
END;
$function$;

CREATE OR REPLACE FUNCTION public.is_org_admin_for_project (project_id_param uuid) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	org_id_var uuid;
BEGIN
	SELECT c.org_id INTO org_id_var
	FROM public.project p
	JOIN public.client c ON p.client_id = c.client_id
	WHERE p.project_id = project_id_param;

	IF org_id_var IS NOT NULL THEN
		RETURN public.current_user_has_entity_role('organization', org_id_var, 'admin');
	END IF;

	RETURN FALSE;
END;
$function$;

CREATE OR REPLACE FUNCTION public.is_project_owner (project_id_param uuid) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	client_id_var uuid;
	org_id_var uuid;
BEGIN
	-- Check if user has direct owner role on the project
	IF public.current_user_has_entity_role('project', project_id_param, 'owner') THEN
		RETURN TRUE;
	END IF;

	-- Check if user has admin role on the client
	SELECT client_id INTO client_id_var
	FROM public.project
	WHERE project_id = project_id_param;

	IF client_id_var IS NOT NULL
	AND public.current_user_has_entity_role('client', client_id_var, 'admin') THEN
		RETURN TRUE;
	END IF;

	-- Check if user has admin role on the organization
	SELECT org_id INTO org_id_var
	FROM public.client
	WHERE client_id = client_id_var;

	IF org_id_var IS NOT NULL
	AND public.current_user_has_entity_role('organization', org_id_var, 'admin') THEN
		RETURN TRUE;
	END IF;

	RETURN FALSE;
END;
$function$;

CREATE OR REPLACE FUNCTION public.is_stage_ready_for_completion (p_project_stage_id uuid) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE 
	incomplete_count INTEGER;
BEGIN 
	-- Count incomplete checklist items for this stage
	SELECT COUNT(*) INTO incomplete_count
	FROM public.gateway_checklist_item gci
	JOIN public.gateway_checklist_item_status_log gcisl ON gci.gateway_checklist_item_id = gcisl.gateway_checklist_item_id
	WHERE gci.project_stage_id = p_project_stage_id
		AND gcisl.latest = TRUE
		AND gcisl.status = 'Incomplete';
	
	-- Stage is ready if there are no incomplete items
	RETURN incomplete_count = 0;
END;
$function$;

CREATE OR REPLACE FUNCTION public.log_initial_checklist_item_status () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$ 
BEGIN 
	-- Insert a new status log entry for the newly created checklist item
	INSERT INTO public.gateway_checklist_item_status_log (
		gateway_checklist_item_id,
		status,
		updated_by_user_id,
		valid_at,
		latest
	)
	VALUES (
		NEW.gateway_checklist_item_id,
		'Incomplete', -- Default initial status
		auth.uid(), -- Current user who created the item
		now(), -- Current timestamp
		TRUE -- This is the latest status since it's the first one
	);
	
	RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.revert_to_budget_snapshot (
	p_budget_snapshot_id uuid,
	p_revert_reason text DEFAULT 'Reverted to snapshot'::text
) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_project_id UUID;
	v_item RECORD;
BEGIN
	-- Get the project_id from the snapshot and stage
	SELECT project_id INTO v_project_id
	FROM public.project_stage ps
	JOIN public.budget_snapshot bs ON ps.project_stage_id = bs.project_stage_id
	WHERE bs.budget_snapshot_id = p_budget_snapshot_id;

	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Budget snapshot not found or not linked to a valid project';
	END IF;

	FOR v_item IN (
		SELECT *
		FROM public.budget_snapshot_line_item
		WHERE budget_snapshot_id = p_budget_snapshot_id
	) LOOP
		-- Use the upsert function for each item
		PERFORM public.upsert_budget_line_item(
			v_project_id,
			v_item.wbs_library_item_id,
			v_item.quantity,
			v_item.unit,
			v_item.material_rate,
			v_item.labor_rate,
			v_item.productivity_per_hour,
			v_item.unit_rate_manual_override,
			v_item.unit_rate,
			v_item.factor,
			v_item.remarks,
			v_item.cost_certainty,
			v_item.design_certainty,
			p_revert_reason,
			NULL -- We want to create new records when reverting, not update existing ones
		);
	END LOOP;

	RETURN TRUE;
END;
$function$;

CREATE OR REPLACE FUNCTION public.set_gateway_checklist_item_latest () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
  -- 1) Un-flag every other entry for this checklist item
  UPDATE public.gateway_checklist_item_status_log
  SET latest = FALSE
  WHERE gateway_checklist_item_id = NEW.gateway_checklist_item_id
    AND log_id != NEW.log_id;

  -- 2) Flag whichever row truly has the most recent valid_at
  UPDATE public.gateway_checklist_item_status_log
  SET latest = TRUE
  WHERE log_id = (
    SELECT log_id
    FROM public.gateway_checklist_item_status_log
    WHERE gateway_checklist_item_id = NEW.gateway_checklist_item_id
    ORDER BY valid_at DESC
    LIMIT 1
  );

  RETURN NULL;  -- trigger is AFTER, so no row returned
END;
$function$;

CREATE OR REPLACE FUNCTION public.update_updated_at_column () RETURNS trigger LANGUAGE plpgsql
SET
	search_path TO '' AS $function$ 
BEGIN 
	NEW.updated_at = timezone('utc', now());
	RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.upsert_budget_line_item (
	p_project_id uuid,
	p_wbs_library_item_id uuid,
	p_quantity numeric,
	p_unit text DEFAULT NULL::text,
	p_material_rate numeric DEFAULT 0,
	p_labor_rate numeric DEFAULT NULL::numeric,
	p_productivity_per_hour numeric DEFAULT NULL::numeric,
	p_unit_rate_manual_override boolean DEFAULT false,
	p_unit_rate numeric DEFAULT NULL::numeric,
	p_factor numeric DEFAULT NULL::numeric,
	p_remarks text DEFAULT NULL::text,
	p_cost_certainty numeric DEFAULT NULL::numeric,
	p_design_certainty numeric DEFAULT NULL::numeric,
	p_change_reason text DEFAULT NULL::text,
	p_budget_line_item_id uuid DEFAULT NULL::uuid
) RETURNS uuid LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_calculated_cost NUMERIC;
    v_cost_to_use     NUMERIC;
    v_budget_line_item_id_out UUID;
BEGIN
    -- 1. calculate base cost via helper
    v_calculated_cost := public.calculate_unit_item_cost(
        p_material_rate,
        p_labor_rate,
        p_productivity_per_hour
    );

    -- 2. decide which cost to use
    IF COALESCE(p_unit_rate_manual_override, FALSE) THEN
        IF p_unit_rate IS NULL THEN
            v_cost_to_use := NULL;
        ELSE
            v_cost_to_use := p_unit_rate;
        END IF;
    ELSE
        v_cost_to_use := v_calculated_cost;
    END IF;

    -- 3. single-step UPSERT on the primary key
    INSERT INTO public.budget_line_item_current (
        budget_line_item_id,
        project_id,
        wbs_library_item_id,
        quantity,
        unit,
        material_rate,
        labor_rate,
        productivity_per_hour,
        unit_rate_manual_override,
        unit_rate,
        factor,
        remarks,
        cost_certainty,
        design_certainty,
        updated_at
    )
    VALUES (
        p_budget_line_item_id,
        p_project_id,
        p_wbs_library_item_id,
        p_quantity,
        p_unit,
        p_material_rate,
        p_labor_rate,
        p_productivity_per_hour,
        p_unit_rate_manual_override,
        v_cost_to_use,
        p_factor,
        p_remarks,
        p_cost_certainty,
        p_design_certainty,
        now()
    )
    ON CONFLICT (budget_line_item_id) DO UPDATE
    SET
        project_id              = EXCLUDED.project_id,
        wbs_library_item_id     = EXCLUDED.wbs_library_item_id,
        quantity                = EXCLUDED.quantity,
        unit                    = EXCLUDED.unit,
        material_rate           = EXCLUDED.material_rate,
        labor_rate              = EXCLUDED.labor_rate,
        productivity_per_hour   = EXCLUDED.productivity_per_hour,
        unit_rate_manual_override = EXCLUDED.unit_rate_manual_override,
        unit_rate               = EXCLUDED.unit_rate,
        factor                  = EXCLUDED.factor,
        remarks                 = EXCLUDED.remarks,
        cost_certainty          = EXCLUDED.cost_certainty,
        design_certainty        = EXCLUDED.design_certainty,
        updated_at              = now()
    RETURNING budget_line_item_id
    INTO v_budget_line_item_id_out;

    RETURN v_budget_line_item_id_out;
END;
$function$;

create policy "Organization editors and admins can insert clients" on "public"."client" as permissive for insert to authenticated
with
	check (
		current_user_has_entity_role (
			'organization'::entity_type,
			org_id,
			'editor'::membership_role
		)
	);

create policy "Organization editors and admins can update clients" on "public"."client" as permissive
for update
	to authenticated using (
		current_user_has_entity_role (
			'organization'::entity_type,
			org_id,
			'editor'::membership_role
		)
	)
with
	check (
		current_user_has_entity_role (
			'organization'::entity_type,
			org_id,
			'editor'::membership_role
		)
	);

create policy "Project editors can delete gateway checklist item" on "public"."gateway_checklist_item" as permissive for delete to authenticated using (
	(
		SELECT
			can_modify_project (
				(
					SELECT
						project_stage.project_id
					FROM
						project_stage
					WHERE
						(
							project_stage.project_stage_id = gateway_checklist_item.project_stage_id
						)
				)
			) AS can_modify_project
	)
);

create policy "Project editors can insert gateway checklist item" on "public"."gateway_checklist_item" as permissive for insert to authenticated
with
	check (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_stage.project_id
						FROM
							project_stage
						WHERE
							(
								project_stage.project_stage_id = gateway_checklist_item.project_stage_id
							)
					)
				) AS can_modify_project
		)
	);

create policy "Project editors can update gateway checklist item" on "public"."gateway_checklist_item" as permissive
for update
	to authenticated using (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_stage.project_id
						FROM
							project_stage
						WHERE
							(
								project_stage.project_stage_id = gateway_checklist_item.project_stage_id
							)
					)
				) AS can_modify_project
		)
	)
with
	check (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_stage.project_id
						FROM
							project_stage
						WHERE
							(
								project_stage.project_stage_id = gateway_checklist_item.project_stage_id
							)
					)
				) AS can_modify_project
		)
	);

create policy "Users can view memberships for entities they have access to" on "public"."membership" as permissive for
select
	to authenticated using (
		current_user_has_entity_access (entity_type, entity_id)
	);

create policy "System can insert wbs library item audit records" on "public"."wbs_library_item_audit" as permissive for insert to service_role
with
	check (true);

create policy "Users can view wbs library item audit for accessible items" on "public"."wbs_library_item_audit" as permissive for
select
	to authenticated using (
		CASE
			WHEN (item_type = 'standard'::text) THEN true
			WHEN (
				(item_type = 'custom'::text)
				AND (client_id IS NOT NULL)
			) THEN can_access_client (client_id)
			WHEN (
				(item_type = 'custom'::text)
				AND (project_id IS NOT NULL)
			) THEN can_access_project (project_id)
			ELSE false
		END
	);

create policy "Project editors can update budget line item" on "public"."budget_line_item_current" as permissive
for update
	to authenticated using (
		(
			SELECT
				can_modify_project (budget_line_item_current.project_id) AS can_modify_project
		)
	)
with
	check (
		(
			SELECT
				can_modify_project (budget_line_item_current.project_id) AS can_modify_project
		)
	);

create policy "Project editors can update budget snapshot" on "public"."budget_snapshot" as permissive
for update
	to authenticated using (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_stage.project_id
						FROM
							project_stage
						WHERE
							(
								project_stage.project_stage_id = budget_snapshot.project_stage_id
							)
					)
				) AS can_modify_project
		)
	)
with
	check (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_stage.project_id
						FROM
							project_stage
						WHERE
							(
								project_stage.project_stage_id = budget_snapshot.project_stage_id
							)
					)
				) AS can_modify_project
		)
	);

create policy "Project editors can update budget snapshot line item" on "public"."budget_snapshot_line_item" as permissive
for update
	to authenticated using (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_stage.project_id
						FROM
							project_stage
						WHERE
							(
								project_stage.project_stage_id = (
									SELECT
										budget_snapshot.project_stage_id
									FROM
										budget_snapshot
									WHERE
										(
											budget_snapshot.budget_snapshot_id = budget_snapshot_line_item.budget_snapshot_id
										)
								)
							)
					)
				) AS can_modify_project
		)
	)
with
	check (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_stage.project_id
						FROM
							project_stage
						WHERE
							(
								project_stage.project_stage_id = (
									SELECT
										budget_snapshot.project_stage_id
									FROM
										budget_snapshot
									WHERE
										(
											budget_snapshot.budget_snapshot_id = budget_snapshot_line_item.budget_snapshot_id
										)
								)
							)
					)
				) AS can_modify_project
		)
	);

create policy "Users can view gateway checklist item audit for accessible proj" on "public"."gateway_checklist_item_audit" as permissive for
select
	to authenticated using (
		(
			SELECT
				can_access_project (
					(
						SELECT
							project_stage.project_id
						FROM
							project_stage
						WHERE
							(
								project_stage.project_stage_id = gateway_checklist_item_audit.project_stage_id
							)
					)
				) AS can_access_project
		)
	);

create policy "Users can insert gateway stage info for projects they can edit" on "public"."project_gateway_stage_info" as permissive for insert to authenticated
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					project_stage ps
				WHERE
					(
						(
							ps.project_stage_id = project_gateway_stage_info.project_stage_id
						)
						AND can_modify_project (ps.project_id)
					)
			)
		)
	);

create policy "Users can view project gateway stage info audit for accessible " on "public"."project_gateway_stage_info_audit" as permissive for
select
	to authenticated using (
		(
			SELECT
				can_access_project (
					(
						SELECT
							project_stage.project_id
						FROM
							project_stage
						WHERE
							(
								project_stage.project_stage_id = project_gateway_stage_info_audit.project_stage_id
							)
					)
				) AS can_access_project
		)
	);

create policy "Project Editors and Owners can update project stage" on "public"."project_stage" as permissive
for update
	to authenticated using (
		(
			SELECT
				can_modify_project (project_stage.project_id) AS can_modify_project
		)
	)
with
	check (
		(
			SELECT
				can_modify_project (project_stage.project_id) AS can_modify_project
		)
	);

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.approved_changes FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.budget_line_item_current FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();

CREATE TRIGGER add_creator_as_admin_client
AFTER INSERT ON public.client FOR EACH ROW
EXECUTE FUNCTION add_creator_as_admin ();

CREATE TRIGGER add_creator_as_admin_project
AFTER INSERT ON public.project FOR EACH ROW
EXECUTE FUNCTION add_creator_as_admin ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.risk_register FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();
