-- Access Control Functions
-- This file contains all functions related to membership, permissions, and access control
-- Function to get entity ancestors for hierarchical access control
CREATE OR REPLACE FUNCTION "public"."get_entity_ancestors" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) RETURNS TABLE (
	"entity_type" "public"."entity_type",
	"entity_id" "uuid"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN 
	-- Return the entity itself
	RETURN QUERY SELECT entity_type_param, entity_id_param;
	
	-- Return ancestors based on entity type
	IF entity_type_param = 'project' THEN 
		-- Project -> Client -> Organization
		RETURN QUERY 
		SELECT 'client'::public.entity_type, p.client_id
		FROM public.project p
		WHERE p.project_id = entity_id_param;
		
		RETURN QUERY 
		SELECT 'organization'::public.entity_type, c.org_id
		FROM public.project p
		JOIN public.client c ON p.client_id = c.client_id
		WHERE p.project_id = entity_id_param;
		
	ELSIF entity_type_param = 'client' THEN 
		-- Client -> Organization
		RETURN QUERY 
		SELECT 'organization'::public.entity_type, c.org_id
		FROM public.client c
		WHERE c.client_id = entity_id_param;
	END IF;
END;
$$;

ALTER FUNCTION "public"."get_entity_ancestors" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) OWNER TO "postgres";

-- Function to check if user has access to an entity
CREATE OR REPLACE FUNCTION "public"."has_entity_access" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ 
BEGIN 
	RETURN EXISTS (
		SELECT 1
		FROM public.membership m
		JOIN LATERAL public.get_entity_ancestors(entity_type_param, entity_id_param) a ON TRUE
		WHERE m.user_id = user_id_param
			AND m.entity_type = a.entity_type
			AND m.entity_id = a.entity_id
	);
END;
$$;

ALTER FUNCTION "public"."has_entity_access" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."has_entity_access" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) IS 'Checks if a user has access to an entity through direct membership or ancestor entities';

-- Function to check if current user has access to an entity
CREATE OR REPLACE FUNCTION "public"."current_user_has_entity_access" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ 
BEGIN 
	RETURN public.has_entity_access(auth.uid(), entity_type_param, entity_id_param);
END;
$$;

ALTER FUNCTION "public"."current_user_has_entity_access" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."current_user_has_entity_access" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) IS 'Checks if the current user has access to an entity';

-- Function to get effective role for a user on an entity
CREATE OR REPLACE FUNCTION "public"."get_effective_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) RETURNS "public"."membership_role" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	effective_role public.membership_role;
BEGIN
	-- Check for direct membership first
	SELECT role INTO effective_role
	FROM public.membership
	WHERE user_id = user_id_param
		AND entity_type = entity_type_param
		AND entity_id = entity_id_param;

	IF FOUND THEN
		RETURN effective_role;
	END IF;

	-- Check hierarchical access
	IF entity_type_param = 'project' THEN
		-- Check client level access
		SELECT m.role INTO effective_role
		FROM public.membership m
		JOIN public.project p ON p.project_id = entity_id_param
		WHERE m.user_id = user_id_param
			AND m.entity_type = 'client'
			AND m.entity_id = p.client_id;

		IF FOUND THEN
			RETURN effective_role;
		END IF;

		-- Check organization level access
		SELECT m.role INTO effective_role
		FROM public.membership m
		JOIN public.project p ON p.project_id = entity_id_param
		JOIN public.client c ON p.client_id = c.client_id
		WHERE m.user_id = user_id_param
			AND m.entity_type = 'organization'
			AND m.entity_id = c.org_id;

		IF FOUND THEN
			RETURN effective_role;
		END IF;

	ELSIF entity_type_param = 'client' THEN
		-- Check organization level access
		SELECT m.role INTO effective_role
		FROM public.membership m
		JOIN public.client c ON c.client_id = entity_id_param
		WHERE m.user_id = user_id_param
			AND m.entity_type = 'organization'
			AND m.entity_id = c.org_id;

		IF FOUND THEN
			RETURN effective_role;
		END IF;
	END IF;

	-- No access found
	RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."get_effective_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_effective_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) IS 'Gets the effective role for a user on an entity, considering the hierarchy';

-- Function to check if user has specific role or higher
CREATE OR REPLACE FUNCTION "public"."has_entity_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	user_role public.membership_role;
BEGIN
	user_role := public.get_effective_role(
		user_id_param,
		entity_type_param,
		entity_id_param
	);

	IF user_role IS NULL THEN
		RETURN FALSE;
	END IF;

	-- Role hierarchy: viewer < editor < admin < owner
	CASE min_role_param
		WHEN 'viewer' THEN
			RETURN user_role IN ('viewer', 'editor', 'admin', 'owner');
		WHEN 'editor' THEN
			RETURN user_role IN ('editor', 'admin', 'owner');
		WHEN 'admin' THEN
			RETURN user_role IN ('admin', 'owner');
		WHEN 'owner' THEN
			RETURN user_role = 'owner';
		ELSE
			RETURN FALSE;
	END CASE;
END;
$$;

ALTER FUNCTION "public"."has_entity_role" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) OWNER TO "postgres";

-- Function to check if current user has specific role or higher
CREATE OR REPLACE FUNCTION "public"."current_user_has_entity_role" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN public.has_entity_role(
		auth.uid(),
		entity_type_param,
		entity_id_param,
		min_role_param
	);
END;
$$;

ALTER FUNCTION "public"."current_user_has_entity_role" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."current_user_has_entity_role" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid",
	"min_role_param" "public"."membership_role"
) IS 'Checks if the current user has a specific role or higher on an entity';

-- Convenience functions for specific entity types
CREATE OR REPLACE FUNCTION "public"."can_access_client" ("client_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN public.current_user_has_entity_access('client', client_id_param);
END;
$$;

ALTER FUNCTION "public"."can_access_client" ("client_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."can_access_project" ("project_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN public.current_user_has_entity_access('project', project_id_param);
END;
$$;

ALTER FUNCTION "public"."can_access_project" ("project_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."can_modify_client" ("client_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN public.current_user_has_entity_role('client', client_id_param, 'editor');
END;
$$;

ALTER FUNCTION "public"."can_modify_client" ("client_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."can_modify_client_wbs" ("client_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN public.current_user_has_entity_role('client', client_id_param, 'admin');
END;
$$;

ALTER FUNCTION "public"."can_modify_client_wbs" ("client_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."can_modify_project" ("project_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN public.current_user_has_entity_role('project', project_id_param, 'editor');
END;
$$;

ALTER FUNCTION "public"."can_modify_project" ("project_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."is_project_owner" ("project_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	client_id_var uuid;
	org_id_var uuid;
BEGIN
	-- Check if user has direct owner role on the project
	IF public.current_user_has_entity_role('project', project_id_param, 'owner') THEN
		RETURN TRUE;
	END IF;

	-- Check if user has admin role on the client
	SELECT client_id INTO client_id_var
	FROM public.project
	WHERE project_id = project_id_param;

	IF client_id_var IS NOT NULL
	AND public.current_user_has_entity_role('client', client_id_var, 'admin') THEN
		RETURN TRUE;
	END IF;

	-- Check if user has admin role on the organization
	SELECT org_id INTO org_id_var
	FROM public.client
	WHERE client_id = client_id_var;

	IF org_id_var IS NOT NULL
	AND public.current_user_has_entity_role('organization', org_id_var, 'admin') THEN
		RETURN TRUE;
	END IF;

	RETURN FALSE;
END;
$$;

ALTER FUNCTION "public"."is_project_owner" ("project_id_param" "uuid") OWNER TO "postgres";
