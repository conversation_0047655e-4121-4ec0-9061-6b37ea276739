-- Membership Management Functions and Triggers
-- This file contains functions and triggers for managing memberships and automatic role assignment
-- Function to automatically add creator as admin/owner
create or replace function public.add_creator_as_admin () returns trigger language plpgsql security definer
set
	search_path = '' as $$
declare has_org_admin_access boolean := false;
org_id_var uuid;
begin -- Add the creator as an admin/owner
if TG_TABLE_NAME = 'organization' then -- For organizations, simply add the creator as admin
insert into public.membership(user_id, role, entity_type, entity_id)
values (
		NEW.created_by_user_id,
		'admin',
		'organization',
		NEW.org_id
	);
elsif TG_TABLE_NAME = 'client' then -- For clients, check if the creator already has admin access through the organization
select exists (
		select 1
		from public.membership m
		where m.user_id = NEW.created_by_user_id
			and m.entity_type = 'organization'
			and m.entity_id = NEW.org_id
			and m.role = 'admin'
	) into has_org_admin_access;
-- Only add client admin membership if they don't have org admin access
if not has_org_admin_access then
insert into public.membership(user_id, role, entity_type, entity_id)
values (
		NEW.created_by_user_id,
		'admin',
		'client',
		NEW.client_id
	);
end if;
elsif TG_TABLE_NAME = 'project' then -- For projects, get the client's org_id
select c.org_id into org_id_var
from public.client c
where c.client_id = NEW.client_id;
-- Check if the creator has admin access through the organization
select exists (
		select 1
		from public.membership m
		where m.user_id = NEW.created_by_user_id
			and m.entity_type = 'organization'
			and m.entity_id = org_id_var
			and m.role = 'admin'
	) into has_org_admin_access;
-- If they have org admin access, don't add redundant memberships
if has_org_admin_access then -- Skip adding project owner membership
null;
else -- Check if they have admin access through the client
select exists (
		select 1
		from public.membership m
		where m.user_id = NEW.created_by_user_id
			and m.entity_type = 'client'
			and m.entity_id = NEW.client_id
			and m.role = 'admin'
	) into has_org_admin_access;
-- Only add project owner membership if they don't have client admin access
if not has_org_admin_access then
insert into public.membership(user_id, role, entity_type, entity_id)
values (
		NEW.created_by_user_id,
		'owner',
		'project',
		NEW.project_id
	);
end if;
end if;
end if;
return NEW;
end;
$$;

ALTER FUNCTION "public"."add_creator_as_admin" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."add_creator_as_admin" () IS 'Automatically adds the creator as an admin/owner of the entity';

-- Function to check membership redundancy
CREATE OR REPLACE FUNCTION "public"."check_membership_redundancy" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE 
	ancestor_role public.membership_role;
BEGIN 
	-- Check if user already has equal or higher role through an ancestor
	IF NEW.entity_type = 'project' THEN 
		-- Check client level
		SELECT public.get_effective_role(NEW.user_id, 'client', p.client_id) INTO ancestor_role
		FROM public.project p
		WHERE p.project_id = NEW.entity_id;
		
		IF ancestor_role = 'admin' OR ancestor_role = 'owner' THEN 
			RAISE EXCEPTION 'User already has admin access to this project through client-level permissions';
		END IF;
		
		-- Check organization level
		SELECT public.get_effective_role(NEW.user_id, 'organization', c.org_id) INTO ancestor_role
		FROM public.project p
		JOIN public.client c ON p.client_id = c.client_id
		WHERE p.project_id = NEW.entity_id;
		
		IF ancestor_role = 'admin' THEN 
			RAISE EXCEPTION 'User already has admin access to this project through organization-level permissions';
		END IF;
		
	ELSIF NEW.entity_type = 'client' THEN 
		-- Check organization level
		SELECT public.get_effective_role(NEW.user_id, 'organization', c.org_id) INTO ancestor_role
		FROM public.client c
		WHERE c.client_id = NEW.entity_id;
		
		IF ancestor_role = 'admin' THEN 
			RAISE EXCEPTION 'User already has admin access to this client through organization-level permissions';
		END IF;
	END IF;
	
	RETURN NEW;
END;
$$;

ALTER FUNCTION "public"."check_membership_redundancy" () OWNER TO "postgres";

-- Function to apply pending invites when user signs up
CREATE OR REPLACE FUNCTION "public"."apply_pending_invites" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_invite public.invite %rowtype;
	v_entity_type public.entity_type;
	v_entity_id uuid;
	v_role public.membership_role;
	v_resource_type text;
BEGIN
	-- Loop through all pending invites for this email
	BEGIN
		FOR v_invite IN (
			SELECT *
			FROM public.invite
			WHERE lower(invitee_email) = lower(NEW.email)
				AND status = 'pending'
				AND expires_at > now()
		) LOOP
			BEGIN
				-- Store resource_type as text to avoid casting issues
				v_resource_type := v_invite.resource_type::text;

				IF v_resource_type = 'organization' THEN
					v_entity_type := 'organization'::public.entity_type;
					v_entity_id := v_invite.resource_id;
					IF v_invite.role = 'member' THEN
						v_role := 'viewer'::public.membership_role;
					ELSE
						v_role := v_invite.role::public.membership_role;
					END IF;
				ELSIF v_resource_type = 'client' THEN
					v_entity_type := 'client'::public.entity_type;
					v_entity_id := v_invite.resource_id;
					v_role := v_invite.role::public.membership_role;
				ELSIF v_resource_type = 'project' THEN
					v_entity_type := 'project'::public.entity_type;
					v_entity_id := v_invite.resource_id;
					v_role := v_invite.role::public.membership_role;
				ELSE
					CONTINUE;
				END IF;

				BEGIN
					INSERT INTO public.membership(user_id, role, entity_type, entity_id)
					VALUES (NEW.user_id, v_role, v_entity_type, v_entity_id)
					ON CONFLICT (entity_type, entity_id, user_id) DO NOTHING;

					UPDATE public.invite
					SET status = 'accepted'::public.invite_status,
						updated_at = now(),
						updated_by = NEW.user_id
					WHERE invite_id = v_invite.invite_id;
				EXCEPTION
					WHEN OTHERS THEN
						-- Log error but continue processing other invites
						RAISE NOTICE 'Error processing invite %: %', v_invite.invite_id, SQLERRM;
				END;
			EXCEPTION
				WHEN OTHERS THEN
					-- Log error but continue processing other invites
					RAISE NOTICE 'Error mapping invite %: %', v_invite.invite_id, SQLERRM;
			END;
		END LOOP;

		BEGIN
			UPDATE public.invite
			SET status = 'expired'::public.invite_status,
				updated_at = now(),
				updated_by = NEW.user_id
			WHERE lower(invitee_email) = lower(NEW.email)
				AND status = 'pending'
				AND expires_at <= now();
		EXCEPTION
			WHEN OTHERS THEN
				-- Log error but allow user creation to continue
				RAISE NOTICE 'Error expiring outdated invites: %', SQLERRM;
		END;
	EXCEPTION
		WHEN OTHERS THEN
			-- Log error but allow user creation to continue
			RAISE NOTICE 'Error in apply_pending_invites: %', SQLERRM;
	END;

	RETURN NEW;
END;
$$;

ALTER FUNCTION "public"."apply_pending_invites" () OWNER TO "postgres";

-- Triggers that depend on the functions defined in this file
-- These triggers must be defined after the functions and all tables
-- Trigger for membership redundancy checking
CREATE OR REPLACE TRIGGER "check_membership_redundancy_trigger" BEFORE INSERT
OR
UPDATE ON "public"."membership" FOR EACH ROW
EXECUTE FUNCTION "public"."check_membership_redundancy" ();

-- Triggers for automatic role assignment
-- Trigger for organization
CREATE OR REPLACE TRIGGER "add_creator_as_admin_organization"
AFTER INSERT ON "public"."organization" FOR EACH ROW
EXECUTE FUNCTION "public"."add_creator_as_admin" ();

-- Trigger for client
CREATE OR REPLACE TRIGGER "add_creator_as_admin_client"
AFTER INSERT ON "public"."client" FOR EACH ROW
EXECUTE FUNCTION "public"."add_creator_as_admin" ();

-- Trigger to apply pending invites when a user registers
-- This trigger must be defined after the apply_pending_invites function and profile table
CREATE OR REPLACE TRIGGER "trg_profile_invites"
AFTER INSERT ON "public"."profile" FOR EACH ROW
EXECUTE FUNCTION "public"."apply_pending_invites" ();

-- Additional utility functions for specific role checks
create or replace function "public"."is_client_admin" ("client_id_param" "uuid") returns boolean language plpgsql security definer
set
	search_path = '' as $$ begin return public.current_user_has_entity_role('client', client_id_param, 'admin');
end;
$$;

ALTER FUNCTION "public"."is_client_admin" ("client_id_param" "uuid") OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."is_org_admin_for_project" ("project_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	org_id_var uuid;
BEGIN
	SELECT c.org_id INTO org_id_var
	FROM public.project p
	JOIN public.client c ON p.client_id = c.client_id
	WHERE p.project_id = project_id_param;

	IF org_id_var IS NOT NULL THEN
		RETURN public.current_user_has_entity_role('organization', org_id_var, 'admin');
	END IF;

	RETURN FALSE;
END;
$$;

ALTER FUNCTION "public"."is_org_admin_for_project" ("project_id_param" "uuid") OWNER TO "postgres";
