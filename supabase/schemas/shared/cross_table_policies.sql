-- Cross-Table RLS Policies
-- This file contains RLS policies that reference multiple tables and must be created after all tables exist
-- Organization SELECT policy that references client and project tables
CREATE POLICY "Users can view organizations they have access to" ON "public"."organization" FOR
SELECT
	TO "authenticated" USING (
		(
			"public"."current_user_has_entity_access" ('organization'::"public"."entity_type", "org_id")
			OR (
				EXISTS (
					SELECT
						1
					FROM
						"public"."client" "c"
					WHERE
						(
							("c"."org_id" = "organization"."org_id")
							AND "public"."current_user_has_entity_access" ('client'::"public"."entity_type", "c"."client_id")
						)
				)
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						(
							"public"."project" "p"
							JOIN "public"."client" "c" ON (("p"."client_id" = "c"."client_id"))
						)
					WHERE
						(
							("c"."org_id" = "organization"."org_id")
							AND "public"."current_user_has_entity_access" (
								'project'::"public"."entity_type",
								"p"."project_id"
							)
						)
				)
			)
		)
	);

-- Client SELECT policy that references project table
CREATE POLICY "Users can view clients they have access to" ON "public"."client" FOR
SELECT
	TO "authenticated" USING (
		(
			"public"."current_user_has_entity_access" ('client'::"public"."entity_type", "client_id")
			OR (
				EXISTS (
					SELECT
						1
					FROM
						"public"."project" "p"
					WHERE
						(
							("p"."client_id" = "client"."client_id")
							AND "public"."current_user_has_entity_access" (
								'project'::"public"."entity_type",
								"p"."project_id"
							)
						)
				)
			)
		)
	);
