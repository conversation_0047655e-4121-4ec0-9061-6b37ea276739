-- Custom Types
CREATE TYPE "public"."checklist_item_status" AS ENUM('Incomplete', 'Deferred', 'Complete');

ALTER TYPE "public"."checklist_item_status" OWNER TO "postgres";

CREATE TYPE "public"."entity_type" AS ENUM('organization', 'client', 'project');

ALTER TYPE "public"."entity_type" OWNER TO "postgres";

CREATE TYPE "public"."invite_resource_type" AS ENUM('organization', 'client', 'project');

ALTER TYPE "public"."invite_resource_type" OWNER TO "postgres";

CREATE TYPE "public"."invite_status" AS ENUM(
	'pending',
	'accepted',
	'revoked',
	'expired',
	'declined'
);

ALTER TYPE "public"."invite_status" OWNER TO "postgres";

CREATE TYPE "public"."membership_role" AS ENUM('viewer', 'editor', 'admin', 'owner');

ALTER TYPE "public"."membership_role" OWNER TO "postgres";

CREATE TYPE "public"."wbs_item_type" AS ENUM('Standard', 'Custom');

ALTER TYPE "public"."wbs_item_type" OWNER TO "postgres";

CREATE TYPE "public"."risk_status" AS ENUM('risk', 'pending', 'approved');

ALTER TYPE "public"."risk_status" OWNER TO "postgres";

-- Budget version kind
CREATE TYPE "public"."budget_version_kind" AS ENUM('stage', 'import', 'manual', 'system');

ALTER TYPE "public"."budget_version_kind" OWNER TO "postgres";

-- Schema Comments
COMMENT ON SCHEMA "public" IS 'standard public schema';
