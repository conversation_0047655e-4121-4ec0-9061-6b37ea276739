-- Membership Table Schema
-- Polymorphic membership table for organization, client, and project access control
-- Membership table (polymorphic access control)
CREATE TABLE IF NOT EXISTS "public"."membership" (
	"membership_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"user_id" "uuid" NOT NULL,
	"role" "public"."membership_role" NOT NULL,
	"entity_type" "public"."entity_type" NOT NULL,
	"entity_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."membership" OWNER TO "postgres";

COMMENT ON TABLE "public"."membership" IS 'Polymorphic membership table for organization, client, and project access control';

-- Primary key constraint
ALTER TABLE ONLY "public"."membership"
ADD CONSTRAINT "membership_pkey" PRIMARY KEY ("membership_id");

-- Foreign key constraint
ALTER TABLE ONLY "public"."membership"
ADD CONSTRAINT "membership_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "membership_entity_idx" ON "public"."membership" USING "btree" ("entity_type", "entity_id");

CREATE UNIQUE INDEX "membership_unique_entity_user" ON "public"."membership" USING "btree" ("entity_type", "entity_id", "user_id");

CREATE INDEX "membership_user_idx" ON "public"."membership" USING "btree" ("user_id");

-- Enable Row Level Security
ALTER TABLE "public"."membership" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."membership" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
CREATE POLICY "Admins can manage memberships" ON "public"."membership" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			"entity_type",
			"entity_id",
			'admin'::"public"."membership_role"
		)
	);

CREATE POLICY "Admins can update memberships" ON "public"."membership"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			"entity_type",
			"entity_id",
			'admin'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			"entity_type",
			"entity_id",
			'admin'::"public"."membership_role"
		)
	);

CREATE POLICY "Admins can delete memberships" ON "public"."membership" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		"entity_type",
		"entity_id",
		'admin'::"public"."membership_role"
	)
);

CREATE POLICY "Users can view memberships for entities they have access to" ON "public"."membership" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ("entity_type", "entity_id")
	);
