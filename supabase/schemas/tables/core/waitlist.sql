-- unapproved signups
CREATE TABLE IF NOT EXISTS "public"."waitlist" (
	"waitlist_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"email" "text" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."waitlist" OWNER TO "postgres";

-- Primary key constraint
ALTER TABLE ONLY "public"."waitlist"
ADD CONSTRAINT "waitlist_pkey" PRIMARY KEY ("waitlist_id");

-- Unique constraint on email
ALTER TABLE ONLY "public"."waitlist"
ADD CONSTRAINT "waitlist_email_key" UNIQUE ("email");

-- Enable Row Level Security
ALTER TABLE "public"."waitlist" ENABLE ROW LEVEL SECURITY;

-- allow insert
CREATE POLICY "Anyone can insert waitlist" ON "public"."waitlist" FOR INSERT TO "anon"
WITH
	CHECK (true);
