-- Budget Versioning Tables and related constraints/RLS
-- budget_version
CREATE TABLE IF NOT EXISTS "public"."budget_version" (
	"budget_version_id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"project_id" uuid NOT NULL,
	"label" text,
	"kind" "public"."budget_version_kind" NOT NULL,
	"stage_id" uuid,
	"prev_version_id" uuid,
	"created_by_user_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT timezone ('utc'::text, now()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT timezone ('utc'::text, now()) NOT NULL
);

ALTER TABLE "public"."budget_version" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_version" IS 'Immutable budget versions per project with lineage and optional stage link';

-- Primary key
ALTER TABLE ONLY "public"."budget_version"
ADD CONSTRAINT "budget_version_pkey" PRIMARY KEY ("budget_version_id");

-- Foreign keys
ALTER TABLE ONLY "public"."budget_version"
ADD CONSTRAINT "budget_version_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_version"
ADD CONSTRAINT "budget_version_stage_id_fkey" FOREIGN KEY ("stage_id") REFERENCES "public"."project_stage" ("project_stage_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_version"
ADD CONSTRAINT "budget_version_prev_version_id_fkey" FOREIGN KEY ("prev_version_id") REFERENCES "public"."budget_version" ("budget_version_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_version"
ADD CONSTRAINT "budget_version_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Constraints
ALTER TABLE ONLY "public"."budget_version"
ADD CONSTRAINT "budget_version_stage_consistency" CHECK (
	(
		(
			kind = 'stage'::public.budget_version_kind
			AND stage_id IS NOT NULL
		)
		OR (kind <> 'stage'::public.budget_version_kind)
	)
);

ALTER TABLE ONLY "public"."budget_version"
ADD CONSTRAINT "budget_version_no_self_reference" CHECK (budget_version_id <> prev_version_id);

-- Indexes
CREATE INDEX IF NOT EXISTS "budget_version_project_id_created_at_idx" ON "public"."budget_version" USING btree ("project_id", "created_at" DESC);

CREATE INDEX IF NOT EXISTS "budget_version_kind_project_id_idx" ON "public"."budget_version" USING btree ("kind", "project_id");

CREATE INDEX IF NOT EXISTS "budget_version_prev_version_id_idx" ON "public"."budget_version" USING btree ("prev_version_id");

CREATE INDEX IF NOT EXISTS "budget_version_stage_id_idx" ON "public"."budget_version" USING btree ("stage_id");

-- RLS
ALTER TABLE "public"."budget_version" ENABLE ROW LEVEL SECURITY;

-- updated_at trigger
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."budget_version" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Policies
CREATE POLICY "Project viewers can view budget versions" ON "public"."budget_version" FOR
SELECT
	TO "authenticated" USING (public.can_access_project (project_id));

CREATE POLICY "Project editors can insert budget versions" ON "public"."budget_version" FOR INSERT TO "authenticated"
WITH
	CHECK (public.can_modify_project (project_id));

CREATE POLICY "Project editors can update budget versions" ON "public"."budget_version"
FOR UPDATE
	TO "authenticated" USING (public.can_modify_project (project_id))
WITH
	CHECK (public.can_modify_project (project_id));

CREATE POLICY "Project editors can delete budget versions" ON "public"."budget_version" FOR DELETE TO "authenticated" USING (public.can_modify_project (project_id));

-- Add cross-table foreign keys now that budget_version exists
DO $$
BEGIN
  BEGIN
    ALTER TABLE ONLY "public"."project"
    ADD CONSTRAINT "project_active_budget_version_id_fkey"
    FOREIGN KEY ("active_budget_version_id") REFERENCES "public"."budget_version" ("budget_version_id") ON UPDATE RESTRICT ON DELETE SET NULL;
  EXCEPTION WHEN duplicate_object THEN
    NULL;
  END;

  BEGIN
    ALTER TABLE ONLY "public"."budget_snapshot"
    ADD CONSTRAINT "budget_snapshot_budget_version_id_fkey"
    FOREIGN KEY ("budget_version_id") REFERENCES "public"."budget_version" ("budget_version_id") ON UPDATE RESTRICT ON DELETE RESTRICT;
  EXCEPTION WHEN duplicate_object THEN
    NULL;
  END;
END $$;
