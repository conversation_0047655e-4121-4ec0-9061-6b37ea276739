-- budget_version_item table and RLS
CREATE TABLE IF NOT EXISTS "public"."budget_version_item" (
	"budget_version_item_id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"budget_version_id" uuid NOT NULL,
	"wbs_library_item_id" uuid NOT NULL,
	-- Columns mirrored from budget_line_item_current
	"quantity" numeric(20, 4) NOT NULL,
	"unit" text,
	"material_rate" numeric(20, 4) NOT NULL,
	"labor_rate" numeric(20, 4),
	"productivity_per_hour" numeric(20, 4),
	"unit_rate_manual_override" boolean DEFAULT false NOT NULL,
	"unit_rate" numeric(20, 4) NOT NULL,
	"factor" numeric(20, 4),
	"remarks" text,
	"cost_certainty" numeric(5, 2),
	"design_certainty" numeric(5, 2),
	"created_at" timestamp with time zone DEFAULT timezone ('utc'::text, now()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT timezone ('utc'::text, now()) NOT NULL
);

ALTER TABLE "public"."budget_version_item" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_version_item" IS 'Immutable line items for a specific budget version (mirror of current line items)';

-- Primary key
ALTER TABLE ONLY "public"."budget_version_item"
ADD CONSTRAINT "budget_version_item_pkey" PRIMARY KEY ("budget_version_item_id");

-- Foreign keys
ALTER TABLE ONLY "public"."budget_version_item"
ADD CONSTRAINT "budget_version_item_version_id_fkey" FOREIGN KEY ("budget_version_id") REFERENCES "public"."budget_version" ("budget_version_id") ON UPDATE RESTRICT ON DELETE CASCADE;

ALTER TABLE ONLY "public"."budget_version_item"
ADD CONSTRAINT "budget_version_item_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Constraints
ALTER TABLE ONLY "public"."budget_version_item"
ADD CONSTRAINT "budget_version_item_unique_per_version" UNIQUE ("budget_version_id", "wbs_library_item_id");

-- Indexes
CREATE INDEX IF NOT EXISTS "budget_version_item_version_id_idx" ON "public"."budget_version_item" USING btree ("budget_version_id");

CREATE INDEX IF NOT EXISTS "budget_version_item_wbs_library_item_id_idx" ON "public"."budget_version_item" USING btree ("wbs_library_item_id");

-- RLS
ALTER TABLE "public"."budget_version_item" ENABLE ROW LEVEL SECURITY;

-- updated_at trigger
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."budget_version_item" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Policies
-- Viewers: can view items if they can access the project of the version
CREATE POLICY "Project viewers can view budget version items" ON "public"."budget_version_item" FOR
SELECT
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				public.budget_version v
			WHERE
				v.budget_version_id = budget_version_item.budget_version_id
				AND public.can_access_project (v.project_id)
		)
	);

-- Editors: can insert items for accessible versions (stage, import, manual)
CREATE POLICY "Editors can insert items for accessible versions" ON "public"."budget_version_item" FOR INSERT TO "authenticated"
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				public.budget_version v
				LEFT JOIN public.project p ON p.project_id = v.project_id
			WHERE
				v.budget_version_id = budget_version_item.budget_version_id
				AND public.can_modify_project (v.project_id)
				AND (
					-- Allow active stage versions (normal editing)
					(
						v.kind = 'stage'::public.budget_version_kind
						AND p.active_budget_version_id = v.budget_version_id
					)
					OR
					-- Allow import versions (during import operations)
					(v.kind = 'import'::public.budget_version_kind)
					OR
					-- Allow manual versions (for cloning operations)
					(v.kind = 'manual'::public.budget_version_kind)
				)
		)
	);

CREATE POLICY "Editors can update items for accessible versions" ON "public"."budget_version_item"
FOR UPDATE
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				public.budget_version v
				LEFT JOIN public.project p ON p.project_id = v.project_id
			WHERE
				v.budget_version_id = budget_version_item.budget_version_id
				AND public.can_modify_project (v.project_id)
				AND (
					-- Allow active stage versions (normal editing)
					(
						v.kind = 'stage'::public.budget_version_kind
						AND p.active_budget_version_id = v.budget_version_id
					)
					OR
					-- Allow import versions (during import operations)
					(v.kind = 'import'::public.budget_version_kind)
					OR
					-- Allow manual versions (for cloning operations)
					(v.kind = 'manual'::public.budget_version_kind)
				)
		)
	)
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				public.budget_version v
				LEFT JOIN public.project p ON p.project_id = v.project_id
			WHERE
				v.budget_version_id = budget_version_item.budget_version_id
				AND public.can_modify_project (v.project_id)
				AND (
					-- Allow active stage versions (normal editing)
					(
						v.kind = 'stage'::public.budget_version_kind
						AND p.active_budget_version_id = v.budget_version_id
					)
					OR
					-- Allow import versions (during import operations)
					(v.kind = 'import'::public.budget_version_kind)
					OR
					-- Allow manual versions (for cloning operations)
					(v.kind = 'manual'::public.budget_version_kind)
				)
		)
	);

CREATE POLICY "Editors can delete items for accessible versions" ON "public"."budget_version_item" FOR DELETE TO "authenticated" USING (
	EXISTS (
		SELECT
			1
		FROM
			public.budget_version v
			LEFT JOIN public.project p ON p.project_id = v.project_id
		WHERE
			v.budget_version_id = budget_version_item.budget_version_id
			AND public.can_modify_project (v.project_id)
			AND (
				-- Allow active stage versions (normal editing)
				(
					v.kind = 'stage'::public.budget_version_kind
					AND p.active_budget_version_id = v.budget_version_id
				)
				OR
				-- Allow import versions (during import operations)
				(v.kind = 'import'::public.budget_version_kind)
				OR
				-- Allow manual versions (for cloning operations)
				(v.kind = 'manual'::public.budget_version_kind)
			)
	)
);
