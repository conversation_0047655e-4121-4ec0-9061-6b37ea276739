-- budget_import table and RLS
CREATE TABLE IF NOT EXISTS "public"."budget_import" (
	"budget_import_id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"project_id" uuid NOT NULL,
	"source_filename" text NOT NULL,
	"source_hash" text NOT NULL,
	"pre_version_id" uuid NOT NULL,
	"new_version_id" uuid NOT NULL,
	"is_undone" boolean DEFAULT false,
	"undone_at" timestamp with time zone,
	"undone_by_user_id" uuid,
	"created_by_user_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT timezone ('utc'::text, now()) NOT NULL
);

ALTER TABLE "public"."budget_import" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_import" IS 'Tracks budget imports and their resulting versions with idempotency';

-- Primary key
ALTER TABLE ONLY "public"."budget_import"
ADD CONSTRAINT "budget_import_pkey" PRIMARY KEY ("budget_import_id");

-- Foreign keys
ALTER TABLE ONLY "public"."budget_import"
ADD CONSTRAINT "budget_import_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_import"
ADD CONSTRAINT "budget_import_pre_version_id_fkey" FOREIGN KEY ("pre_version_id") REFERENCES "public"."budget_version" ("budget_version_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_import"
ADD CONSTRAINT "budget_import_new_version_id_fkey" FOREIGN KEY ("new_version_id") REFERENCES "public"."budget_version" ("budget_version_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_import"
ADD CONSTRAINT "budget_import_undone_by_user_id_fkey" FOREIGN KEY ("undone_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_import"
ADD CONSTRAINT "budget_import_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Idempotency
ALTER TABLE ONLY "public"."budget_import"
ADD CONSTRAINT "budget_import_project_source_hash_key" UNIQUE ("project_id", "source_hash");

-- Indexes
CREATE INDEX IF NOT EXISTS "budget_import_project_id_idx" ON "public"."budget_import" USING btree ("project_id");

CREATE INDEX IF NOT EXISTS "budget_import_created_at_idx" ON "public"."budget_import" USING btree ("created_at");

-- RLS
ALTER TABLE "public"."budget_import" ENABLE ROW LEVEL SECURITY;

-- Policies
CREATE POLICY "Project viewers can view budget imports" ON "public"."budget_import" FOR
SELECT
	TO "authenticated" USING (public.can_access_project (project_id));

CREATE POLICY "Project editors can insert budget imports" ON "public"."budget_import" FOR INSERT TO "authenticated"
WITH
	CHECK (public.can_modify_project (project_id));

CREATE POLICY "Project editors can update budget imports" ON "public"."budget_import"
FOR UPDATE
	TO "authenticated" USING (public.can_modify_project (project_id))
WITH
	CHECK (public.can_modify_project (project_id));

CREATE POLICY "Project editors can delete budget imports" ON "public"."budget_import" FOR DELETE TO "authenticated" USING (public.can_modify_project (project_id));
