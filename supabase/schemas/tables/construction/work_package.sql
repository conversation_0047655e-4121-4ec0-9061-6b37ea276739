-- Work Package Table Schema
-- Contains work package information with project-level access control
-- Work packages are hierarchical units of work associated with projects and WBS items
CREATE TABLE IF NOT EXISTS "public"."work_package" (
	"work_package_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"project_id" "uuid" NOT NULL,
	"wbs_library_item_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."work_package" OWNER TO "postgres";

COMMENT ON TABLE "public"."work_package" IS 'Work packages for project work breakdown structure';

COMMENT ON COLUMN "public"."work_package"."name" IS 'Name of the work package';

COMMENT ON COLUMN "public"."work_package"."description" IS 'Detailed description of the work package';

COMMENT ON COLUMN "public"."work_package"."wbs_library_item_id" IS 'Associated WBS library item';

-- Primary key constraint
ALTER TABLE ONLY "public"."work_package"
ADD CONSTRAINT "work_package_pkey" PRIMARY KEY ("work_package_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."work_package"
ADD CONSTRAINT "work_package_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."work_package"
ADD CONSTRAINT "work_package_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "work_package_project_id_idx" ON "public"."work_package" USING "btree" ("project_id");

CREATE INDEX "work_package_wbs_library_item_id_idx" ON "public"."work_package" USING "btree" ("wbs_library_item_id");

-- Enable Row Level Security
ALTER TABLE "public"."work_package" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."work_package" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Audit trigger for work_package table (function defined in shared/audit_functions.sql)
CREATE OR REPLACE TRIGGER "audit_work_package_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."work_package" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_work_package_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view work packages for projects they have access to
CREATE POLICY "Users can view work packages for accessible projects" ON "public"."work_package" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);

-- INSERT policies - users can create work packages for projects they have editor access to
CREATE POLICY "Users can create work packages for projects they can edit" ON "public"."work_package" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

-- UPDATE policies - users can update work packages for projects they have editor access to
CREATE POLICY "Users can update work packages for projects they can edit" ON "public"."work_package"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

-- DELETE policies - users can delete work packages for projects they have admin access to
CREATE POLICY "Users can delete work packages for projects they can admin" ON "public"."work_package" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'admin'::"public"."membership_role"
	)
);

-- Table-specific functions
-- Function to get work packages accessible to a user for a specific project
CREATE OR REPLACE FUNCTION "public"."get_accessible_work_packages" ("project_id_param" "uuid") RETURNS TABLE (
	"work_package_id" "uuid",
	"name" "text",
	"description" "text",
	"project_id" "uuid",
	"purchase_order_count" integer,
	"wbs_library_item_id" "uuid",
	"wbs_code" "text",
	"wbs_description" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
) LANGUAGE "plpgsql"
SET
	"search_path" TO '' AS $$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return work packages for the project with related information
	RETURN QUERY
	SELECT
		wp.work_package_id,
		wp.name,
		wp.description,
		wp.project_id,
		COALESCE(po_agg.purchase_order_count, 0) AS purchase_order_count,
		wp.wbs_library_item_id,
		wli.code AS wbs_code,
		wli.description AS wbs_description,
		wp.created_at,
		wp.updated_at
	FROM public.work_package wp
	LEFT JOIN public.wbs_library_item wli ON wp.wbs_library_item_id = wli.wbs_library_item_id
	LEFT JOIN (
		SELECT
			po.work_package_id,
			COUNT(po.purchase_order_id)::integer AS purchase_order_count
		FROM public.purchase_order po
		WHERE po.project_id = project_id_param
		GROUP BY po.work_package_id
	) po_agg ON wp.work_package_id = po_agg.work_package_id
	WHERE wp.project_id = project_id_param
	ORDER BY wp.name;
END;
$$;

ALTER FUNCTION "public"."get_accessible_work_packages" ("project_id_param" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_accessible_work_packages" ("project_id_param" "uuid") IS 'Returns work packages accessible to the current user for a specific project with purchase order count';

-- Function to get work packages for a project
CREATE OR REPLACE FUNCTION "public"."get_work_packages_for_project" ("project_id_param" "uuid") RETURNS TABLE (
	"work_package_id" "uuid",
	"name" "text",
	"description" "text",
	"wbs_code" "text"
) LANGUAGE "plpgsql"
SET
	"search_path" TO '' AS $$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return work packages for the project with WBS information
	RETURN QUERY
	SELECT
		wp.work_package_id,
		wp.name,
		wp.description,
		wli.code AS wbs_code
	FROM public.work_package wp
	LEFT JOIN public.wbs_library_item wli ON wp.wbs_library_item_id = wli.wbs_library_item_id
	WHERE wp.project_id = project_id_param
	ORDER BY wp.name;
END;
$$;

ALTER FUNCTION "public"."get_work_packages_for_project" ("project_id_param" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_work_packages_for_project" ("project_id_param" "uuid") IS 'Returns all work packages for a project';

-- Function to get WBS library items for work package selection
CREATE OR REPLACE FUNCTION "public"."get_wbs_items_for_work_package" ("project_id_param" "uuid") RETURNS TABLE (
	"wbs_library_item_id" "uuid",
	"code" "text",
	"description" "text",
	"level" integer,
	"parent_item_id" "uuid"
) LANGUAGE "plpgsql"
SET
	"search_path" TO '' AS $$
DECLARE
	v_user_id UUID;
	v_client_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Get the client_id for the project
	SELECT p.client_id INTO v_client_id
	FROM public.project p
	WHERE p.project_id = project_id_param;

	-- Return WBS library items accessible for this project
	-- This includes: Standard items, client-level custom items, and project-level custom items
	RETURN QUERY
	SELECT
		wli.wbs_library_item_id,
		wli.code,
		wli.description,
		wli.level,
		wli.parent_item_id
	FROM public.wbs_library_item wli
	WHERE
		-- Standard items (globally accessible)
		wli.item_type = 'Standard'::public.wbs_item_type
		OR
		-- Client-level custom items
		(wli.item_type = 'Custom'::public.wbs_item_type AND wli.client_id = v_client_id AND wli.project_id IS NULL)
		OR
		-- Project-level custom items
		(wli.item_type = 'Custom'::public.wbs_item_type AND wli.project_id = project_id_param)
	ORDER BY wli.code;
END;
$$;

ALTER FUNCTION "public"."get_wbs_items_for_work_package" ("project_id_param" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_wbs_items_for_work_package" ("project_id_param" "uuid") IS 'Returns WBS library items for work package selection';

-- Function to get cost detail data combining WBS codes, budget items, work packages, and purchase orders
CREATE OR REPLACE FUNCTION "public"."get_cost_detail_data" ("project_id_param" "uuid") RETURNS TABLE (
	"wbs_library_item_id" "uuid",
	"wbs_code" "text",
	"wbs_description" "text",
	"wbs_level" integer,
	"parent_item_id" "uuid",
	"budget_amount" numeric,
	"quantity" numeric,
	"unit_rate" numeric,
	"factor" numeric,
	"work_packages" jsonb,
	"purchase_orders" jsonb
) LANGUAGE "plpgsql"
SET
	"search_path" TO '' AS $$
DECLARE
	v_user_id UUID;
	v_client_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Get the client_id for the project
	SELECT p.client_id INTO v_client_id
	FROM public.project p
	WHERE p.project_id = project_id_param;

	-- Return cost detail data with hierarchical WBS structure
	RETURN QUERY
	WITH wbs_budget AS (
		-- Get all WBS items for the project with their budget data
		SELECT
			wli.wbs_library_item_id,
			wli.code AS wbs_code,
			wli.description AS wbs_description,
			wli.level AS wbs_level,
			wli.parent_item_id,
			bli.quantity,
			bli.unit_rate,
			bli.factor,
			CASE
				WHEN bli.quantity IS NOT NULL AND bli.unit_rate IS NOT NULL THEN
					bli.quantity * bli.unit_rate * COALESCE(bli.factor, 1)
				ELSE 0
			END AS budget_amount
		FROM public.wbs_library_item wli
		LEFT JOIN public.budget_line_item_current bli ON wli.wbs_library_item_id = bli.wbs_library_item_id
			AND bli.project_id = project_id_param
		WHERE wli.wbs_library_id = (
			SELECT p.wbs_library_id
			FROM public.project p
			WHERE p.project_id = project_id_param
		)
		AND (
			wli.item_type = 'Standard'::public.wbs_item_type
			OR (wli.item_type = 'Custom'::public.wbs_item_type AND (
				wli.client_id = v_client_id OR wli.project_id = project_id_param
			))
		)
	),
	work_package_data AS (
		-- Get work packages grouped by WBS item
		SELECT
			wp.wbs_library_item_id,
			jsonb_agg(
				jsonb_build_object(
					'work_package_id', wp.work_package_id,
					'name', wp.name,
					'description', wp.description,
					'purchase_order_count', COALESCE(po_count.count, 0)
				)
			) AS work_packages
		FROM public.work_package wp
		LEFT JOIN (
			SELECT
				po.work_package_id,
				COUNT(po.purchase_order_id) AS count
			FROM public.purchase_order po
			WHERE po.project_id = project_id_param
			GROUP BY po.work_package_id
		) po_count ON wp.work_package_id = po_count.work_package_id
		WHERE wp.project_id = project_id_param
		GROUP BY wp.wbs_library_item_id
	),
	purchase_order_data AS (
		-- Get purchase orders for each WBS item through work packages
		SELECT
			wp.wbs_library_item_id,
			jsonb_agg(
				DISTINCT jsonb_build_object(
					'purchase_order_id', po.purchase_order_id,
					'po_number', po.po_number,
					'description', po.description,
					'vendor_name', v.name,
					'original_amount', po.original_amount,
					'co_amount', po.co_amount,
					'work_package_id', po.work_package_id
				)
			) FILTER (WHERE po.purchase_order_id IS NOT NULL) AS purchase_orders
		FROM public.work_package wp
		LEFT JOIN public.purchase_order po ON wp.work_package_id = po.work_package_id
		LEFT JOIN public.vendor v ON po.vendor_id = v.vendor_id
		WHERE wp.project_id = project_id_param
		GROUP BY wp.wbs_library_item_id
	)
	SELECT
		wb.wbs_library_item_id,
		wb.wbs_code,
		wb.wbs_description,
		wb.wbs_level,
		wb.parent_item_id,
		wb.budget_amount,
		wb.quantity,
		wb.unit_rate,
		wb.factor,
		COALESCE(wpd.work_packages, '[]'::jsonb) AS work_packages,
		COALESCE(pod.purchase_orders, '[]'::jsonb) AS purchase_orders
	FROM wbs_budget wb
	LEFT JOIN work_package_data wpd ON wb.wbs_library_item_id = wpd.wbs_library_item_id
	LEFT JOIN purchase_order_data pod ON wb.wbs_library_item_id = pod.wbs_library_item_id
	ORDER BY wb.wbs_code;
END;
$$;

ALTER FUNCTION "public"."get_cost_detail_data" ("project_id_param" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_cost_detail_data" ("project_id_param" "uuid") IS 'Returns cost detail data combining WBS codes, budget items, work packages, and purchase orders for a project';
