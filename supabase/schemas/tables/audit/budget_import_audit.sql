-- Budget Import Audit Table Schema
-- Audit log of all changes to budget imports
-- Budget Import Audit table
CREATE TABLE IF NOT EXISTS "public"."budget_import_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"budget_import_id" "uuid",
	"project_id" "uuid",
	"source_filename" "text",
	"source_hash" "text",
	"pre_version_id" "uuid",
	"new_version_id" "uuid",
	"is_undone" boolean,
	"undone_at" timestamp with time zone,
	"undone_by_user_id" "uuid",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	CONSTRAINT "budget_import_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."budget_import_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_import_audit" IS 'Audit log of all changes to budget imports';

-- Primary key constraint
ALTER TABLE ONLY "public"."budget_import_audit"
ADD CONSTRAINT "budget_import_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."budget_import_audit"
ADD CONSTRAINT "budget_import_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "budget_import_audit_changed_at_idx" ON "public"."budget_import_audit" USING "btree" ("changed_at");

CREATE INDEX "budget_import_audit_budget_import_id_idx" ON "public"."budget_import_audit" USING "btree" ("budget_import_id");

CREATE INDEX "budget_import_audit_project_id_idx" ON "public"."budget_import_audit" USING "btree" ("project_id");

-- Enable Row Level Security
ALTER TABLE "public"."budget_import_audit" ENABLE ROW LEVEL SECURITY;

-- Budget Import Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_budget_import_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.budget_import_audit (
            operation_type, changed_by, changed_at, old_values,
            budget_import_id, project_id, source_filename, source_hash, pre_version_id, new_version_id,
            is_undone, undone_at, undone_by_user_id, created_by_user_id, created_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.budget_import_id, OLD.project_id, OLD.source_filename, OLD.source_hash, OLD.pre_version_id, OLD.new_version_id,
            OLD.is_undone, OLD.undone_at, OLD.undone_by_user_id, OLD.created_by_user_id, OLD.created_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.budget_import_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            budget_import_id, project_id, source_filename, source_hash, pre_version_id, new_version_id,
            is_undone, undone_at, undone_by_user_id, created_by_user_id, created_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.budget_import_id, NEW.project_id, NEW.source_filename, NEW.source_hash, NEW.pre_version_id, NEW.new_version_id,
            NEW.is_undone, NEW.undone_at, NEW.undone_by_user_id, NEW.created_by_user_id, NEW.created_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.budget_import_audit (
            operation_type, changed_by, changed_at, new_values,
            budget_import_id, project_id, source_filename, source_hash, pre_version_id, new_version_id,
            is_undone, undone_at, undone_by_user_id, created_by_user_id, created_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.budget_import_id, NEW.project_id, NEW.source_filename, NEW.source_hash, NEW.pre_version_id, NEW.new_version_id,
            NEW.is_undone, NEW.undone_at, NEW.undone_by_user_id, NEW.created_by_user_id, NEW.created_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_budget_import_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_budget_import_changes" () IS 'Audit trigger function for budget_import table';

-- Audit trigger for budget_import table
CREATE OR REPLACE TRIGGER "audit_budget_import_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."budget_import" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_budget_import_changes" ();

-- Row Level Security Policies
CREATE POLICY "System can insert budget import audit records" ON "public"."budget_import_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view budget import audit for accessible projects" ON "public"."budget_import_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));
