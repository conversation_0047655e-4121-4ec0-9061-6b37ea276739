-- Project Stage Audit Table Schema
-- Audit log of all changes to project stages
-- Project Stage Audit table
CREATE TABLE IF NOT EXISTS "public"."project_stage_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"project_stage_id" "uuid",
	"project_id" "uuid",
	"name" "text",
	"description" "text",
	"stage_order" integer,
	"stage" integer,
	"gateway_qualitative_scorecard" "jsonb",
	"date_started" timestamp with time zone,
	"date_completed" timestamp with time zone,
	"completion_notes" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "project_stage_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."project_stage_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."project_stage_audit" IS 'Audit log of all changes to project stages';

-- Primary key constraint
ALTER TABLE ONLY "public"."project_stage_audit"
ADD CONSTRAINT "project_stage_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."project_stage_audit"
ADD CONSTRAINT "project_stage_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "project_stage_audit_changed_at_idx" ON "public"."project_stage_audit" USING "btree" ("changed_at");

CREATE INDEX "project_stage_audit_project_stage_id_idx" ON "public"."project_stage_audit" USING "btree" ("project_stage_id");

CREATE INDEX "project_stage_audit_changed_by_idx" ON "public"."project_stage_audit" USING "btree" ("changed_by");

CREATE INDEX "project_stage_audit_operation_type_idx" ON "public"."project_stage_audit" USING "btree" ("operation_type");

CREATE INDEX "project_stage_audit_project_id_idx" ON "public"."project_stage_audit" USING "btree" ("project_id");

-- Enable Row Level Security
ALTER TABLE "public"."project_stage_audit" ENABLE ROW LEVEL SECURITY;

-- Project Stage Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_project_stage_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.project_stage_audit (
            operation_type, changed_by, changed_at, old_values,
            project_stage_id, project_id, name, description, stage_order, stage,
            gateway_qualitative_scorecard, date_started, date_completed, completion_notes,
            created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.project_stage_id, OLD.project_id, OLD.name, OLD.description, OLD.stage_order, OLD.stage,
            OLD.gateway_qualitative_scorecard, OLD.date_started, OLD.date_completed, OLD.completion_notes,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.project_stage_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            project_stage_id, project_id, name, description, stage_order, stage,
            gateway_qualitative_scorecard, date_started, date_completed, completion_notes,
            created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.project_stage_id, NEW.project_id, NEW.name, NEW.description, NEW.stage_order, NEW.stage,
            NEW.gateway_qualitative_scorecard, NEW.date_started, NEW.date_completed, NEW.completion_notes,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.project_stage_audit (
            operation_type, changed_by, changed_at, new_values,
            project_stage_id, project_id, name, description, stage_order, stage,
            gateway_qualitative_scorecard, date_started, date_completed, completion_notes,
            created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.project_stage_id, NEW.project_id, NEW.name, NEW.description, NEW.stage_order, NEW.stage,
            NEW.gateway_qualitative_scorecard, NEW.date_started, NEW.date_completed, NEW.completion_notes,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSE
        RAISE EXCEPTION 'Unexpected TG_OP value: %', TG_OP;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_project_stage_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_project_stage_changes" () IS 'Audit trigger function for project_stage table';

-- Audit trigger for project_stage table
CREATE OR REPLACE TRIGGER "audit_project_stage_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."project_stage" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_project_stage_changes" ();

-- Row Level Security Policies
CREATE POLICY "System can insert project stage audit records" ON "public"."project_stage_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view project stage audit for accessible projects" ON "public"."project_stage_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));
