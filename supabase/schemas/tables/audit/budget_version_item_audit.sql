-- Budget Version Item Audit Table Schema
-- Minimal audit log of budget version item changes (not full row copies)
-- Budget Version Item Audit table
CREATE TABLE IF NOT EXISTS "public"."budget_version_item_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"budget_version_item_id" "uuid",
	"budget_version_id" "uuid",
	"wbs_library_item_id" "uuid",
	"project_id" "uuid", -- Denormalized for easier querying
	"notes" "jsonb", -- Minimal additional context if needed
	CONSTRAINT "budget_version_item_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."budget_version_item_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_version_item_audit" IS 'Minimal audit log of budget version item changes (item id, action, who/when)';

-- Primary key constraint
ALTER TABLE ONLY "public"."budget_version_item_audit"
ADD CONSTRAINT "budget_version_item_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."budget_version_item_audit"
ADD CONSTRAINT "budget_version_item_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "budget_version_item_audit_changed_at_idx" ON "public"."budget_version_item_audit" USING "btree" ("changed_at");

CREATE INDEX "budget_version_item_audit_budget_version_item_id_idx" ON "public"."budget_version_item_audit" USING "btree" ("budget_version_item_id");

CREATE INDEX "budget_version_item_audit_budget_version_id_idx" ON "public"."budget_version_item_audit" USING "btree" ("budget_version_id");

CREATE INDEX "budget_version_item_audit_project_id_idx" ON "public"."budget_version_item_audit" USING "btree" ("project_id");

-- Enable Row Level Security
ALTER TABLE "public"."budget_version_item_audit" ENABLE ROW LEVEL SECURITY;

-- Budget Version Item Audit Function (minimal logging)
CREATE OR REPLACE FUNCTION "public"."audit_budget_version_item_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
    v_project_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    -- Get project_id from budget_version for easier querying
    IF TG_OP = 'DELETE' THEN
        SELECT v.project_id INTO v_project_id
        FROM public.budget_version v
        WHERE v.budget_version_id = OLD.budget_version_id;
        
        INSERT INTO public.budget_version_item_audit (
            operation_type, changed_by, changed_at,
            budget_version_item_id, budget_version_id, wbs_library_item_id, project_id
        ) VALUES (
            'DELETE', v_user_id, NOW(),
            OLD.budget_version_item_id, OLD.budget_version_id, OLD.wbs_library_item_id, v_project_id
        );
        RETURN OLD;
    ELSE
        SELECT v.project_id INTO v_project_id
        FROM public.budget_version v
        WHERE v.budget_version_id = NEW.budget_version_id;
        
        IF TG_OP = 'UPDATE' THEN
            INSERT INTO public.budget_version_item_audit (
                operation_type, changed_by, changed_at,
                budget_version_item_id, budget_version_id, wbs_library_item_id, project_id
            ) VALUES (
                'UPDATE', v_user_id, NOW(),
                NEW.budget_version_item_id, NEW.budget_version_id, NEW.wbs_library_item_id, v_project_id
            );
        ELSIF TG_OP = 'INSERT' THEN
            INSERT INTO public.budget_version_item_audit (
                operation_type, changed_by, changed_at,
                budget_version_item_id, budget_version_id, wbs_library_item_id, project_id
            ) VALUES (
                'INSERT', v_user_id, NOW(),
                NEW.budget_version_item_id, NEW.budget_version_id, NEW.wbs_library_item_id, v_project_id
            );
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_budget_version_item_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_budget_version_item_changes" () IS 'Minimal audit trigger function for budget_version_item table';

-- Audit trigger for budget_version_item table
CREATE OR REPLACE TRIGGER "audit_budget_version_item_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."budget_version_item" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_budget_version_item_changes" ();

-- Row Level Security Policies
CREATE POLICY "System can insert budget version item audit records" ON "public"."budget_version_item_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view budget version item audit for accessible projects" ON "public"."budget_version_item_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));
