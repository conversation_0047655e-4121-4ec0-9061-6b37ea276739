-- Project Audit Table Schema
-- Contains audit log of all changes to project table
-- Project audit table
CREATE TABLE IF NOT EXISTS "public"."project_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL CHECK (
		"operation_type" IN ('INSERT', 'UPDATE', 'DELETE')
	),
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	-- Original table columns for easier querying
	"project_id" "uuid",
	"name" "text",
	"description" "text",
	"created_by_user_id" "uuid",
	"client_id" "uuid",
	"wbs_library_id" "uuid",
	"construction_stage_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

ALTER TABLE "public"."project_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."project_audit" IS 'Audit log of all changes to projects';

-- Primary key constraint
ALTER TABLE ONLY "public"."project_audit"
ADD CONSTRAINT "project_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraint
ALTER TABLE ONLY "public"."project_audit"
ADD CONSTRAINT "project_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Enable Row Level Security
ALTER TABLE "public"."project_audit" ENABLE ROW LEVEL SECURITY;

-- Indexes for performance
CREATE INDEX "idx_project_audit_project_id" ON "public"."project_audit" ("project_id");

CREATE INDEX "idx_project_audit_changed_by" ON "public"."project_audit" ("changed_by");

CREATE INDEX "idx_project_audit_changed_at" ON "public"."project_audit" ("changed_at");

CREATE INDEX "idx_project_audit_operation_type" ON "public"."project_audit" ("operation_type");

-- Audit trigger function for project
CREATE OR REPLACE FUNCTION "public"."audit_project_changes" () RETURNS TRIGGER SECURITY DEFINER
SET
	search_path = '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.project_audit (
            operation_type, changed_by, changed_at, old_values,
            project_id, name, description, created_by_user_id, client_id,
            wbs_library_id, construction_stage_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.project_id, OLD.name, OLD.description, OLD.created_by_user_id, OLD.client_id,
            OLD.wbs_library_id, OLD.construction_stage_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.project_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            project_id, name, description, created_by_user_id, client_id,
            wbs_library_id, construction_stage_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.project_id, NEW.name, NEW.description, NEW.created_by_user_id, NEW.client_id,
            NEW.wbs_library_id, NEW.construction_stage_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.project_audit (
            operation_type, changed_by, changed_at, new_values,
            project_id, name, description, created_by_user_id, client_id,
            wbs_library_id, construction_stage_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.project_id, NEW.name, NEW.description, NEW.created_by_user_id, NEW.client_id,
            NEW.wbs_library_id, NEW.construction_stage_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Project Audit Trigger
CREATE TRIGGER "audit_project_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."project" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_project_changes" ();

-- Row Level Security Policies
CREATE POLICY "Users can view project audit for accessible projects" ON "public"."project_audit" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);

-- Only system can insert audit records (via triggers)
CREATE POLICY "System can insert project audit records" ON "public"."project_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

-- Grant permissions to service role
GRANT
SELECT
,
	INSERT,
UPDATE,
DELETE,
REFERENCES,
TRIGGER,
TRUNCATE ON "public"."project_audit" TO "service_role";
