-- Vendor Audit Table Schema
-- Audit log of all changes to vendor entries
-- Vendor Audit table
CREATE TABLE IF NOT EXISTS "public"."vendor_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL CHECK (
		"operation_type" IN ('INSERT', 'UPDATE', 'DELETE')
	),
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	-- Original table columns
	"vendor_id" "uuid",
	"name" "text",
	"description" "text",
	"org_id" "uuid",
	"client_id" "uuid",
	"project_id" "uuid",
	"contact_name" "text",
	"contact_email" "text",
	"contact_phone" "text",
	"contact_address" "text",
	"website" "text",
	"vendor_type" "text",
	"tax_id" "text",
	"payment_terms" "text",
	"payment_terms_days" integer,
	"credit_limit" numeric(15, 2),
	"currency" "text",
	"is_active" boolean,
	"certification_info" "jsonb",
	"insurance_info" "jsonb",
	"additional_data" "jsonb",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

ALTER TABLE "public"."vendor_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."vendor_audit" IS 'Audit log of all changes to vendor entries';

-- Primary key constraint for audit table
ALTER TABLE ONLY "public"."vendor_audit"
ADD CONSTRAINT "vendor_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraint for audit table
ALTER TABLE ONLY "public"."vendor_audit"
ADD CONSTRAINT "vendor_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for audit table performance
CREATE INDEX "vendor_audit_vendor_id_idx" ON "public"."vendor_audit" USING "btree" ("vendor_id");

CREATE INDEX "vendor_audit_changed_by_idx" ON "public"."vendor_audit" USING "btree" ("changed_by");

CREATE INDEX "vendor_audit_changed_at_idx" ON "public"."vendor_audit" USING "btree" ("changed_at");

CREATE INDEX "vendor_audit_operation_type_idx" ON "public"."vendor_audit" USING "btree" ("operation_type");

-- Enable Row Level Security for audit table
ALTER TABLE "public"."vendor_audit" ENABLE ROW LEVEL SECURITY;

-- Row Level Security Policies for Audit Table
CREATE POLICY "System can insert vendor audit records" ON "public"."vendor_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view vendor audit for accessible vendors" ON "public"."vendor_audit" FOR
SELECT
	TO "authenticated" USING (
		(
			-- Organization-level vendors: user has access to the organization
			(
				"org_id" IS NOT NULL
				AND "public"."current_user_has_entity_access" ('organization'::"public"."entity_type", "org_id")
			)
			OR
			-- Client-level vendors: user has access to the client or its organization
			(
				"client_id" IS NOT NULL
				AND (
					"public"."current_user_has_entity_access" ('client'::"public"."entity_type", "client_id")
					OR EXISTS (
						SELECT
							1
						FROM
							"public"."client" "c"
						WHERE
							"c"."client_id" = "vendor_audit"."client_id"
							AND "public"."current_user_has_entity_access" (
								'organization'::"public"."entity_type",
								"c"."org_id"
							)
					)
				)
			)
			OR
			-- Project-level vendors: user has access to the project, its client, or organization
			(
				"project_id" IS NOT NULL
				AND (
					"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
					OR EXISTS (
						SELECT
							1
						FROM
							"public"."project" "p"
							JOIN "public"."client" "c" ON "p"."client_id" = "c"."client_id"
						WHERE
							"p"."project_id" = "vendor_audit"."project_id"
							AND (
								"public"."current_user_has_entity_access" ('client'::"public"."entity_type", "p"."client_id")
								OR "public"."current_user_has_entity_access" (
									'organization'::"public"."entity_type",
									"c"."org_id"
								)
							)
					)
				)
			)
		)
	);
