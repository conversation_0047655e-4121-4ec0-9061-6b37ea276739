-- Budget Version Audit Table Schema
-- Audit log of all changes to budget versions
-- Budget Version Audit table
CREATE TABLE IF NOT EXISTS "public"."budget_version_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"budget_version_id" "uuid",
	"project_id" "uuid",
	"label" "text",
	"kind" "public"."budget_version_kind",
	"stage_id" "uuid",
	"prev_version_id" "uuid",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "budget_version_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."budget_version_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_version_audit" IS 'Audit log of all changes to budget versions';

-- Primary key constraint
ALTER TABLE ONLY "public"."budget_version_audit"
ADD CONSTRAINT "budget_version_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."budget_version_audit"
ADD CONSTRAINT "budget_version_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "budget_version_audit_changed_at_idx" ON "public"."budget_version_audit" USING "btree" ("changed_at");

CREATE INDEX "budget_version_audit_budget_version_id_idx" ON "public"."budget_version_audit" USING "btree" ("budget_version_id");

CREATE INDEX "budget_version_audit_project_id_idx" ON "public"."budget_version_audit" USING "btree" ("project_id");

-- Enable Row Level Security
ALTER TABLE "public"."budget_version_audit" ENABLE ROW LEVEL SECURITY;

-- Budget Version Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_budget_version_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.budget_version_audit (
            operation_type, changed_by, changed_at, old_values,
            budget_version_id, project_id, label, kind, stage_id, prev_version_id,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.budget_version_id, OLD.project_id, OLD.label, OLD.kind, OLD.stage_id, OLD.prev_version_id,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.budget_version_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            budget_version_id, project_id, label, kind, stage_id, prev_version_id,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.budget_version_id, NEW.project_id, NEW.label, NEW.kind, NEW.stage_id, NEW.prev_version_id,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.budget_version_audit (
            operation_type, changed_by, changed_at, new_values,
            budget_version_id, project_id, label, kind, stage_id, prev_version_id,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.budget_version_id, NEW.project_id, NEW.label, NEW.kind, NEW.stage_id, NEW.prev_version_id,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_budget_version_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_budget_version_changes" () IS 'Audit trigger function for budget_version table';

-- Audit trigger for budget_version table
CREATE OR REPLACE TRIGGER "audit_budget_version_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."budget_version" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_budget_version_changes" ();

-- Row Level Security Policies
CREATE POLICY "System can insert budget version audit records" ON "public"."budget_version_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view budget version audit for accessible projects" ON "public"."budget_version_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));
