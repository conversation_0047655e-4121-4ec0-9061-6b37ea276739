-- Budget Snapshot Line Item Table Schema
-- Line items within budget snapshots
-- Budget Snapshot Line Item table
CREATE TABLE IF NOT EXISTS "public"."budget_snapshot_line_item" (
	"budget_snapshot_line_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"budget_snapshot_id" "uuid" NOT NULL,
	"wbs_library_item_id" "uuid" NOT NULL,
	"quantity" numeric(20, 4),
	"unit" "text",
	"material_rate" numeric(20, 4),
	"labor_rate" numeric(20, 4),
	"productivity_per_hour" numeric(20, 4),
	"unit_rate_manual_override" boolean DEFAULT false NOT NULL,
	"unit_rate" numeric(20, 4),
	"factor" numeric(20, 4),
	"remarks" "text",
	"cost_certainty" numeric(5, 2),
	"design_certainty" numeric(5, 2),
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."budget_snapshot_line_item" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_snapshot_line_item" IS 'Line items within budget snapshots';

-- Primary key constraint
ALTER TABLE ONLY "public"."budget_snapshot_line_item"
ADD CONSTRAINT "budget_snapshot_line_item_pkey" PRIMARY KEY ("budget_snapshot_line_item_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."budget_snapshot_line_item"
ADD CONSTRAINT "budget_snapshot_line_item_budget_snapshot_id_fkey" FOREIGN KEY ("budget_snapshot_id") REFERENCES "public"."budget_snapshot" ("budget_snapshot_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_snapshot_line_item"
ADD CONSTRAINT "budget_snapshot_line_item_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "budget_snapshot_line_item_budget_snapshot_id_idx" ON "public"."budget_snapshot_line_item" USING "btree" ("budget_snapshot_id");

CREATE INDEX "budget_snapshot_line_item_wbs_library_item_id_idx" ON "public"."budget_snapshot_line_item" USING "btree" ("wbs_library_item_id");

-- Enable Row Level Security
ALTER TABLE "public"."budget_snapshot_line_item" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."budget_snapshot_line_item" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
CREATE POLICY "Project editors can insert budget snapshot line item" ON "public"."budget_snapshot_line_item" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"budget_snapshot"."project_stage_id"
									FROM
										"public"."budget_snapshot"
									WHERE
										(
											"budget_snapshot"."budget_snapshot_id" = "budget_snapshot_line_item"."budget_snapshot_id"
										)
								)
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can update budget snapshot line item" ON "public"."budget_snapshot_line_item"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"budget_snapshot"."project_stage_id"
									FROM
										"public"."budget_snapshot"
									WHERE
										(
											"budget_snapshot"."budget_snapshot_id" = "budget_snapshot_line_item"."budget_snapshot_id"
										)
								)
							)
					)
				) AS "can_modify_project"
		)
	)
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"budget_snapshot"."project_stage_id"
									FROM
										"public"."budget_snapshot"
									WHERE
										(
											"budget_snapshot"."budget_snapshot_id" = "budget_snapshot_line_item"."budget_snapshot_id"
										)
								)
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can delete budget snapshot line item" ON "public"."budget_snapshot_line_item" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" (
				(
					SELECT
						"project_stage"."project_id"
					FROM
						"public"."project_stage"
					WHERE
						(
							"project_stage"."project_stage_id" = (
								SELECT
									"budget_snapshot"."project_stage_id"
								FROM
									"public"."budget_snapshot"
								WHERE
									(
										"budget_snapshot"."budget_snapshot_id" = "budget_snapshot_line_item"."budget_snapshot_id"
									)
							)
						)
				)
			) AS "can_modify_project"
	)
);

CREATE POLICY "Project viewers can view budget snapshot line item" ON "public"."budget_snapshot_line_item" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"budget_snapshot"."project_stage_id"
									FROM
										"public"."budget_snapshot"
									WHERE
										(
											"budget_snapshot"."budget_snapshot_id" = "budget_snapshot_line_item"."budget_snapshot_id"
										)
								)
							)
					)
				) AS "can_access_project"
		)
	);
