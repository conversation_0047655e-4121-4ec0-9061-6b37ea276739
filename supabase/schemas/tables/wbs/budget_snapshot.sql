-- Budget Snapshot Table Schema
-- Snapshots of budget state at specific project stages
-- Budget Snapshot table
CREATE TABLE IF NOT EXISTS "public"."budget_snapshot" (
	"budget_snapshot_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_stage_id" "uuid" NOT NULL,
	"freeze_date" timestamp with time zone DEFAULT "now" () NOT NULL,
	"freeze_reason" "text",
	"created_by_user_id" "uuid" NOT NULL,
	"budget_version_id" "uuid",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."budget_snapshot" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_snapshot" IS 'Snapshots of budget state at specific project stages';

-- Primary key constraint
ALTER TABLE ONLY "public"."budget_snapshot"
ADD CONSTRAINT "budget_snapshot_pkey" PRIMARY KEY ("budget_snapshot_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."budget_snapshot"
ADD CONSTRAINT "budget_snapshot_project_stage_id_fkey" FOREIGN KEY ("project_stage_id") REFERENCES "public"."project_stage" ("project_stage_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_snapshot"
ADD CONSTRAINT "budget_snapshot_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "budget_snapshot_created_by_user_id_idx" ON "public"."budget_snapshot" USING "btree" ("created_by_user_id");

CREATE INDEX "budget_snapshot_project_stage_id_idx" ON "public"."budget_snapshot" USING "btree" ("project_stage_id");

CREATE INDEX IF NOT EXISTS "budget_snapshot_budget_version_id_idx" ON "public"."budget_snapshot" USING "btree" ("budget_version_id");

-- Enable Row Level Security
ALTER TABLE "public"."budget_snapshot" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."budget_snapshot" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
CREATE POLICY "Project viewers can insert budget snapshot" ON "public"."budget_snapshot" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "budget_snapshot"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can update budget snapshot" ON "public"."budget_snapshot"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "budget_snapshot"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	)
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "budget_snapshot"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can delete budget snapshot" ON "public"."budget_snapshot" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" (
				(
					SELECT
						"project_stage"."project_id"
					FROM
						"public"."project_stage"
					WHERE
						(
							"project_stage"."project_stage_id" = "budget_snapshot"."project_stage_id"
						)
				)
			) AS "can_modify_project"
	)
);

CREATE POLICY "Project viewers can view budget snapshot" ON "public"."budget_snapshot" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "budget_snapshot"."project_stage_id"
							)
					)
				) AS "can_access_project"
		)
	);
