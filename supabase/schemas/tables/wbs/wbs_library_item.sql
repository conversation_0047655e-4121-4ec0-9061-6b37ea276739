-- WBS Library Item Table Schema
-- Individual items within a WBS library, organized hierarchically
-- WBS Library Item table
CREATE TABLE IF NOT EXISTS "public"."wbs_library_item" (
	"wbs_library_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"wbs_library_id" "uuid" NOT NULL,
	"level" integer NOT NULL,
	"in_level_code" "text" NOT NULL,
	"parent_item_id" "uuid",
	"code" "text" NOT NULL,
	"description" "text" NOT NULL,
	"cost_scope" "text",
	"item_type" "public"."wbs_item_type" DEFAULT 'Custom'::"public"."wbs_item_type" NOT NULL,
	"client_id" "uuid",
	"project_id" "uuid",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."wbs_library_item" OWNER TO "postgres";

COMMENT ON TABLE "public"."wbs_library_item" IS 'Individual items within a WBS library, organized hierarchically';

COMMENT ON COLUMN "public"."wbs_library_item"."level" IS 'Hierarchical level (1=top level, 2=sub-category, etc.)';

COMMENT ON COLUMN "public"."wbs_library_item"."in_level_code" IS 'Code within the current level (e.g., "01", "02")';

COMMENT ON COLUMN "public"."wbs_library_item"."code" IS 'Full hierarchical code (e.g., "01.02.03")';

COMMENT ON COLUMN "public"."wbs_library_item"."item_type" IS 'Standard items are from libraries like ICMS, Custom items are project-specific';

-- Primary key constraint
ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_pkey" PRIMARY KEY ("wbs_library_item_id");

-- Partial unique indexes
CREATE UNIQUE INDEX wbs_library_item_standard_uq ON public.wbs_library_item (wbs_library_id, code)
WHERE
	item_type = 'Standard';

CREATE UNIQUE INDEX wbs_library_item_project_uq ON public.wbs_library_item (project_id, code)
WHERE
	item_type = 'Custom'
	AND project_id IS NOT NULL;

CREATE UNIQUE INDEX wbs_library_item_client_uq ON public.wbs_library_item (client_id, code)
WHERE
	item_type = 'Custom'
	AND project_id IS NULL;

-- Foreign key constraints
ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_wbs_library_id_fkey" FOREIGN KEY ("wbs_library_id") REFERENCES "public"."wbs_library" ("wbs_library_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_parent_item_id_fkey" FOREIGN KEY ("parent_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "public"."client" ("client_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "wbs_library_item_parent_item_id_idx" ON "public"."wbs_library_item" USING "btree" ("parent_item_id");

CREATE INDEX "wbs_library_item_wbs_library_id_idx" ON "public"."wbs_library_item" USING "btree" ("wbs_library_id");

CREATE INDEX "wbs_library_item_client_idx" ON "public"."wbs_library_item" USING "btree" ("client_id");

CREATE INDEX "wbs_library_item_project_idx" ON "public"."wbs_library_item" USING "btree" ("project_id");

-- Enable Row Level Security
ALTER TABLE "public"."wbs_library_item" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."wbs_library_item" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
-- Combined policy for viewing WBS library items (both standard and custom)
CREATE POLICY "Users can view WBS library items" ON "public"."wbs_library_item" FOR
SELECT
	TO "authenticated" USING (
		"item_type" = 'Standard'::"public"."wbs_item_type"
		OR (
			"item_type" = 'Custom'::"public"."wbs_item_type"
			AND (
				"public"."current_user_has_entity_access" ('client'::"public"."entity_type", "client_id")
				OR (
					"project_id" IS NOT NULL
					AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
				)
			)
		)
	);

-- Service role can manage standard library items
CREATE POLICY "Service role can insert standard WBS library item" ON "public"."wbs_library_item" FOR INSERT TO "service_role"
WITH
	CHECK (
		(
			"item_type" = 'Standard'::"public"."wbs_item_type"
		)
	);

CREATE POLICY "Service role can update standard WBS library item" ON "public"."wbs_library_item"
FOR UPDATE
	TO "service_role" USING (
		(
			"item_type" = 'Standard'::"public"."wbs_item_type"
		)
	)
WITH
	CHECK (
		(
			"item_type" = 'Standard'::"public"."wbs_item_type"
		)
	);

CREATE POLICY "Service role can delete standard WBS library item" ON "public"."wbs_library_item" FOR DELETE TO "service_role" USING (
	(
		"item_type" = 'Standard'::"public"."wbs_item_type"
	)
);

-- Client/project admins can manage custom items
CREATE POLICY "Client/project admins can insert custom WBS library item" ON "public"."wbs_library_item" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			("item_type" = 'Custom'::"public"."wbs_item_type")
			AND (
				(
					("project_id" IS NOT NULL)
					AND "public"."can_modify_project" ("project_id")
				)
				OR "public"."can_modify_client_wbs" ("client_id")
			)
		)
	);

CREATE POLICY "Client/project admins can update custom WBS library item" ON "public"."wbs_library_item"
FOR UPDATE
	TO "authenticated" USING (
		(
			("item_type" = 'Custom'::"public"."wbs_item_type")
			AND (
				(
					("project_id" IS NOT NULL)
					AND "public"."can_modify_project" ("project_id")
				)
				OR "public"."can_modify_client_wbs" ("client_id")
			)
		)
	)
WITH
	CHECK (
		(
			("item_type" = 'Custom'::"public"."wbs_item_type")
			AND (
				(
					("project_id" IS NOT NULL)
					AND "public"."can_modify_project" ("project_id")
				)
				OR "public"."can_modify_client_wbs" ("client_id")
			)
		)
	);

CREATE POLICY "Client/project admins can delete custom WBS library item" ON "public"."wbs_library_item" FOR DELETE TO "authenticated" USING (
	(
		("item_type" = 'Custom'::"public"."wbs_item_type")
		AND (
			(
				("project_id" IS NOT NULL)
				AND "public"."can_modify_project" ("project_id")
			)
			OR "public"."can_modify_client_wbs" ("client_id")
		)
	)
);
