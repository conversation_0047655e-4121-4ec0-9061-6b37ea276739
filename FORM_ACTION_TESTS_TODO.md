# Form Action Tests TODO

format: <form action name> — <path to the file>

- [ ] selectOrganization — src/routes/api/org-selection/+page.server.ts
- [ ] default — src/routes/auth/change-password/+page.server.ts
- [ ] default — src/routes/auth/reset-password/+page.server.ts
- [ ] default — src/routes/auth/signin/+page.server.ts
- [ ] accept — src/routes/auth/invite/+page.server.ts
- [ ] default — src/routes/auth/signup/+page.server.ts
- [ ] default — src/routes/settings/+page.server.ts
- [ ] default — src/routes/organizations/new/+page.server.ts
- [ ] updateItem — src/routes/clients/[client_name]/wbs/[wbs_item_id]/+page.server.ts
- [ ] default — src/routes/organizations/[orgId]/members/new/+page.server.ts
- [ ] default — src/routes/clients/[client_name]/projects/new/+page.server.ts
- [ ] createItem — src/routes/clients/[client_name]/wbs/+page.server.ts
- [ ] remove — src/routes/organizations/[orgId]/members/+page.server.ts
- [ ] invite — src/routes/clients/[client_name]/invite/+page.server.ts
- [ ] default — src/routes/organizations/[orgId]/invite/+page.server.ts
- [ ] default — src/routes/clients/[client_name]/edit/+page.server.ts
- [ ] updateBudgetItem — src/routes/clients/[client_name]/projects/[project_id_short]/budget/edit/+page.server.ts
- [ ] upsertBudgetItem — src/routes/clients/[client_name]/projects/[project_id_short]/budget/+page.server.ts
- [ ] default — src/routes/organizations/[orgId]/settings/+page.server.ts
- [ ] default — src/routes/clients/[client_name]/projects/[project_id_short]/edit/+page.server.ts
- [ ] default — src/routes/clients/new/+page.server.ts
- [ ] createItem — src/routes/clients/[client_name]/projects/[project_id_short]/wbs/+page.server.ts
- [ ] updateChecklist — src/routes/clients/[client_name]/projects/[project_id_short]/stage-[stage_order]/gateway/+page.server.ts
