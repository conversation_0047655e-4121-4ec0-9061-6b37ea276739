# Project Controls

Project Controls is a SvelteKit application for managing construction projects, budgets and team access. It uses Supabase for authentication and database storage and is built with TypeScript and TailwindCSS. End-to-end tests are written with Playwright and unit tests with Vitest. Storybook is included for UI development.

## Stack Overview

```mermaid
graph TD
  Browser --> |SSR & API| SvelteKit
  SvelteKit --> |Auth & RPC| Supabase
  Supabase --> Postgres[(PostgreSQL)]
  Supabase --> Storage[(Storage)]
```

## Invite Flow

```mermaid
sequenceDiagram
  participant User
  participant App
  participant Supabase
  participant Email

  User->>App: request invite
  App->>Supabase: create invite record
  App->>Email: send invite link
  User->>App: follow invite link
  App->>Supabase: validate token & join
```

## Running Locally

1. Install dependencies

```bash
pnpm install
```

2. Create an `.env` file and provide the following variables:

```
PUBLIC_SUPABASE_URL=<supabase-url>
PUBLIC_SUPABASE_ANON_KEY=<anon-key>
SUPABASE_SERVICE_KEY=<service-key>
RESEND_API_KEY=<resend-api-key>
PUBLIC_FROM_EMAIL_ADDRESS=<from-email>
PUBLIC_VERCEL_URL=localhost:5173
SUPABASE_JWT_SECRET=<jwt-secret>
```

3. Start Supabase locally if desired

```bash
supabase start
```

4. Run the development server

```bash
pnpm dev -- --open
```

## Scripts

- `pnpm dev` – start local development server
- `pnpm build` – build production bundle
- `pnpm preview` – preview built app
- `pnpm test:unit` – run unit tests with Vitest
- `pnpm test:e2e` – run Playwright tests
- `pnpm storybook` – run Storybook UI environment

## Testing

Unit tests are located under `src/tests`. Playwright e2e tests live in `e2e`. Run all tests with:

```bash
pnpm test
```

---

This repository also contains database migrations under `supabase/migrations` and seed data in `supabase/seeds` for the local Supabase instance.
