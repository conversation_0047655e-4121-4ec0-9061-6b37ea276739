# Budget Import Plan

We are going to create a user interface flow for importing an excel spreadsheet into a construction cost controls web app. The spreadsheet is an export from CostX, which has a flexible interface and allows users to group items in multiple ways. It shows a construction budget broken down with a work breakdown structure which has a hierarchical code structure.
We cannot get a CSV export. We cannot count on the columns always having the same names. We think the columns will be in a consistent order but we should not count on that. These are non-technical users so we are not exposing technical or implementation details. The goals are accuracy and simplicity, then speed and ease of use.

## Expected file description

### The CostX export is a report. It is not designed to be read by a machine.

We expect only one sheet. If there are multiple sheets, we will only look at the first one.
There are some report title rows in the Excel file.
There are empty rows.

There is a row around row 4 that has column definitions for the rest of the file. The expected column names in the Excel file are:

```
Code
Description
Quantity
UOM
Rate
SubTotal
Factor
Total
Total
Total
Total
Total
User1
User2
User3
User4
```

### App database info

Those import columns need to be matched to the existing `budget_line_item_current` table which has the following columns:

```sql
budget_line_item_id bigint generated always as identity primary key,
project_id uuid not null references project (project_id) on update restrict on delete restrict,
wbs_library_item_id uuid not null references wbs_library_item (wbs_library_item_id) on update restrict on delete restrict,
quantity numeric(15, 2) not null,
unit text,
material_rate numeric(15, 2) not null,
labor_rate numeric(15, 2),
productivity_per_hour numeric(15, 2),
unit_rate_manual_override boolean not null default false,
unit_rate numeric(15, 2) not null,
factor numeric(15, 2),
remarks text,
cost_certainty numeric(5, 2),
design_certainty numeric(5, 2),
created_at timestamptz default timezone ('utc'::text, now()) not null,
updated_at timestamptz default timezone ('utc'::text, now()) not null
```

with that foreign key reference to the `wbs_library_item` table:

```sql
wbs_library_item_id uuid not null default gen_random_uuid () primary key,
wbs_library_id bigint not null references wbs_library (wbs_library_id) on update restrict on delete restrict,
level integer not null,
in_level_code text not null,
parent_item_id uuid references wbs_library_item (wbs_library_item_id),
code text not null,
description text not null,
cost_scope text,
item_type public.wbs_item_type not null default 'Custom',
client_id uuid references client (client_id),
project_id uuid references project (project_id),
created_at timestamptz default timezone ('utc'::text, now()) not null,
updated_at timestamptz default timezone ('utc'::text, now()) not null
```

The Excel `code` column should be create a `wbs_library_item.code` and the `description` should become that row's `wbs_library_item.cost_scope`
Quantity is matched to `budget_line_item_current.quantity`
UOM is matched to `budget_line_item_current.unit`
Rate is matched to `budget_line_item_current.material_rate`
Factor is matched to `budget_line_item_current.factor`
SubTotal, Total and the other columns will not be saved in the database

There are also rows which do not have a "code" but do have a description. This is a user-defined group, set off from the rest of the table with empty lines above and below the group. We are going to edit the description of each item in the group to include that category name as a prefix in square brackets. For example, given this group of items:

```md
| Code      | Description                                                                                                                              | Quantity | UOM  |   Rate | SubTotal | Factor |  Total |
| --------- | ---------------------------------------------------------------------------------------------------------------------------------------- | :------: | :--: | -----: | -------: | :----: | -----: |
|           | Plan 1                                                                                                                                   |          |      |        |          |        |        |
| *******.1 | Form 1100×2100 mm door opening in existing wall, including cutting, edge finishing, and disposal.                                        |    4     | Nos  | 16 220 |   64 880 |        | 64 880 |
| *******.2 | Demolition and removal of existing single sash doors in areas upgraded from CNC to Grade D (RKD)                                         |    6     | Nos  |    500 |    3 000 |        |  3 000 |
| *******.3 | Dismantle and remove wall-mounted lockers (09 nos) from rooms 187A & 187F, including surface patching and preparation for possible reuse |    1     | Item | 23 300 |   23 300 |        | 23 300 |
```

We are going to transform those descriptions like this:

```md
| Code      | Description                                                                                                                                       | Quantity | UOM  |   Rate | SubTotal | Factor |  Total |
| --------- | ------------------------------------------------------------------------------------------------------------------------------------------------- | :------: | :--: | -----: | -------: | :----: | -----: |
|           | Plan 1                                                                                                                                            |          |      |        |          |        |        |
| *******.1 | [Plan 1] Form 1100×2100 mm door opening in existing wall, including cutting, edge finishing, and disposal.                                        |    4     | Nos  | 16 220 |   64 880 |        | 64 880 |
| *******.2 | [Plan 1] Demolition and removal of existing single sash doors in areas upgraded from CNC to Grade D (RKD)                                         |    6     | Nos  |    500 |    3 000 |        |  3 000 |
| *******.3 | [Plan 1] Dismantle and remove wall-mounted lockers (09 nos) from rooms 187A & 187F, including surface patching and preparation for possible reuse |    1     | Item | 23 300 |   23 300 |        | 23 300 |
```

There can be multiple layers of these groups and that is ok. We will then use multiple prefixes like `[Outer Group][Inner Group]`.

There are rows showing totals which may contain the "Factor" used for its category. These rows do not show a code and the Description column starts with "TOTAL " and then a string which matches a description used above in a row which does have a code. For example, given this group of items:

```
| Code      | Description                                                                                      | Quantity | UOM | Rate    | SubTotal | Factor | Total   | Total   |
| --------- | ------------------------------------------------------------------------------------------------ | -------- | --- | ------- | -------- | ------ | ------- | ------- |
| 1.2.2     | Superstructure                                                                                   |          |     |         |          |        |         |         |
| *******   | Frame                                                                                            |          |     |         |          |        |         |         |
|           | Structural support for new AHU unit                                                              |          |     |         |          |        |         |         |
| *******.1 | Install additional beams under the new AHU position to provide sufficient support and anchoring. | 1        | P,S | 1000000 | 1000000  |        | 1000000 |         |
| *******.2 | (20x 3 m) = 60 m²                                                                                |          |     |         |          |        |         |         |
|           | TOTAL Frame.                                                                                     |          |     |         | 1000000  | 0.75   |         | 1000000 |
```

the factor shown in the last row with a description of `TOTAL Frame` applies to the code `*******`.

If a cell in the SubTotal column has a yellow fill, that is CostX telling us that there is hidden detail. There are additional "levels" that are entered in CostX and can be exported by increasing that option in the export dialog.

Only rows with a code will become rows in the database, with the code and description going into the `wbs_library_item` table and a fk reference to that item along with the other columns in the `budget_line_item_current` table.

## UI Steps:

1- File drop zone with a link to CostX export instructions and a brief description of how the import will work.
2- Table showing the file without edits. - Show a message if there are yellow fill SubTotal cells about increasing the levels in the export. - Alert the user if the file has multiple sheets. - Alert the user if there are any other issues with the file.
3- Show the automatic classification of the columns - ask for confirmation and assistance with anything unclear. - If necessary columns are not included, make that clear.
4- Show the automatic classification of the rows. - A new first column should be a select dropdown showing the interpretation: `detail`, `summary`, `category` or `ignore`. The user can edit these as needed. - The `category` option should have a text input in the description column prefilled with the category name pulled from the import. This will allow the user to edit it if necessary. - Each member of a category should have its new description prefixed with the category name in square brackets shown with the edit highlighted to make clear what is changing. - `summary` and `ignore` rows should be greyed out or otherwise indicated as not being imported. - If a `summary` row has a `factor` then that should be shown in the `factor` column for its corresponding `detail` row. The detail row should be highlighted to make clear what is changing.
5- Show a summary of the import, including the number of rows that will be imported and the total budget as recalculated from the imported data.
6- Submit - The Excel file is not uploaded. Only the data to be imported is uploaded. - The import is done in a transaction. If it fails, the user can try again. This will require creating a new postgres function that can be called from the supabase-js client as an RPC.
7- Confirm with a success toast and redirect to the imported budget

## Notes:

- All of this should start at the route `src/routes/org/[org_name]/clients/[client_name]/projects/[project_id_short]/budget/import/+page.svelte`.
- Intermediate state should be saved to `localStorage` so that the user can leave and come back.
- Use search params to store the step so that we do not need additional routes.
- Break down the steps into small, testable components.
- Use the File Drop Zone component in the UI for step 1. `src/lib/components/ui/file-drop-zone/file-drop-zone.svelte`. Use the prop `accept=".xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"`.
- The existing form schema at `src/routes/org/[org_name]/clients/[client_name]/projects/[project_id_short]/budget/import/schema.ts` needs to be changed. It assumes the excel file will be uploaded.
- Create tests for each step and component.
- Create Storybook stories for each component.
