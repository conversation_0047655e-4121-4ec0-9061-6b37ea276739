# Repository Guidelines

## Project Structure & Module Organization

- `src/`: app code. Key areas: `lib/` (components, schemas, server utils), `routes/` (pages + endpoints), `stories/`, `tests/`.
- `e2e/`: Playwright end-to-end tests and setup.
- `supabase/`: local database config, `migrations/`, `schemas/`, and `seeds/`.
- `.storybook/`: Storybook config and test setup.
- `static/`, `docs/`, `vendor/`: static assets, docs, and vendored packages.

## Build, Test, and Development Commands

- `pnpm dev`: start Vite dev server.
- `pnpm build`: production build (SvelteKit + Vite).
- `pnpm preview`: preview built app.
- `pnpm check`: type-check and Svelte diagnostics.
- `pnpm lint` / `pnpm format`: lint or format with ESLint/Prettier.
- `pnpm test`: run unit + e2e tests.
- `pnpm test:unit`: Vitest projects (client, server, ssr, storybook).
- `pnpm test:e2e`: Playwright tests in `e2e/`.
- `pnpm storybook`: Storybook on port 6006.
- `pnpm reset-db`: reset local Supabase and regen `src/lib/database.types.ts`.
- `pnpm test:mcp`: MCP-focused Playwright config (see `MCP_SETUP.md`).

## Coding Style & Naming Conventions

- Use TypeScript and Svelte 5. Prettier (2-space indent) and ESLint are enforced.
- Components: PascalCase in `src/lib/components` (e.g., `ProjectTable.svelte`).
- Server code: `.server.ts` files under `src/lib/server` and route endpoints.
- Imports: use aliases `@ $lib` and `$tests` (see `svelte.config.js`).

## Testing Guidelines

- Unit/integration tests live under `src/tests` (patterns: `*.{test,spec}.ts`).
- Browser component tests: `*.svelte.{test,spec}.ts` (Vitest browser env).
- SSR/server tests: `*.ssr.{test,spec}.ts` and Node env specs.
- E2E tests live in `e2e/`; Playwright config auto-starts the dev server.
- Coverage: `pnpm test:unit -- --coverage` (v8 coverage provider).

## Commit & Pull Request Guidelines

- Commits: present-tense, concise summary; scope where helpful (e.g., "auth: fix token refresh").
- Pre-commit hook runs `pretty-quick` to format staged files.
- PRs must include: clear description, linked issues, test plan/results, screenshots for UI, and notes for DB migrations (`supabase/migrations`). Run `pnpm lint` and `pnpm check` before submitting.

## Database Schema & Migrations

- Source of truth: files in `supabase/schemas/` (do NOT edit existing migration files).
- Change flow: edit schema files; if adding a new schema file, also add it to `supabase/config.toml` in the exact processing order.
- Generate migration: `supabase db diff -f <change_description>`; a timestamped file is created in `supabase/migrations/`.
- Review: open the generated migration and verify completeness/ordering; fix issues by adjusting schema files and re-running diff.
- Apply locally: run `pnpm reset-db` to run the migrations and regenerate types

## Security & Configuration Tips

- Never commit secrets. Use `.env` (see `README.md` for required keys).
- Supabase: `pnpm reset-db` wipes local data—use cautiously.
- Deployment adapter is Vercel; review Sentry/Resend keys in CI before enabling.
- For MCP/test automation details, read `MCP_SETUP.md`.

## MCP + Playwright

- Purpose: Drive the app via Playwright through MCP for agent workflows.
- Config: Codex MCP server is defined in `~/.codex/config.toml` under `[mcp_servers.server-name]` → `npx -y @playwright/mcp@latest`.
- Dev server: Run `pnpm dev` (defaults to `http://localhost:5173`).
- Run tests: `pnpm test:mcp` uses `playwright-mcp.config.ts` (Chromium, MCP-optimized).
- Helpful specs: see `e2e/*mcp*.test.ts` and helpers in `e2e/utils/mcp-helpers.ts`.
- Manual sign-in (local): navigate to `http://localhost:5173/auth/signin` and use `<EMAIL>` / `testtest`.
- Example project page: `http://localhost:5173/org/Aurora/clients/Maroon%20Dysprosium/projects/Marcuss%20Gata%2031/overview`.
