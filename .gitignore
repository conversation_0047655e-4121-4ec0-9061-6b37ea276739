test-results
node_modules

# Output
.output
.vercel
.netlify
.wrangler
/.svelte-kit
/build

# OS
.DS_Store
Thumbs.db

# Env
.env
.env.*
!.env.example
!.env.test

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

*storybook.log
storybook-static/**/*

**/.claude/settings.local.json

# playwright reporting
playwright-report
test-results-mcp

# Sentry Config File
.env.sentry-build-plugin

# Crush
.crush
