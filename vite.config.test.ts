import { defineConfig } from 'vite';
import { sveltekit } from '@sveltejs/kit/vite';
import tailwindcss from '@tailwindcss/vite';

// Test-specific Vite configuration that excludes Sentry plugin
// This prevents <PERSON><PERSON> from interfering with test execution
export default defineConfig({
	plugins: [tailwindcss(), sveltekit()],
	// Add optimizeDeps to prevent problematic dependencies during tests
	optimizeDeps: {
		exclude: ['@sentry/sveltekit', '@tailwindcss/oxide', 'lightningcss', 'fsevents', '__sveltekit'],
	},
	// Resolve configuration for test environment
	resolve: {
		alias: {
			// Mock problematic imports
			lightningcss: 'data:text/javascript,export default {}',
			__sveltekit: 'data:text/javascript,export default {}',
		},
	},
	// Define test-specific environment variables
	define: {
		// Disable Sentry in test environment
		'process.env.NODE_ENV': '"test"',
	},

	// Configure build to exclude problematic native dependencies
	build: {
		rollupOptions: {
			external: ['@tailwindcss/oxide-darwin-arm64', 'fsevents', 'lightningcss'],
		},
	},
});
