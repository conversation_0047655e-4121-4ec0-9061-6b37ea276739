#!/bin/bash
set -e

echo "🚀 Setting up SvelteKit project environment..."

# Update package lists
sudo apt-get update

# Install Node.js 20 (LTS) using NodeSource repository
echo "📦 Installing Node.js 20..."
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js installation
node --version
npm --version

# Install pnpm using npm with sudo to avoid permission issues
echo "📦 Installing pnpm 10.11.0..."
sudo npm install -g pnpm@10.11.0

# Verify pnpm installation
pnpm --version

# Navigate to the workspace directory
cd /mnt/persist/workspace

# Install project dependencies
echo "📦 Installing project dependencies..."
pnpm install

# Install Playwright system dependencies
echo "🎭 Installing Playwright system dependencies..."
sudo apt-get install -y \
    libnss3 \
    libnspr4 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libxkbcommon0 \
    libatspi2.0-0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libpango-1.0-0 \
    libcairo2 \
    libasound2

# Install Playwright browsers for testing
echo "🎭 Installing Playwright browsers..."
pnpm exec playwright install chromium

# Sync SvelteKit
echo "🔄 Syncing SvelteKit..."
pnpm prepare

echo "✅ Setup completed successfully!"