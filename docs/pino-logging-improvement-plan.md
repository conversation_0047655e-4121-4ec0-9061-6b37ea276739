# Pino Logging Improvement Plan for SvelteKit

## Current Implementation Analysis

### ✅ What's Working Well

- **Basic Setup**: <PERSON><PERSON> is properly installed and configured
- **Request Context**: AsyncLocalStorage is implemented for request tracking
- **Request Scoped Logging**: Each request gets a child logger with requestId, path, and method
- **Type Safety**: <PERSON><PERSON> is properly typed in `app.d.ts` as `locals.log`
- **Environment Configuration**: Log level is configurable via `PUBLIC_LOG_LEVEL`
- **Security**: Sensitive data is redacted (authorization headers, cookies, emails, tokens)
- **Request Lifecycle**: Start/end logging with duration tracking
- **Error Logging**: Already using structured logging for errors (seen in dashboard data fetching)

### 🔧 Areas for Improvement

## Recommended Improvements

### 1. **Enhanced Logger Configuration**

- **Issue**: <PERSON>gger uses `env.PUBLIC_LOG_LEVEL` but should use private env vars for server-side config
- **Issue**: Missing transport configuration for different environments
- **Issue**: No log rotation or file output configuration
- **Issue**: Service name is hardcoded as 'cost-atlas-ssr'

### 2. **Development Experience**

- **Issue**: No pretty printing in development
- **Issue**: Missing correlation IDs for distributed tracing
- **Issue**: No structured logging helpers for common patterns

### 3. **Production Optimization**

- **Issue**: No log aggregation setup
- **Issue**: Missing performance monitoring integration
- **Issue**: No log sampling for high-traffic scenarios

### 4. **Error Handling & Monitoring**

- **Issue**: No integration with existing Sentry setup
- **Issue**: Missing error context enrichment
- **Issue**: No automatic error correlation with request IDs

### 5. **Observability**

- **Issue**: Missing business metrics logging
- **Issue**: No database query logging
- **Issue**: Missing user action tracking

## Implementation Plan

### Phase 1: Core Logger Improvements

- [ ] Fix environment variable usage (use private env vars)
- [ ] Add transport configuration for different environments
- [ ] Implement pretty printing for development
- [ ] Add log rotation for production
- [ ] Make service name configurable

### Phase 2: Enhanced Request Context

- [ ] Add user ID to request context when available
- [ ] Add organization ID to request context
- [ ] Implement correlation IDs for distributed tracing
- [ ] Add request/response body logging (with redaction)

### Phase 3: Structured Logging Helpers

- [ ] Create logging utilities for common patterns
- [ ] Add database operation logging helpers
- [ ] Implement business event logging
- [ ] Add performance timing helpers

### Phase 4: Error Integration

- [ ] Integrate with Sentry for error correlation
- [ ] Add automatic error context enrichment
- [ ] Implement error rate monitoring
- [ ] Add alert thresholds

### Phase 5: Production Features

- [ ] Set up log aggregation (if needed)
- [ ] Implement log sampling strategies
- [ ] Add health check logging
- [ ] Configure log retention policies

### Phase 6: Monitoring & Observability

- [ ] Add business metrics logging
- [ ] Implement user journey tracking
- [ ] Add performance bottleneck detection
- [ ] Create logging dashboards

## Specific Technical Recommendations

### 1. Logger Configuration Improvements

```typescript
// src/lib/server/logger.ts
import { env } from '$env/dynamic/private';
import { dev } from '$app/environment';
import pino from 'pino';

const logLevel = env.LOG_LEVEL ?? (dev ? 'debug' : 'info');
const serviceName = env.SERVICE_NAME ?? 'project-controls';

export const logger = pino({
	level: logLevel,
	redact: {
		paths: [
			'req.headers.authorization',
			'req.headers.cookie',
			'user.email',
			'user.token',
			'password',
			'secret',
		],
		remove: true,
	},
	base: {
		service: serviceName,
		env: env.VERCEL_ENV ?? 'development',
		version: env.npm_package_version,
	},
	transport: dev
		? {
				target: 'pino-pretty',
				options: {
					colorize: true,
					translateTime: 'SYS:standard',
					ignore: 'pid,hostname',
				},
			}
		: undefined,
	formatters: {
		level: (label) => ({ level: label }),
	},
});
```

### 2. Enhanced Request Context

```typescript
// src/lib/server/request-context.ts
import { AsyncLocalStorage } from 'node:async_hooks';

type RequestContext = {
	requestId: string;
	start: number;
	userId?: string;
	orgId?: string;
	correlationId?: string;
};

export const als = new AsyncLocalStorage<RequestContext>();

export function getRequestContext(): RequestContext | undefined {
	return als.getStore();
}

export function getRequestId(): string | undefined {
	return als.getStore()?.requestId;
}
```

### 3. Logging Utilities

```typescript
// src/lib/server/logging-utils.ts
import type { Logger } from 'pino';

export function logDatabaseOperation(
	logger: Logger,
	operation: string,
	table: string,
	duration?: number,
	error?: unknown,
) {
	const logData = {
		operation,
		table,
		duration_ms: duration,
	};

	if (error) {
		logger.error({ ...logData, error }, `Database ${operation} failed`);
	} else {
		logger.info(logData, `Database ${operation} completed`);
	}
}

export function logBusinessEvent(logger: Logger, event: string, data?: Record<string, unknown>) {
	logger.info({ event, ...data }, `Business event: ${event}`);
}
```

### 4. Environment Variables

```bash
# .env
LOG_LEVEL=debug
SERVICE_NAME=project-controls
```

```bash
# .env.production
LOG_LEVEL=info
SERVICE_NAME=project-controls
```

### 5. Enhanced Hook Implementation

```typescript
// Enhanced requestContext hook
const requestContext: Handle = async ({ event, resolve }) => {
	const platformId = event.request.headers.get('x-vercel-id') ?? undefined;
	const requestId = platformId ?? randomUUID();
	const correlationId = event.request.headers.get('x-correlation-id') ?? randomUUID();
	const start = performance.now();

	return als.run({ requestId, start, correlationId }, async () => {
		// Get user context if available
		const { user } = await event.locals.getSession();
		const userId = user?.id;
		const orgId = event.cookies.get(ORG_COOKIE_NAME);

		// Update context with user info
		const context = als.getStore();
		if (context) {
			context.userId = userId;
			context.orgId = orgId;
		}

		// Create request-scoped logger
		event.locals.log = logger.child({
			requestId,
			correlationId,
			path: event.url.pathname,
			method: event.request.method,
			userId,
			orgId,
		});

		event.locals.log.info({ msg: 'request.start' });

		try {
			const res = await resolve(event);
			event.locals.log.info({
				msg: 'request.end',
				status: res.status,
				duration_ms: Math.round(performance.now() - start),
			});
			return res;
		} catch (error) {
			event.locals.log.error({
				msg: 'request.error',
				error,
				duration_ms: Math.round(performance.now() - start),
			});
			throw error;
		}
	});
};
```

## Benefits of These Improvements

1. **Better Development Experience**: Pretty printing and debug logging
2. **Production Ready**: Proper log levels and structured output
3. **Observability**: Request correlation and user context tracking
4. **Performance**: Duration tracking and bottleneck identification
5. **Security**: Enhanced redaction and sensitive data protection
6. **Maintainability**: Structured logging utilities and consistent patterns
7. **Integration**: Better Sentry correlation and error tracking

## Implementation Priority

**High Priority** (Phase 1-2):

- Fix environment variable usage
- Add transport configuration
- Enhance request context

**Medium Priority** (Phase 3-4):

- Add logging utilities
- Integrate with Sentry

**Low Priority** (Phase 5-6):

- Production optimizations
- Advanced monitoring features

## Testing Strategy

- [ ] Test logging in development with pretty printing
- [ ] Verify log levels work correctly in different environments
- [ ] Test request correlation across multiple requests
- [ ] Validate redaction of sensitive data
- [ ] Test error logging integration with Sentry
- [ ] Performance test logging overhead

## Deployment Considerations

- Configure log aggregation service (if using external service)
- Set up log retention policies
- Configure alerting on error rates
- Monitor logging performance impact
- Set up log analysis dashboards
