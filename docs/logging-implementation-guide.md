# Enhanced Pino Logging Implementation Guide

This document describes the enhanced Pino logging system implemented for the SvelteKit project-controls application.

## Overview

The logging system has been significantly improved to provide:

- **Structured logging** with consistent patterns
- **Request correlation** with unique IDs
- **User context tracking** across requests
- **Performance monitoring** capabilities
- **Security event logging**
- **Development-friendly** pretty printing
- **Production-ready** configuration

## Key Features

### ✅ Implemented Features

1. **Enhanced Logger Configuration**
   - Uses private environment variables for server-side config
   - Configurable service name and log levels
   - Pretty printing in development with pino-pretty
   - Structured JSON output in production

2. **Request Context Tracking**
   - Unique request IDs for each request
   - User ID and organization ID context
   - Request lifecycle logging (start/end/error)

3. **Logging Utilities**
   - Database operation logging
   - Business event logging
   - Performance timing helpers
   - User action tracking
   - API call logging
   - Authentication event logging
   - Security event logging

4. **Error Handling**
   - Enhanced error context with request information
   - Automatic error correlation with request IDs
   - Structured error logging

## Configuration

### Environment Variables

```bash
# Development (.env)
LOG_LEVEL="debug"
SERVICE_NAME="project-controls"

# Production (.env.production)
LOG_LEVEL="info"
SERVICE_NAME="project-controls"
```

### Logger Configuration

The logger is configured in `src/lib/server/logger.ts`:

```typescript
export const logger = pino({
	level: logLevel,
	redact: {
		paths: [
			'req.headers.authorization',
			'req.headers.cookie',
			'user.email',
			'user.token',
			'password',
			'secret',
		],
		remove: true,
	},
	base: {
		service: serviceName,
		env: env.VERCEL_ENV ?? 'development',
		version: env.npm_package_version,
	},
	transport: dev
		? {
				target: 'pino-pretty',
				options: {
					colorize: true,
					translateTime: 'SYS:standard',
					ignore: 'pid,hostname',
				},
			}
		: undefined,
});
```

## Usage Examples

### Basic Request Logging

Request logging is automatically handled by the hooks system. Each request gets:

```typescript
// Automatic request start logging
event.locals.log.info({ msg: 'request.start' });

// Automatic request end logging
event.locals.log.info({
	msg: 'request.end',
	status: res.status,
	duration_ms: Math.round(performance.now() - start),
});
```

### Database Operation Logging

```typescript
import { logDatabaseOperation } from '$lib/server/logging-utils';

export async function getProjects(event: RequestEvent) {
	const start = performance.now();

	try {
		const { data, error } = await event.locals.supabase.from('project').select('*');

		const duration = performance.now() - start;

		if (error) {
			logDatabaseOperation(event.locals.log, 'SELECT', 'project', duration, error);
			throw error;
		}

		logDatabaseOperation(event.locals.log, 'SELECT', 'project', duration);
		return data;
	} catch (error) {
		const duration = performance.now() - start;
		logDatabaseOperation(event.locals.log, 'SELECT', 'project', duration, error);
		throw error;
	}
}
```

### Business Event Logging

```typescript
import { logBusinessEvent } from '$lib/server/logging-utils';

// Log project creation
logBusinessEvent(event.locals.log, 'project_created', {
	projectId: 'proj_123',
	organizationId: event.locals.orgId,
	createdBy: event.locals.user?.id,
	projectType: 'construction',
});
```

### Performance Monitoring

```typescript
import { logPerformanceTiming } from '$lib/server/logging-utils';

export async function calculateBudget(event: RequestEvent) {
	const start = performance.now();

	// Expensive operation
	const result = await performComplexCalculation();

	const duration = performance.now() - start;

	logPerformanceTiming(event.locals.log, 'budget_calculation', duration, {
		projectId: 'proj_123',
		lineCount: 150,
		complexity: 'high',
	});

	return result;
}
```

### User Action Logging

```typescript
import { logUserAction } from '$lib/server/logging-utils';

logUserAction(event.locals.log, 'project_viewed', event.locals.user?.id, {
	projectId: 'proj_123',
	viewType: 'dashboard',
	source: 'navigation',
});
```

### Authentication Event Logging

```typescript
import { logAuthEvent } from '$lib/server/logging-utils';

// Log successful login
logAuthEvent(event.locals.log, 'login', userId, {
	method: 'email',
	ipAddress: event.getClientAddress(),
	userAgent: event.request.headers.get('user-agent'),
});
```

### Security Event Logging

```typescript
import { logSecurityEvent } from '$lib/server/logging-utils';

// Log suspicious activity
logSecurityEvent(event.locals.log, 'multiple_failed_logins', 'medium', {
	email: '<EMAIL>',
	attemptCount: 5,
	ipAddress: event.getClientAddress(),
	timeWindow: '5 minutes',
});
```

## Request Context

### Accessing Request Context

```typescript
import { getRequestContext, getRequestId, getUserId } from '$lib/server/request-context';

// Get full context
const context = getRequestContext();

// Get specific values
const requestId = getRequestId();
const userId = getUserId();
```

### Request Context Structure

```typescript
type RequestContext = {
	requestId: string;
	start: number;
	userId?: string;
	orgId?: string;
};
```

## Log Output Examples

### Development (Pretty Printed)

```
[2025-08-26 15:39:30.703 +0200] INFO: request.start
    service: "project-controls"
    env: "development"
    version: "0.0.1"
    requestId: "b4f79767-99c8-4898-a537-364cda6a527c"
    path: "/"
    method: "GET"
```

### Production (JSON)

```json
{
	"level": 30,
	"time": 1640995200000,
	"service": "project-controls",
	"env": "production",
	"version": "1.0.0",
	"requestId": "req_123",
	"path": "/api/projects",
	"method": "GET",
	"msg": "request.start"
}
```

## Security Features

### Data Redaction

Sensitive data is automatically redacted from logs:

- Authorization headers
- Cookies
- User emails
- Tokens
- Passwords
- Secrets

### Security Event Monitoring

The system includes built-in security event logging for:

- Failed login attempts
- Unauthorized access attempts
- Suspicious activity patterns
- Permission violations

## Performance Considerations

### Log Levels

- **Development**: `debug` level for detailed debugging
- **Production**: `info` level for operational logging
- **Trace**: Available for extremely detailed debugging

### Structured Logging

All logs use structured JSON format for:

- Easy parsing and analysis
- Consistent field naming
- Efficient storage and querying

## Integration with Existing Systems

### Sentry Integration

The logging system works alongside the existing Sentry error tracking:

- Request IDs are included in error context
- Structured logging provides additional context
- Enhanced error correlation with request information

### Request Hooks

The logging system is integrated into the SvelteKit hooks pipeline:

```typescript
export const handle: Handle = sequence(
	Sentry.sentryHandle(),
	requestContext, // Sets up logging context
	supabase, // Sets up Supabase client
	orgContext, // Adds organization context
	enhanceLogging, // Enhances logger with user context
	authGuard, // Handles authentication
);
```

## Best Practices

### 1. Use Appropriate Log Levels

```typescript
// Use debug for detailed debugging information
event.locals.log.debug({ data }, 'Processing data');

// Use info for general operational information
event.locals.log.info({ userId }, 'User logged in');

// Use warn for potentially problematic situations
event.locals.log.warn({ attempts: 3 }, 'Multiple failed login attempts');

// Use error for error conditions
event.locals.log.error({ error }, 'Database operation failed');
```

### 2. Include Relevant Context

```typescript
// Good: Include relevant context
event.locals.log.info(
	{
		userId: event.locals.user?.id,
		projectId: 'proj_123',
		action: 'budget_export',
		format: 'excel',
	},
	'User exported budget',
);

// Avoid: Generic messages without context
event.locals.log.info('Budget exported');
```

### 3. Use Logging Utilities

```typescript
// Good: Use provided utilities
logDatabaseOperation(event.locals.log, 'SELECT', 'project', duration);

// Avoid: Manual logging without consistent structure
event.locals.log.info('Database query completed');
```

### 4. Handle Errors Consistently

```typescript
try {
	// Operation
} catch (error) {
	logDatabaseOperation(event.locals.log, 'SELECT', 'project', duration, error);
	throw error; // Re-throw after logging
}
```

## Monitoring and Alerting

### Key Metrics to Monitor

1. **Error Rates**: Monitor error log frequency
2. **Performance**: Track operation durations
3. **Security Events**: Alert on suspicious activities
4. **User Actions**: Track business-critical operations

### Log Analysis

The structured JSON format enables:

- Easy filtering by request ID
- Performance analysis by operation type
- User behavior tracking
- Security incident investigation

## Future Enhancements

### Planned Improvements

1. **Log Aggregation**: Integration with external log management
2. **Sampling**: Implement log sampling for high-traffic scenarios
3. **Metrics**: Extract metrics from log data
4. **Dashboards**: Create operational dashboards
5. **Alerting**: Set up automated alerts for critical events

### Configuration Options

Future configuration options may include:

- Log rotation policies
- Sampling rates
- Custom redaction rules
- External transport configurations

## Troubleshooting

### Common Issues

1. **Missing pino-pretty**: Install with `pnpm add -D pino-pretty`
2. **Environment Variables**: Ensure LOG_LEVEL and SERVICE_NAME are set
3. **Hook Order**: Ensure logging hooks run in correct sequence
4. **Context Access**: Use logging utilities within request context

### Debug Mode

Enable trace-level logging for detailed debugging:

```bash
LOG_LEVEL="trace"
```

This will show extremely detailed information including SQL queries and internal operations.
