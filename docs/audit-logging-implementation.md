# Project Audit Logging Implementation

## Overview

This document outlines the implementation of a comprehensive audit logging system for all project-related data tables. The system follows the existing pattern established by `budget_line_item_audit` and provides complete change tracking with full attribution.

## Scope

The audit logging system will be implemented for the following tables:

- `project_stage` → `project_stage_audit`
- `gateway_checklist_item` → `gateway_checklist_item_audit`
- `project_gateway_stage_info` → `project_gateway_stage_info_audit`
- `risk_register` → `risk_register_audit`
- `approved_changes` → `approved_changes_audit`

## Architecture

### Audit Table Structure

Each audit table follows this standardized structure:

- All columns from the source table (with nullable constraints where appropriate)
- `audit_id` (UUID, primary key)
- `operation_type` (TEXT: 'INSERT', 'UPDATE', 'DELETE')
- `changed_by` (UUID, references profile.user_id)
- `changed_at` (TIMESTAMPTZ, default NOW())
- `old_values` (JSONB, previous values for UPDATE operations)
- `new_values` (JSONB, new values for INSERT/UPDATE operations)

### Trigger Functions

PostgreSQL trigger functions will:

- Set `search_path = ''` for security
- Use fully qualified schema names
- Capture authenticated user ID via `auth.uid()`
- Store complete before/after state for all changes
- Handle INSERT, UPDATE, and DELETE operations

## Implementation Tasks

### Phase 1: Database Schema

1. **Create Migration File**
   - Follow naming convention: `YYYYMMDDHHMMSS_add_project_audit_logging.sql`
   - Include all audit tables, triggers, and policies in single migration

2. **Audit Table Creation**
   - Create each audit table with proper column definitions
   - Include appropriate indexes for performance
   - Add table and column comments for documentation

3. **Trigger Function Implementation**
   - Create reusable trigger function for audit logging
   - Handle all operation types (INSERT, UPDATE, DELETE)
   - Ensure proper error handling and security

4. **Trigger Creation**
   - Create triggers on each source table
   - Use AFTER triggers to capture final state
   - Include proper trigger naming convention

### Phase 2: Security and Access Control

5. **Row Level Security (RLS)**
   - Enable RLS on all audit tables
   - Create policies matching source table access patterns
   - Ensure hierarchical access control works correctly

6. **Permissions and Grants**
   - Grant appropriate permissions to service_role
   - Follow existing permission patterns

### Phase 3: Performance and Indexing

7. **Index Creation**
   - Create indexes on frequently queried columns
   - Include composite indexes for common query patterns
   - Focus on project_id, changed_by, and timestamp columns

### Phase 4: Testing and Validation

8. **Functional Testing**
   - Test INSERT operations create audit records
   - Test UPDATE operations capture before/after state
   - Test DELETE operations preserve final state
   - Verify user attribution works correctly

9. **Performance Testing**
   - Ensure audit logging doesn't significantly impact performance
   - Validate index effectiveness

10. **Integration Testing**
    - Test with existing application code
    - Verify RLS policies work correctly
    - Test edge cases and error conditions

## Technical Specifications

### Audit Table Naming Convention

- Source table: `table_name`
- Audit table: `table_name_audit`

### Trigger Naming Convention

- Function: `audit_table_name_changes()`
- Trigger: `audit_table_name_trigger`

### Column Specifications

#### Standard Audit Columns

```sql
audit_id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
operation_type TEXT NOT NULL CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE')),
changed_by UUID NOT NULL REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
changed_at TIMESTAMPTZ NOT NULL DEFAULT NOW (),
old_values JSONB,
new_values JSONB
```

#### Source Table Columns

All columns from source table with:

- Primary key constraints removed
- NOT NULL constraints relaxed where appropriate
- Foreign key constraints maintained for referential integrity

### Security Considerations

1. **Search Path Security**
   - All functions use `search_path = ''`
   - All table references use fully qualified names

2. **User Attribution**
   - Use `auth.uid()` to capture authenticated user
   - Handle cases where `auth.uid()` might be NULL

3. **Data Integrity**
   - Maintain referential integrity where possible
   - Use appropriate constraints and checks

## Migration Strategy

### Single Migration Approach

- All audit tables, functions, and triggers in one migration
- Reduces complexity and ensures consistency
- Easier to rollback if needed

### Rollback Plan

- Drop triggers first
- Drop audit tables
- Drop trigger functions
- Clean rollback without data loss

## Monitoring and Maintenance

### Performance Monitoring

- Monitor audit table growth
- Track query performance on audit tables
- Consider archival strategy for old audit records

### Data Retention

- Consider implementing data retention policies
- Archive old audit records to maintain performance
- Ensure compliance with data retention requirements

## Future Enhancements

### Potential Improvements

- Audit log compression for large datasets
- Real-time audit event streaming
- Audit log analysis and reporting tools
- Integration with external audit systems

### Scalability Considerations

- Partition audit tables by date if needed
- Consider separate audit database for high-volume systems
- Implement audit log archival automation
