# Budget Versioning Upgrade Plan & Implementation

> **Implementation-Ready Document**: This document combines strategic planning with detailed implementation specifications. Use this as the single source of truth for the budget versioning upgrade.

## Executive Summary

This upgrade introduces immutable budget versions with an active pointer per project, enabling "replace-on-import" with full auditability and fast undo. Because there is no production data to preserve, we will use an aggressive rollout without dual-write, feature flags, or compatibility views.

### Key Benefits

- **Immutable versions**: Every budget state is preserved as a version
- **Replace-on-import**: Create new version while keeping pre-import state for undo
- **Minimal disruption**: Direct integration without views or dual-write
- **Custom WBS support**: Full integration with existing custom WBS functionality
- **Performance**: Optimized for 5k-20k budget items per project

### Decisions Made

- ✅ **Aggressive rollout**: No dual-write and no feature flags
- ✅ **No compatibility views**: Do not replace table with a view after soak
- ✅ **Snapshot linking now**: Add `budget_snapshot.budget_version_id` now and backfill; enforcement level TBD after backfill
- ✅ **Version lineage**: Include `prev_version_id` for audit trail and undo
- ✅ **Security**: All RPCs are `SECURITY INVOKER` and must error when `auth.uid()` is NULL unless explicitly justified
- ✅ **Active pointer semantics**: `project.active_budget_version_id` points to the in-progress version for pre-construction; becomes immutable in construction (changes via `risk_register` and `approved_change` only)
- ✅ **RLS first**: Prefer preserving RLS over performance; materialized views considered only if security is preserved
- ✅ **Import idempotency**: `budget_import.source_hash` uniqueness is per-project
- ✅ **Version items schema**: `budget_version_item` mirrors all columns from `budget_line_item_current`; no new denormalized totals
- ✅ **Backfill**: Create backfilled versions with `kind='stage'` and set active pointers for all projects (low priority but keeps demo data usable)

## Implementation Checklist

### Phase 1: Schema & Database Setup

Focus on the schemas and do not create or edit migration files. Generate a migration after schema changes should be rolled out by running `supabase db diff -f <description>`. New schema files must be included in `supabase/config.toml` in the exact processing order to be included in a migration.

- [x] **Create budget versioning tables**
  - [x] Create `budget_version_kind` enum: `('stage', 'import', 'manual', 'system')`
  - [x] Create `budget_version` table with constraints and indexes
  - [x] Create `budget_version_item` table mirroring `budget_line_item_current`
  - [x] Create `budget_import` table with per-project idempotency
- [x] **Update existing tables**
  - [x] Add `active_budget_version_id` to `project` table (nullable FK)
  - [x] Initialize `active_budget_version_id` for all projects (via backfill)
  - [x] Add `budget_version_id` to `budget_snapshot` table now and plan backfill
- [x] **Create performance indexes** (see indexing guidance)
- [x] **Add comprehensive RLS policies** mirroring current rules for active stage versions
- [x] **Run backfill migration** to create bootstrap versions (`kind='stage'`) from existing data (low priority)
  - Implemented as manual migration: `supabase/migrations/20250829145500_budget_version_backfill.sql`
- [x] **Update supabase/config.toml** with new table ordering

### Phase 2: RPC Functions (No Dual-Write)

- [x] **Core version management RPCs**
  - [x] Implement `create_budget_version()` (clone active by default; empty for imports)
  - [x] Implement `activate_budget_version()` with per-project concurrency lock
  - [x] Implement `list_budget_versions()` (newest first; include cached aggregates)
  - [x] Implement `diff_budget_versions()` (JSON shape TBD)
- [x] **Enhanced import system**
  - [x] Implement `apply_budget_import()` starting from a new empty version; if `(project_id, source_hash)` hit exists, reuse that version
  - [x] Keep existing project-scoped custom WBS creation; on undo, remove created custom WBS items (undo removal to be completed when undo RPC is added)
- [x] **Set permissions and security** for all new RPCs (error if `auth.uid()` is NULL)

Note: RLS policies now explicitly allow inserts/updates to `budget_version` and `budget_version_item` for import/manual kinds when the user can modify the project. Accordingly, `create_budget_version()` and `apply_budget_import()` are implemented as `SECURITY INVOKER` and enforce permission checks (`auth.uid()`, `can_modify_project`, `can_access_project`).

### Phase 3: Read Path Migration

- [ ] If/when we migrate reads, we will update application queries to read from `budget_version`/`budget_version_item` directly with RLS-preserving policies.

### Phase 4: Frontend Integration & Import UI

- [ ] **Backend service updates** for new import system
- [ ] Implement small "undo import" RPC to revert `project.active_budget_version_id` to the import's `pre_version_id`; optionally remove custom WBS items created by that import
- Defer undo UX for now (still support undo via RPCs if needed)
- [ ] **Enhanced import UX** with preview and diff display
- [ ] **Telemetry and monitoring** for import operations

### Phase 5: Stage Integration

- [ ] **Stage completion integration** to tag versions
- [ ] **Gateway workflow validation** ensuring no UI changes
- [ ] **Version lineage for stages** through gate progression

### Phase 6: Version Management UI

- [ ] **Version list interface** with history and quick actions
- [ ] **Version comparison interface** with detailed diffs
- [ ] **Version management controls** for activation and labeling
- [ ] **Import workflow improvements** with preview capabilities

### Phase 7: Testing & Validation

- [ ] **Unit tests** for all RPC functions and edge cases
- [ ] **Integration tests** for cross-system functionality
- [ ] **End-to-end tests** for complete user workflows
- [ ] **Performance tests** with large datasets

### Phase 8: Production Rollout

- [ ] **Pre-deployment preparation** with concise runbooks and monitoring
- [ ] **Immediate cutover** (no dual-write, no feature flags)
- [ ] **Monitoring and optimization** after rollout
- [ ] **Cleanup and finalization**

### Operational Considerations

- [ ] **Concurrency and safety** with advisory locks
- [ ] **Observability** with structured logging and metrics
- [ ] **Data management** and retention policies
- [ ] **Performance monitoring** and optimization

## Technical Implementation Details

### Key Database Tables

#### budget_version

```sql
CREATE TYPE budget_version_kind AS ENUM ('stage', 'import', 'manual', 'system');

CREATE TABLE budget_version (
	budget_version_id uuid PRIMARY KEY DEFAULT gen_random_uuid (),
	project_id uuid NOT NULL REFERENCES project (project_id),
	label text,
	kind budget_version_kind NOT NULL,
	stage_id uuid REFERENCES project_stage (project_stage_id),
	prev_version_id uuid REFERENCES budget_version (budget_version_id),
	created_by_user_id uuid NOT NULL REFERENCES profile (user_id),
	created_at timestamptz DEFAULT now (),
	updated_at timestamptz DEFAULT now (),
	CONSTRAINT stage_consistency CHECK (
		(
			kind = 'stage'
			AND stage_id IS NOT NULL
		)
		OR (kind != 'stage')
	),
	CONSTRAINT no_self_reference CHECK (budget_version_id != prev_version_id)
);
```

#### budget_version_item

```sql
CREATE TABLE budget_version_item (
	budget_version_item_id uuid PRIMARY KEY DEFAULT gen_random_uuid (),
	budget_version_id uuid NOT NULL REFERENCES budget_version (budget_version_id) ON DELETE CASCADE,
	wbs_library_item_id uuid NOT NULL REFERENCES wbs_library_item (wbs_library_item_id),
	-- Mirror all columns from budget_line_item_current 1:1 (no extra denormalized totals)
	-- e.g., quantity, unit, material_rate, labor_rate, productivity_per_hour,
	-- unit_rate_manual_override, unit_rate, factor, remarks, cost_certainty, design_certainty, etc.
	created_at timestamptz DEFAULT now (),
	UNIQUE (budget_version_id, wbs_library_item_id)
);
```

#### budget_import

```sql
CREATE TABLE budget_import (
	budget_import_id uuid PRIMARY KEY DEFAULT gen_random_uuid (),
	project_id uuid NOT NULL REFERENCES project (project_id),
	source_filename text NOT NULL,
	source_hash text NOT NULL,
	pre_version_id uuid NOT NULL REFERENCES budget_version (budget_version_id),
	new_version_id uuid NOT NULL REFERENCES budget_version (budget_version_id),
	is_undone boolean DEFAULT false,
	undone_at timestamptz,
	undone_by_user_id uuid REFERENCES profile (user_id),
	created_by_user_id uuid NOT NULL REFERENCES profile (user_id),
	created_at timestamptz DEFAULT now (),
	UNIQUE (project_id, source_hash) -- idempotency scoped per project
);
```

### Key RPC Functions

```sql
-- Create new budget version
CREATE FUNCTION create_budget_version (
	p_project_id uuid,
	p_label text DEFAULT NULL,
	p_kind budget_version_kind DEFAULT 'manual',
	p_stage_id uuid DEFAULT NULL,
	p_prev_version_id uuid DEFAULT NULL
) RETURNS uuid LANGUAGE plpgsql SECURITY INVOKER;

-- Apply budget import with full error handling
CREATE FUNCTION apply_budget_import (
	p_project_id uuid,
	p_source_filename text,
	p_source_hash text DEFAULT NULL,
	p_items jsonb,
	p_notes text DEFAULT NULL
) RETURNS jsonb LANGUAGE plpgsql SECURITY INVOKER;

-- Activate a budget version
CREATE FUNCTION activate_budget_version (p_version_id uuid, p_reason text DEFAULT NULL) RETURNS boolean LANGUAGE plpgsql SECURITY INVOKER;

-- List versions with pagination
CREATE FUNCTION list_budget_versions (
	p_project_id uuid,
	p_limit integer DEFAULT 50,
	p_cursor timestamptz DEFAULT NULL
) RETURNS TABLE (
	budget_version_id uuid,
	label text,
	kind budget_version_kind,
	is_active boolean,
	item_count bigint,
	total_cost numeric,
	created_at timestamptz
	-- ... additional fields
) LANGUAGE plpgsql SECURITY INVOKER;

-- Compare two versions
CREATE FUNCTION diff_budget_versions (p_version_a uuid, p_version_b uuid) RETURNS jsonb LANGUAGE plpgsql SECURITY INVOKER;

-- Detailed diff with added/removed/changed items
```

### RLS Policies

- Mirror existing `budget_line_item_current` rules: viewers can read; editors can modify.
- Apply these rules only to the current active budget where `budget_version.kind = 'stage'` and `budget_version.budget_version_id = project.active_budget_version_id`.
- For non-active versions or versions with other kinds, allow read-only access.
- All RPCs must use `SECURITY INVOKER` and should `RAISE EXCEPTION` when `auth.uid()` is NULL; fall back to system user only where explicitly required and consistent with existing patterns.

## Migration Strategy

### Phase 1: Schema Migration

1. Deploy new tables with indexes and constraints
2. Add `project.active_budget_version_id` and initialize for all projects
3. Add `budget_snapshot.budget_version_id` and plan backfill
4. Run backfill (low priority) to create bootstrap versions with `kind='stage'` for demo data

### Phase 2: Write Path Cutover

1. Update write paths to operate only on `budget_version`/`budget_version_item`
2. Keep reads as-is for now (no view replacement). If/when needed, update application reads directly to new tables.
3. Monitor performance and RLS behavior

### Phase 3: Read Migration (Optional / TBD)

1. If required, update application code to read from `budget_version`/`budget_version_item` for the active version only
2. Validate performance and RLS

### Phase 4: Cleanup

1. Remove any dead code and legacy references
2. Finalize indexes and constraints post-backfill

## Success Metrics

- **Migration Success**: 100% of projects migrated with data integrity
- **Performance**: Import <30s for 10k items, <60s for 20k items
- **Reliability**: <0.1% error rate for imports and version operations
- **User Adoption**: >80% using new system within 30 days

## Risk Mitigation

- **Data Loss**: Complete validation and rollback procedures
- **Performance**: Load testing and query optimization
- **User Disruption**: Aggressive cutover with limited demo data; clear rollback plan
- **Concurrency**: Advisory locks and testing
- **Rollback**: Multiple rollback strategies at each phase

---

_This document serves as the complete implementation guide. All developers should reference this single document for the budget versioning upgrade project._

---

## Additional Specifications

### Active Budget Pointer Semantics

- `project.active_budget_version_id` must always reference the in-progress version for pre-construction stages.
- When a project enters the construction stage, the active budget becomes immutable; all modifications must occur via `risk_register` and `approved_change` flows.

### Budget Snapshot Linking

- Add `budget_snapshot.budget_version_id` now; backfill existing snapshots.
- Backfill method TBD; recommendation: correlate snapshots by timestamp to the nearest earlier active version per project, or maintain explicit linkage during snapshot creation going forward.

### RPC Semantics

- `create_budget_version()`
  - Clone from the active version by default.
  - When `kind='import'`, create an empty version.
  - Attribution: `created_by_user_id = auth.uid()` with fallback to system user per existing patterns.
- `apply_budget_import()`
  - Start from a new empty budget version.
  - If `(project_id, source_hash)` already exists, reuse that version instead of creating a new one.
  - Maintain existing per-project custom WBS creation; on undo, remove created custom WBS items.
  - On undo, attribute `undone_by_user_id = auth.uid()` with system fallback as needed.
- `activate_budget_version()`
  - Use a per-project concurrency lock (e.g., `pg_advisory_xact_lock` keyed by project) to serialize activations.
- `list_budget_versions()`
  - Sort newest first (`created_at DESC`).
  - Include total items and sum from a cached aggregation (see Aggregations & Indexes).
- `diff_budget_versions()`
  - JSON shape TBD; include added/removed/changed items and summary stats.

### RLS Details

- Editors can modify only where `budget_version.kind='stage'` and the version is active for the project.
- Viewers can read all versions; edits blocked on non-active versions and non-`stage` kinds.
- All RPCs are `SECURITY INVOKER` and should `RAISE EXCEPTION` when `auth.uid()` is NULL.

### Aggregations & Indexes

- Prefer preserving RLS even if performance costs increase.
- Consider a materialized view only if RLS can be preserved; otherwise, maintain a per-version summary table updated by triggers/worker under `SECURITY INVOKER`.
- Recommended indexes:
  - `budget_version (project_id, created_at DESC)`
  - `budget_version (kind, project_id)`
  - `budget_version (prev_version_id)`
  - `budget_version (stage_id)`
  - `budget_version_item (budget_version_id)`
  - `budget_version_item (wbs_library_item_id)`
  - Unique: `(budget_version_id, wbs_library_item_id)`
  - `budget_import (project_id, source_hash)` unique
  - `project (active_budget_version_id)`
  - `budget_snapshot (budget_version_id)`

### Concurrency Locking

- Use a per-project advisory lock in `activate_budget_version()` to prevent races, e.g.:

```sql
PERFORM pg_advisory_xact_lock(
  hashtextextended('activate_budget_version', 0),
  hashtextextended(p_project_id::text, 0)
);
```

### Version Labeling

- Keep labeling simple and human-friendly for now; can evolve later.

### Version History UX

- Show all human-readable fields; avoid exposing UUIDs.
- Provide links to diff against the active version.
- Undo UX not required at this time.

### Audit Logging

- Add audit tables for new budget version tables to record who made changes (no need to duplicate full budget line items in audit records).
- Suggested approach:
  - `budget_version_audit(budget_version_id, action, changed_by_user_id, changed_at, notes jsonb)`
  - `budget_import_audit(budget_import_id, action, changed_by_user_id, changed_at, notes jsonb)`
  - For `budget_version_item` changes, capture minimal events (item id, action, who/when), not full row copies.

### Import Payload

- No changes required to the `import_budget_data` JSON shape.

### Backfill Defaults

- Backfilled versions should use `kind='stage'`.
- Set the active pointer for all projects; there are no archived projects (production contains only demo projects).
