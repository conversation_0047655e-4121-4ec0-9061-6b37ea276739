import { sentrySvelteKit } from '@sentry/sveltekit';
import { defineConfig } from 'vite';
import { sveltekit } from '@sveltejs/kit/vite';
import { enhancedImages } from '@sveltejs/enhanced-img';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
	plugins: [
		sentrySvelteKit({
			sourceMapsUploadOptions: {
				org: 'aurora-construction-consultanc',
				project: 'cost-atlas',
			},
		}),
		tailwindcss(),
		enhancedImages(), // must come before the SvelteKit plugin
		sveltekit(),
	],
	server: {
		allowedHosts: ['costatlas.lehrer.us'],
	},
});
