<script lang="ts">
	import '../app.css';
	import { Toaster } from '$lib/components/ui/sonner';
	import AppSidebar from '$lib/components/app-sidebar.svelte';
	import * as Breadcrumb from '$lib/components/ui/breadcrumb/index.js';
	import { Separator } from '$lib/components/ui/separator/index.js';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import { getFlash } from 'sveltekit-flash-message';
	import { page } from '$app/state';
	import { toast } from 'svelte-sonner';
	import { invalidate } from '$app/navigation';
	import type { LayoutProps } from './$types';
	import { setCurrentOrgId } from '$lib/current-org.svelte';
	import Logo from '$lib/assets/logo.svg.svelte';
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { beforeNavigate, afterNavigate } from '$app/navigation';
	import posthog from 'posthog-js';
	import { ORG_COOKIE_NAME } from '$lib/current-org.svelte';

	// PostHog pageview & pageleave tracking
	if (browser) {
		beforeNavigate(() => posthog.capture('$pageleave'));
		afterNavigate(() => posthog.capture('$pageview'));
	}

	const flash = getFlash(page);
	const orgContext = setCurrentOrgId(null);

	const { children, data }: LayoutProps = $props();
	const sidebarClients = $derived(data.sidebarClients);

	$effect(() => {
		if (!$flash) return;

		if ($flash.type === 'success') {
			toast.success($flash.message);

			// Handle new organization ID from the flash message
			if ($flash.data?.newOrgId) {
				// Set new organization as active in context
				orgContext.setOrgId($flash.data.newOrgId);
				orgContext.loadOrgs(page.data.supabase, userId);
			}
		} else if ($flash.type === 'error') {
			toast.error($flash.message);
		} else {
			toast.info($flash.message);
		}

		// Clear the flash message to avoid double-toasting.
		$flash = undefined;
	});

	$effect(() => {
		const { data: authData } = data.supabase.auth.onAuthStateChange((_event, newSession) => {
			if (newSession?.expires_at !== page.data.session?.expires_at) {
				invalidate('supabase:auth');
			}
			posthog.identify(newSession?.user?.id);
		});
		return () => authData.subscription.unsubscribe();
	});

	const userId = $derived(page.data.user?.id);

	onMount(() => {
		if (browser && userId) {
			// Load organizations when the component mounts
			orgContext.loadOrgs(page.data.supabase, userId);

			// Check cookie for organization ID if it's not already set
			const id = orgContext.getOrgId();
			if (!id) {
				// Read org ID from cookie
				const cookies = document.cookie.split('; ');
				const orgCookie = cookies.find((cookie) => cookie.startsWith(`${ORG_COOKIE_NAME}=`));
				if (orgCookie) {
					const orgId = orgCookie.split('=')[1];
					if (orgId) {
						orgContext.setOrgId(orgId);
					}
				}
			}
		}
	});
</script>

<svelte:head>
	<title>{page.data.title ? `${page.data.title} | Cost Atlas` : 'Cost Atlas'}</title>
</svelte:head>

<Toaster position="top-center" richColors />
{#if page.data.session}
	<Sidebar.Provider>
		<AppSidebar clients={sidebarClients} collapsible="offcanvas" />
		<Sidebar.Inset>
			<header
				class="flex h-16 shrink-0 items-center justify-between transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-16"
			>
				<div class="flex items-center gap-2 px-4">
					<Sidebar.Trigger class="-ml-1" />
					{#if page.params.org_name && page.params.client_name}
						<Separator orientation="vertical" class="mr-2 h-4" />
						<Breadcrumb.Root>
							<Breadcrumb.List>
								<Breadcrumb.Item class="hidden md:block">
									<Breadcrumb.Link href="/org/{encodeURIComponent(page.params.org_name)}/clients"
										>{page.params.org_name}</Breadcrumb.Link
									>
								</Breadcrumb.Item>
								<Breadcrumb.Separator class="hidden md:block" />
								<Breadcrumb.Item class="hidden md:block">
									<Breadcrumb.Link
										href="/org/{encodeURIComponent(
											page.params.org_name,
										)}/clients/{encodeURIComponent(page.params.client_name)}"
										>{page.params.client_name}</Breadcrumb.Link
									>
								</Breadcrumb.Item>
								{#if page.params.project_id_short}
									<Breadcrumb.Separator class="hidden md:block" />
									<Breadcrumb.Item>
										<Breadcrumb.Link
											href="/org/{encodeURIComponent(
												page.params.org_name,
											)}/clients/{encodeURIComponent(
												page.params.client_name,
											)}/projects/{encodeURIComponent(page.params.project_id_short)}"
											>{page.data.project?.name}</Breadcrumb.Link
										>
									</Breadcrumb.Item>
									{#if page.params.stage_order}
										<Breadcrumb.Separator />
										<Breadcrumb.Item>
											<Breadcrumb.Link
												href="/org/{encodeURIComponent(
													page.params.org_name,
												)}/clients/{encodeURIComponent(
													page.params.client_name,
												)}/projects/{encodeURIComponent(page.params.project_id_short)}/stage-{page
													.params.stage_order}">Stage {page.params.stage_order}</Breadcrumb.Link
											>
										</Breadcrumb.Item>
										{#if page.route.id?.endsWith('/checklist')}
											<Breadcrumb.Separator />
											<Breadcrumb.Item>
												<Breadcrumb.Link
													href="/org/{encodeURIComponent(
														page.params.org_name,
													)}/clients/{encodeURIComponent(
														page.params.client_name,
													)}/projects/{encodeURIComponent(page.params.project_id_short)}/stage-{page
														.params.stage_order}/gateway">Gateway</Breadcrumb.Link
												>
											</Breadcrumb.Item>
										{/if}
									{/if}
								{/if}
							</Breadcrumb.List>
						</Breadcrumb.Root>
					{:else if page.params.library_id}
						<Separator orientation="vertical" class="mr-2 h-4" />
						<Breadcrumb.Root>
							<Breadcrumb.List>
								<Breadcrumb.Item class="hidden md:block">
									<Breadcrumb.Link href="/wbs-libaries">WBS Libraries</Breadcrumb.Link>
								</Breadcrumb.Item>
								<Breadcrumb.Separator class="hidden md:block" />
								<Breadcrumb.Item class="">
									<Breadcrumb.Link href="/wbs-libaries/{page.data.library.id}"
										>{page.data.library?.name}</Breadcrumb.Link
									>
								</Breadcrumb.Item>
							</Breadcrumb.List>
						</Breadcrumb.Root>
					{:else if page.params.org_name}
						<Separator orientation="vertical" class="mr-2 h-4" />
						<Breadcrumb.Root>
							<Breadcrumb.List>
								<Breadcrumb.Item>
									<Breadcrumb.Link href="/org/{encodeURIComponent(page.params.org_name)}/clients"
										>{page.params.org_name}</Breadcrumb.Link
									>
								</Breadcrumb.Item>
								{#if page.route.id === '/org/[org_name]/invite'}
									<Breadcrumb.Separator />
									<Breadcrumb.Item>
										<Breadcrumb.Link href="/org/{encodeURIComponent(page.params.org_name)}/members"
											>Members</Breadcrumb.Link
										>
									</Breadcrumb.Item>
								{/if}
							</Breadcrumb.List>
						</Breadcrumb.Root>
					{:else if page.route.id?.startsWith('/vendor')}
						<Separator orientation="vertical" class="mr-2 h-4" />
						<Breadcrumb.Root>
							<Breadcrumb.List>
								<Breadcrumb.Item>
									<Breadcrumb.Link href="/vendor">Vendors</Breadcrumb.Link>
								</Breadcrumb.Item>
								{#if page.route.id === '/vendor/new'}
									<Breadcrumb.Separator />
									<Breadcrumb.Item>
										<Breadcrumb.Page>New Vendor</Breadcrumb.Page>
									</Breadcrumb.Item>
								{:else if page.route.id === '/vendor/[vendor_id]'}
									<Breadcrumb.Separator />
									<Breadcrumb.Item>
										<Breadcrumb.Page>{page.data.vendor?.name || 'Vendor'}</Breadcrumb.Page>
									</Breadcrumb.Item>
								{:else if page.route.id === '/vendor/[vendor_id]/edit'}
									<Breadcrumb.Separator />
									<Breadcrumb.Item>
										<Breadcrumb.Link href="/vendor/{page.params.vendor_id}">
											{page.data.vendor?.name || 'Vendor'}
										</Breadcrumb.Link>
									</Breadcrumb.Item>
									<Breadcrumb.Separator />
									<Breadcrumb.Item>
										<Breadcrumb.Page>Edit</Breadcrumb.Page>
									</Breadcrumb.Item>
								{/if}
							</Breadcrumb.List>
						</Breadcrumb.Root>
					{/if}
				</div>
			</header>

			<div class={[!page.params.project_id_short && 'sm:p-4 lg:p-12']}>
				{@render children()}
			</div>
		</Sidebar.Inset>
	</Sidebar.Provider>
{:else}
	<header class="flex h-screen flex-col">
		<div class="h-16 w-full">
			<a href="/">
				<div class="flex items-center gap-2 px-4 py-3">
					<div class="logo shrink-0">
						<Logo />
					</div>
					<span class="text-2xl font-semibold">Cost Atlas</span>
				</div>
			</a>
		</div>
		{@render children()}
	</header>
{/if}
