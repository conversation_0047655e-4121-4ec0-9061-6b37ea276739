<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { toast } from 'svelte-sonner';
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';

	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { TestTube, Buildings, SpinnerGap } from 'phosphor-svelte';
	import { goto } from '$app/navigation';

	const { data }: PageProps = $props();

	const ribaForm = superForm(data.ribaForm, {
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text, {
						action: {
							label: `Go to Project`,
							onClick: () =>
								goto(form.message?.data?.link ? (form.message.data.link as string) : '/'),
						},
					});
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: ribaFormData, enhance: ribaEnhance, submitting } = ribaForm;
</script>

<div class="relative container mx-auto max-w-4xl py-8">
	<div class="mb-8">
		<div class="mb-4 flex items-center gap-3">
			<TestTube class="h-8 w-8 text-orange-500" />
			<h1 class="mb-0 text-3xl font-bold">Admin Tools</h1>
			<div class="rounded-full bg-orange-100 px-3 py-1 text-sm font-medium text-orange-800">
				Test Data Generation
			</div>
		</div>
		<p class="text-muted-foreground">Administrative tools for demo and testing purposes.</p>
	</div>

	{#if $submitting}
		<div class="absolute inset-0 z-10 bg-gray-500/20"></div>
		<div class="absolute inset-1/2 size-16 -translate-x-1/2 -translate-y-1/2">
			<SpinnerGap class="text-primary size-12 animate-spin" />
		</div>
	{/if}
	<!-- RIBA Demo Project Generation Tool -->
	<div class="bg-card rounded-lg border p-6">
		<div class="mb-4 flex items-center gap-3">
			<Buildings class="h-6 w-6 text-green-500" />
			<h2 class="text-xl font-semibold">Generate RIBA Demo Project Data</h2>
		</div>

		<p class="text-muted-foreground mb-6">
			Creates a comprehensive RIBA-staged project under your current organization with detailed
			budget progression through all 6 RIBA stages (0-5). Includes vendors, purchase orders, work
			packages, invoices, risks, and approved changes with realistic data relationships.
		</p>

		<div class="bg-muted/50 mb-6 rounded border p-4">
			<h3 class="mb-2 font-medium">What this tool does:</h3>
			<ul class="text-muted-foreground space-y-1 text-sm">
				<li>• Creates a new client and project under Aurora organization</li>
				<li>• Generates all 6 RIBA stages (0-5) with progressive budget detail</li>
				<li>• Stage 1: High-level acquisition/construction split</li>
				<li>• Stage 2-5: Progressive budget breakdown to level 4 WBS detail</li>
				<li>• Creates 30 vendors with purchase orders and work packages</li>
				<li>• Generates invoices linked to purchase orders</li>
				<li>• Creates 10 risk register entries with potential approved changes</li>
				<li>• Uses ICMS v3 WBS library for realistic cost structure</li>
			</ul>
		</div>
		<form class="my-12" method="POST" action="?/generateRibaDemoProject" use:ribaEnhance>
			<div class="space-y-4">
				<div class="space-y-2">
					<Label for="total-budget">Total Budget (SEK)</Label>
					<Input
						id="total-budget"
						name="totalBudget"
						type="number"
						min="100000"
						max="10000000000"
						step="1000"
						bind:value={$ribaFormData.totalBudget}
						placeholder="1000000000"
					/>
				</div>
				<div class="flex items-center space-x-2">
					<Checkbox
						id="riba-confirm-checkbox"
						name="confirm"
						bind:checked={$ribaFormData.confirm}
					/>
					<label for="riba-confirm-checkbox" class="text-sm font-normal">
						I understand this will create RIBA test data in the database
					</label>
				</div>
				<Button type="submit" disabled={!$ribaFormData.confirm || $submitting}
					>Generate RIBA Demo Project Data</Button
				>
			</div>
		</form>
	</div>
</div>
