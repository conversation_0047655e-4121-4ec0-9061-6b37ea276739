import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import { dev } from '$app/environment';
import { requireUser } from '$lib/server/auth';
import generateRibaDemoProjectData from '$lib/demo/fake_project';
import { SUPERADMIN_ID } from '$env/static/private';

// Schema for the RIBA demo project data generation form
const ribaDemoProjectSchema = z.object({
	confirm: z.boolean().refine((val) => val === true, {
		message: 'You must confirm to generate RIBA demo project data',
	}),
	totalBudget: z.number().min(100000).max(10000000000).default(1000000000),
});

export const load: PageServerLoad = async () => {
	// if user id is SUPERADMIN_ID or we are in dev mode, then go forward, otherwise, return 403
	const { user } = await requireUser();
	if (user.id !== SUPERADMIN_ID && !dev) {
		throw new Error('Admin tools are only available in development mode');
	}

	// Initialize the forms
	const ribaForm = await superValidate(zod(ribaDemoProjectSchema), { errors: false });

	return {
		title: 'Admin Tools',
		ribaForm,
	};
};

export const actions: Actions = {
	generateRibaDemoProject: async ({ request, locals }) => {
		locals.log.info('Generating RIBA demo project data...');
		const { user } = await requireUser();
		if (user.id !== SUPERADMIN_ID && !dev) {
			throw new Error('Admin tools are only available in development mode');
		}

		const { supabase } = locals;

		// Validate form data
		const form = await superValidate(request, zod(ribaDemoProjectSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			locals.log.info({
				msg: 'Starting RIBA demo project generation with budget:',
				totalBudget: form.data.totalBudget,
			});
			// Call the generateRibaDemoProjectData function
			const result = await generateRibaDemoProjectData(supabase, form.data.totalBudget);
			locals.log.info({ msg: 'RIBA demo project generation completed successfully:', result });

			return message(form, {
				type: 'success',
				text: `RIBA demo project data generated successfully!`,
				data: {
					projectName: result.projectName,
					link: result.link,
				},
			});
		} catch (error) {
			locals.log.error({ msg: 'Error generating RIBA demo project data', error });

			return message(form, {
				type: 'error',
				text: `Error generating RIBA demo project data: ${error instanceof Error ? error.message : 'Unknown error'}`,
			});
		}
	},
};
