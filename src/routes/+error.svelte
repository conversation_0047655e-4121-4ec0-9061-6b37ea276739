<script lang="ts">
	import { page } from '$app/state';
</script>

<div>
	{#if page.status === 404}
		<h1 class="mb-4 text-3xl font-semibold">Not Found</h1>
		<p class="mb-6">
			That page does not exist. Try going back or go to the <a
				class="text-blue-600 underline"
				href="/">dashboard</a
			>.
		</p>
	{:else}
		<h1 class="mb-4 text-3xl font-semibold">{page.error?.message}</h1>
		{#if page.error?.errorId}
			<p><span class="font-bold">Error ID:&nbsp;</span>{page.error?.errorId}</p>
		{/if}
		<p class="mb-6">
			We are investigating the error. Try going back or to the <a
				class="text-blue-600 underline"
				href="/">dashboard</a
			>.
		</p>
	{/if}
</div>

<style>
	div {
		padding: 2rem 6rem;
	}
	p {
		margin-block-end: 1rem;
	}
</style>
