import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ url, locals }) => {
	const token = url.searchParams.get('token');
	if (!token) {
		return { title: 'Verify Email', status: 'invalid' };
	}

	const email = url.searchParams.get('email');
	if (!email) {
		return { title: 'Verify Email', status: 'invalid' };
	}

	const { error } = await locals.supabase.auth.verifyOtp({ type: 'signup', token, email });
	if (error) {
		return { title: 'Verify Email', status: 'error', message: error.message };
	}

	return { title: 'Verify Email', status: 'success' };
};
