<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { superForm } from 'sveltekit-superforms';
	import type { PageProps } from './$types';
	import { toast } from 'svelte-sonner';
	import SpinnerGapIcon from 'phosphor-svelte/lib/SpinnerGap';
	import { signOut } from '$lib/auth';

	const { data }: PageProps = $props();

	// Initialize forms only if not an email mismatch
	const acceptFormHandler = !data.emailMismatch
		? superForm(data.acceptForm!, {
				id: 'accept-invitation',
				onUpdated: ({ form }) => {
					if (form.message) {
						if (form.message.type === 'success') {
							toast.success(form.message.text);
						} else if (form.message.type === 'error') {
							toast.error(form.message.text);
						}
					}
				},
			})
		: undefined;

	const declineFormHandler = !data.emailMismatch
		? superForm(data.declineForm!, {
				id: 'decline-invitation',
				onUpdated: ({ form }) => {
					if (form.message) {
						if (form.message.type === 'success') {
							toast.success(form.message.text);
						} else if (form.message.type === 'error') {
							toast.error(form.message.text);
						}
					}
				},
				delayMs: 500,
				timeoutMs: 8000,
			})
		: undefined;

	const enhanceAccept = acceptFormHandler?.enhance;
	const delayedAccept = acceptFormHandler?.delayed;
	const submittingAccept = acceptFormHandler?.submitting;
	const enhanceDecline = declineFormHandler?.enhance;
</script>

<div class="container mx-auto max-w-lg py-8">
	<div class="rounded-lg border p-8 text-center">
		{#if data.emailMismatch}
			<h1>Email Mismatch</h1>
			<p class="mb-6 text-red-600">
				This invitation was sent to <span class="font-semibold">{data.invitedEmail}</span>, but you
				are currently signed in as <span class="font-semibold">{data.currentEmail}</span>.
			</p>
			<p class="mb-6">
				{#if data.inviter}
					<span class="font-semibold">{data.inviter}</span> invited
					<span class="font-semibold">{data.invitedEmail}</span> to join this resource.
				{:else}
					The invitation was sent to <span class="font-semibold">{data.invitedEmail}</span>.
				{/if}
			</p>
			<p class="text-muted-foreground mb-6 text-sm">
				To accept this invitation, please sign out and sign in with the invited email address, or
				contact the person who sent the invitation to request a new invitation for your current
				email address.
			</p>
			<div class="flex justify-center gap-4">
				<Button onclick={signOut} variant="outline">Sign Out</Button>
				<Button href="/" variant="default">Go to Dashboard</Button>
			</div>
		{:else}
			<h1>Invitation</h1>
			<p class="mb-6">
				{#if data.inviter}
					<span class="font-semibold">{data.inviter}</span> has invited you to join the
					<span class="font-semibold">{data.name}</span>
					{data.resource_type} as a
					<span class="font-semibold capitalize">{data.role}</span>.
				{:else}
					You've been invited to join the <span class="font-semibold">{data.name}</span>
					{data.resource_type} as a
					<span class="font-semibold capitalize">{data.role}</span>.
				{/if}
			</p>

			<div class="flex justify-center gap-4">
				{#if enhanceAccept && enhanceDecline}
					<form method="POST" action="?/accept" use:enhanceAccept>
						<input type="hidden" name="token" value={data.token} />
						<Button type="submit" variant="default" disabled={$submittingAccept}>
							{#if $delayedAccept}
								<SpinnerGapIcon class="text-primary size-4 animate-spin" />
							{/if}
							Accept Invitation</Button
						>
					</form>

					<form method="POST" action="?/decline" use:enhanceDecline>
						<input type="hidden" name="token" value={data.token} />
						<Button type="submit" variant="outline">Decline Invitation</Button>
					</form>
				{/if}
			</div>
		{/if}
	</div>
</div>
