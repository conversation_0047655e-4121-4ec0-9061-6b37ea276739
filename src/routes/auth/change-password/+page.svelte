<script lang="ts">
	import type { PageProps } from './$types';
	import { superForm } from 'sveltekit-superforms';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import SpinnerGapIcon from 'phosphor-svelte/lib/SpinnerGap';

	const { data }: PageProps = $props();
	const formHandler = superForm(data.form, {
		delayMs: 500,
		timeoutMs: 8000,
	});
	const { form, enhance, delayed, submitting } = formHandler;
</script>

<div class="container mx-auto max-w-md py-16">
	<h1 class="mb-6 text-2xl font-semibold">Change Password</h1>
	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance>
			<div class="space-y-4">
				<Form.Field form={formHandler} name="password">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>New Password <span class="text-red-500">*</span></Form.Label>
							<Input {...props} type="password" bind:value={$form.password} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={formHandler} name="confirmPassword">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Confirm Password <span class="text-red-500">*</span></Form.Label>
							<Input {...props} type="password" bind:value={$form.confirmPassword} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<div class="pt-6">
					<Form.Button class="w-full" disabled={$submitting}>
						Change Password
						{#if $delayed}
							<SpinnerGapIcon class="text-primary size-4 animate-spin" />
						{/if}
					</Form.Button>
				</div>
			</div>
		</form>
	</div>
</div>
