<script lang="ts">
	import { page } from '$app/state';
</script>

<div class="flex min-h-screen flex-col items-center justify-center p-4">
	<div class="w-full max-w-md space-y-8 rounded-lg border p-6 shadow-md">
		<div class="text-center">
			<h1 class="text-red-600">Authentication Error</h1>
			<p class="mt-2 text-sm text-gray-600">
				There was a problem with your authentication request.
			</p>
			{#if page.url.searchParams.get('error')}
				<p class="mt-4 text-sm text-red-500">
					Error: {page.url.searchParams.get('error')}
				</p>
			{/if}
		</div>

		<div class="mt-8 flex justify-center">
			<a
				href="/auth"
				class="rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden"
			>
				Return to login
			</a>
		</div>
	</div>
</div>
