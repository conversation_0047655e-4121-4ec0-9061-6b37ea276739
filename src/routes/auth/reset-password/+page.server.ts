import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { resetPasswordSchema } from '$lib/schemas/auth';
import { PUBLIC_VERCEL_URL } from '$env/static/public';
import { dev } from '$app/environment';

export const load: PageServerLoad = async () => {
	const form = await superValidate(zod(resetPasswordSchema));
	return { title: 'Reset Password', form };
};

export const actions: Actions = {
	default: async ({ request, locals }) => {
		const form = await superValidate(request, zod(resetPasswordSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { error } = await locals.supabase.auth.resetPasswordForEmail(form.data.email, {
			redirectTo: `${dev ? 'http' : 'https'}://${PUBLIC_VERCEL_URL}/auth/change-password`,
		});
		if (error) {
			form.errors.email = [error.message];
			return fail(400, { form });
		}

		return { success: true };
	},
};
