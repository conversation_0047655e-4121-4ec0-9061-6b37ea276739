import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { signUpSchema } from '$lib/schemas/auth';
import { PUBLIC_VERCEL_URL } from '$env/static/public';
import { dev } from '$app/environment';
import { redirect } from 'sveltekit-flash-message/server';
import { supabaseServiceClient } from '$lib/server/supabase_client';
import { getPostHogClient } from '$lib/server/posthog';

export const load: PageServerLoad = async ({ url }) => {
	const email = url.searchParams.get('email') ?? undefined;
	const form = await superValidate({ email }, zod(signUpSchema), { errors: false });
	return { title: 'Sign Up', form };
};

export const actions: Actions = {
	default: async ({ request, locals, cookies }) => {
		const form = await superValidate(request, zod(signUpSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		// restrict signups to aurora.cc emails for now
		let inviteData: { resource_type: string; resource_id: string } | null = null;
		// but allow existing users to invite others
		if (!form.data.email.endsWith('@aurora.cc') && !dev) {
			// check to see if the user has been invited using RPC function
			const { data: invite, error: inviteError } = await supabaseServiceClient
				.from('invite')
				.select('*')
				.eq('invitee_email', form.data.email)
				.eq('status', 'pending')
				.gte('expires_at', new Date().toISOString())
				.single();

			if (inviteError) {
				locals.log.error({ msg: 'Error checking invite:', inviteError });
			}

			if (!invite) {
				locals.log.error({ msg: 'Unauthorized signup request', email: form.data.email });
				await locals.supabase.from('waitlist').insert({ email: form.data.email });
				getPostHogClient().capture({
					distinctId: form.data.email,
					event: 'user_attempted_signup',
				});
				return message(form, {
					type: 'error',
					text: 'Signup is restricted at this time. We have noted your email and will be in touch when we are ready.',
				});
			}

			inviteData = {
				resource_type: invite.resource_type!,
				resource_id: invite.resource_id!,
			};
		}

		const emailRedirectTo = `${dev ? 'http' : 'https'}://${PUBLIC_VERCEL_URL}/auth/signin`;

		const { error } = await locals.supabase.auth.signUp({
			email: form.data.email,
			password: form.data.password,
			options: {
				emailRedirectTo,
			},
		});

		if (error) {
			locals.log.error({ error, email: form.data.email });
			return message(form, { type: 'error', text: error.message });
		}

		getPostHogClient().capture({
			distinctId: form.data.email,
			event: 'user_signed_up',
		});

		if (inviteData?.resource_type === 'organization' && inviteData.resource_id) {
			return redirect(
				'/auth/signin',
				{
					type: 'success',
					message: 'Account created successfully. Please check your email to verify.',
					data: {
						newOrgId: inviteData.resource_id,
					},
				},
				cookies,
			);
		} else {
			return redirect(
				'/auth/signin',
				{
					type: 'success',
					message: 'Account created successfully. Please check your email to verify.',
				},
				cookies,
			);
		}
	},
};
