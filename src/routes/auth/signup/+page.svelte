<script lang="ts">
	import type { PageProps } from './$types';
	import { superForm } from 'sveltekit-superforms';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { toast } from 'svelte-sonner';
	import SpinnerGapIcon from 'phosphor-svelte/lib/SpinnerGap';

	const { data }: PageProps = $props();
	const formHandler = superForm(data.form, {
		onUpdated({ form }) {
			if (form.message) {
				console.log({ type: form.message.type, text: form.message.text });
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const { form, enhance, delayed, submitting } = formHandler;
</script>

<div class="container mx-auto max-w-md py-16">
	<h1 class="mb-6 text-2xl font-semibold">Sign Up</h1>
	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance>
			<div class="space-y-4">
				<Form.Field form={formHandler} name="email">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Email <span class="text-red-500">*</span></Form.Label>
							<Input
								{...props}
								type="email"
								placeholder="<EMAIL>"
								bind:value={$form.email}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field form={formHandler} name="password">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Password <span class="text-red-500">*</span></Form.Label>
							<Input {...props} type="password" bind:value={$form.password} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<div class="pt-6">
					<Form.Button class="w-full" disabled={$submitting}>
						{#if $delayed}
							<SpinnerGapIcon class="text-primary size-4 animate-spin" />
						{/if}
						Sign Up</Form.Button
					>
				</div>
			</div>
			<div class="mt-4 text-center">
				<a
					href="/auth/signin{$form.email ? `?email=${encodeURIComponent($form.email)}` : ''}"
					class="text-sm text-blue-600 hover:underline">Already have an account?</a
				>
			</div>
		</form>
	</div>
</div>
