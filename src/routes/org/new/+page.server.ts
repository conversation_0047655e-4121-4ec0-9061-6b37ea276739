import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { organizationSchema } from '$lib/schemas/organization';
import { redirect } from 'sveltekit-flash-message/server';
import { ORG_COOKIE_NAME, ORG_COOKIE_OPTIONS } from '$lib/current-org.svelte';
import { getPostHogClient } from '$lib/server/posthog';

export const load: PageServerLoad = async () => {
	const form = await superValidate(zod(organizationSchema));
	return { title: 'New Organization', form };
};

export const actions: Actions = {
	default: async ({ request, locals, cookies }) => {
		const form = await superValidate(request, zod(organizationSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { supabase } = locals;

		// Create the organization using the RPC function
		const { data, error } = await supabase.rpc('create_organization', {
			name: form.data.name,
			description: form.data.description,
			logo_url: form.data.logo_url,
		});

		if (error) {
			if (error.code === '23505') {
				form.errors.name = [
					'An organization with that name already exists. Please choose another.',
				];
				return fail(400, { form });
			}
			locals.log.error({ msg: 'Error creating organization:', error });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create organization. Please try again.' },
			});
		}

		// The RPC function returns a JSON object with an org property containing the new organization
		// We need to cast the data to access the properties we know exist
		type OrgResponse = { org_id: string; name: string; logo_url: string | null };
		// First cast to unknown, then to our expected type (safer approach)
		const org = data as unknown as OrgResponse;

		getPostHogClient().capture({
			distinctId: locals.user!.id,
			event: 'organization_created',
			properties: {
				org_id: org.org_id,
			},
		});

		// Set the cookie for the new organization
		cookies.set(ORG_COOKIE_NAME, org.org_id, ORG_COOKIE_OPTIONS);

		// Redirect to clients page with the new org name in the URL
		return redirect(
			`/org/${encodeURIComponent(org.name)}/clients/new`,
			{
				type: 'success',
				message: 'Organization created successfully.',
				data: { newOrgId: org.org_id },
			},
			cookies,
		);
	},
};
