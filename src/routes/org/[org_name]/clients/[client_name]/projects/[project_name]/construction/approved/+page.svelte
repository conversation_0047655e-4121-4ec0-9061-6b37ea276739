<script lang="ts">
	import { goto } from '$app/navigation';
	import { superForm } from 'sveltekit-superforms';
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle,
	} from '$lib/components/ui/card';
	import { formatCurrency, formatDate } from '$lib/utils';
	import { cn } from '$lib/utils';
	import * as Command from '$lib/components/ui/command';
	import * as Popover from '$lib/components/ui/popover';
	import { Calendar } from '$lib/components/ui/calendar';
	import {
		DateFormatter,
		type DateValue,
		getLocalTimeZone,
		parseDate,
		today,
	} from '@internationalized/date';
	import * as Form from '$lib/components/ui/form';
	import { SvelteURLSearchParams } from 'svelte/reactivity';
	import {
		Calendar as CalendarIcon,
		SpinnerGap as SpinnerGapIcon,
		CaretUpDown as CaretUpDownIcon,
		Check as CheckIcon,
	} from 'phosphor-svelte';
	import { tick } from 'svelte';
	import { useId } from 'bits-ui';

	const { data } = $props();

	// Extract data from props
	const project = $derived(data.project);
	const client = $derived(data.client);
	const approvedChanges = $derived(data.approvedChanges);
	const rawWbsItems = $derived(data.wbsItems);
	const budgetMap = $derived(data.budgetMap);

	// Transform wbsItems for combobox usage
	const wbsItems = $derived(
		rawWbsItems.map((item) => ({
			label: `${item.code} - ${item.description}`,
			value: item.wbs_library_item_id.toString(),
		})),
	);

	// Helper functions for combobox
	let filterOpen = $state(false);
	let filterTriggerRef = $state<HTMLButtonElement>(null!);

	// We want to refocus the trigger button when the user selects
	// an item from the list so users can continue navigating the
	// rest of the form with the keyboard.
	function closeAndFocusTrigger(triggerId: string) {
		filterOpen = false;
		tick().then(() => {
			document.getElementById(triggerId)?.focus();
		});
	}
	const filterTriggerId = useId();

	function getWbsItemName(wbsId: string | null | undefined): string {
		if (!wbsId) return 'All WBS Items';

		// Handle special cases
		if (wbsId === '__project_budget_root__') return 'All WBS Items';
		if (wbsId === '__unallocated_risks__') return 'Unallocated';

		const item = wbsItems.find((item) => item.value === wbsId);
		return item ? item.label : 'Unknown';
	}

	// Setup filter form
	const filterForm = superForm(data.filterForm, {
		onUpdated({ form }) {
			// Build URL with filter parameters
			const params = new SvelteURLSearchParams();

			if (form.data.date_from) {
				params.set('date_from', form.data.date_from);
			}

			if (form.data.date_to) {
				params.set('date_to', form.data.date_to);
			}

			if (form.data.wbs_library_item_id) {
				params.set('wbs_item', form.data.wbs_library_item_id.toString());
			}

			// Navigate to the filtered URL
			goto(`?${params.toString()}`);
		},
	});

	// Form bindings
	const {
		form: filterData,
		enhance: enhanceFilterForm,
		delayed: delayedFilterForm,
		submitting: submittingFilterForm,
	} = filterForm;

	// Calculate totals for approved changes
	const totalDesignFees = $derived(
		approvedChanges.reduce((sum, change) => sum + (change.design_fees || 0), 0),
	);
	const totalConstructionTotal = $derived(
		approvedChanges.reduce((sum, change) => sum + (change.construction_total || 0), 0),
	);

	// Get change owner display name
	function getChangeOwnerDisplay(change: (typeof approvedChanges)[0]) {
		if (change.risk_owner?.full_name) {
			return change.risk_owner.full_name;
		} else if (change.risk_owner?.email) {
			return change.risk_owner?.email;
		} else if (change.risk_owner_name) {
			return change.risk_owner_name;
		} else if (change.risk_owner_email) {
			return change.risk_owner_email;
		}
		return 'Not assigned';
	}

	// No separate approver — approval tracked via status on risk register

	// Get original budget amount for WBS item
	function getOriginalBudgetAmount(wbsItemId: string | null) {
		if (!wbsItemId) return 0;
		return budgetMap[wbsItemId] || 0;
	}

	// Date formatter for displaying formatted dates
	const df = new DateFormatter('en-GB', {
		dateStyle: 'long',
	});

	// State variables for date pickers
	const startDate = $derived($filterData.date_from ? parseDate($filterData.date_from) : undefined);
	const endDate = $derived($filterData.date_to ? parseDate($filterData.date_to) : undefined);

	// Set placeholder for calendar
	let placeholder = $state<DateValue>(today(getLocalTimeZone()));
</script>

<svelte:head>
	<title>Approved Changes - {project.name} - {client.name}</title>
</svelte:head>

<div class="mx-auto mb-12 px-2 py-6 lg:px-6">
	<div class="mb-6 flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">Approved Changes</h1>
			<p class="text-muted-foreground mt-2">
				Changes that have been approved and are being implemented
			</p>
		</div>
	</div>

	<!-- Filter Card -->
	<Card class="mb-6">
		<CardHeader>
			<CardTitle>Filters</CardTitle>
			<CardDescription>Filter approved changes by date or WBS item</CardDescription>
		</CardHeader>
		<CardContent>
			<form
				action="?/applyFilters"
				method="POST"
				use:enhanceFilterForm
				class="grid gap-4 md:grid-cols-4"
			>
				<!-- Status filter removed; approval state inferred from risk status -->

				<div>
					<Form.Field form={filterForm} name="date_from">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>From Date</Form.Label>
								<Popover.Root>
									<Popover.Trigger
										{...props}
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-start text-left font-normal',
											!startDate && 'text-muted-foreground',
										)}
									>
										{startDate
											? df.format(startDate.toDate(getLocalTimeZone()))
											: 'Pick a start date'}
										<CalendarIcon class="ml-auto h-4 w-4 opacity-50" />
									</Popover.Trigger>
									<Popover.Content class="w-auto p-0" side="top">
										<Calendar
											type="single"
											value={startDate as DateValue}
											bind:placeholder
											calendarLabel="Start date"
											onValueChange={(v) => {
												if (v) {
													$filterData.date_from = v.toString();
												} else {
													$filterData.date_from = undefined;
												}
											}}
										/>
									</Popover.Content>
								</Popover.Root>
								<Form.FieldErrors />
								<input hidden value={$filterData.date_from} name={props.name} />
							{/snippet}
						</Form.Control>
					</Form.Field>
				</div>

				<div>
					<Form.Field form={filterForm} name="date_to">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>To Date</Form.Label>
								<Popover.Root>
									<Popover.Trigger
										{...props}
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-start text-left font-normal',
											!endDate && 'text-muted-foreground',
										)}
									>
										{endDate ? df.format(endDate.toDate(getLocalTimeZone())) : 'Pick an end date'}
										<CalendarIcon class="ml-auto h-4 w-4 opacity-50" />
									</Popover.Trigger>
									<Popover.Content class="w-auto p-0" side="top">
										<Calendar
											type="single"
											value={endDate as DateValue}
											bind:placeholder
											calendarLabel="End date"
											onValueChange={(v) => {
												if (v) {
													$filterData.date_to = v.toString();
												} else {
													$filterData.date_to = undefined;
												}
											}}
										/>
									</Popover.Content>
								</Popover.Root>
								<Form.FieldErrors />
								<input hidden value={$filterData.date_to} name={props.name} />
							{/snippet}
						</Form.Control>
					</Form.Field>
				</div>

				<div class="col-span-2">
					<Form.Field form={filterForm} name="wbs_library_item_id" class="flex flex-col">
						<Popover.Root bind:open={filterOpen}>
							<Form.Control id={filterTriggerId}>
								{#snippet children({ props })}
									<Form.Label>WBS Item</Form.Label>
									<Popover.Trigger
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-between',
											!$filterData.wbs_library_item_id && 'text-muted-foreground',
										)}
										role="combobox"
										bind:ref={filterTriggerRef}
										{...props}
									>
										{$filterData.wbs_library_item_id
											? getWbsItemName($filterData.wbs_library_item_id)
											: 'Select WBS item'}
										<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
									</Popover.Trigger>
									<input hidden value={$filterData.wbs_library_item_id} name={props.name} />
								{/snippet}
							</Form.Control>
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								side="bottom"
								align="start"
							>
								<Command.Root>
									<Command.Input autofocus placeholder="Search WBS items..." class="h-9" />
									<Command.Empty>No matching items found.</Command.Empty>
									<Command.Group class="max-h-[300px] overflow-y-auto">
										<Command.Item
											value="All WBS Items"
											onSelect={() => {
												$filterData.wbs_library_item_id = undefined;
												closeAndFocusTrigger(filterTriggerId);
											}}
										>
											All WBS Items
											<CheckIcon
												class={cn(
													'ml-auto size-4',
													$filterData.wbs_library_item_id && 'text-transparent',
												)}
											/>
										</Command.Item>
										<Command.Item
											value="Unallocated"
											onSelect={() => {
												$filterData.wbs_library_item_id = '__unallocated_risks__';
												closeAndFocusTrigger(filterTriggerId);
											}}
										>
											Unallocated
											<CheckIcon
												class={cn(
													'ml-auto size-4',
													$filterData.wbs_library_item_id !== '__unallocated_risks__' &&
														'text-transparent',
												)}
											/>
										</Command.Item>
										{#each wbsItems as option (option.value)}
											<Command.Item
												value={option.label}
												onSelect={() => {
													$filterData.wbs_library_item_id = option.value;
													closeAndFocusTrigger(filterTriggerId);
												}}
											>
												{option.label}
												<CheckIcon
													class={cn(
														'ml-auto size-4',
														option.value !== $filterData.wbs_library_item_id && 'text-transparent',
													)}
												/>
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="col-span-2 justify-self-end md:col-span-4">
					<Button type="submit" disabled={$submittingFilterForm}>
						{#if $delayedFilterForm}
							<SpinnerGapIcon class="text-primary size-4 animate-spin" />
						{/if}
						Apply Filters</Button
					>
				</div>
			</form>
		</CardContent>
	</Card>

	<!-- Summary Card -->
	<Card class="mb-6">
		<CardHeader>
			<CardTitle>Approved Changes Summary</CardTitle>
		</CardHeader>
		<CardContent>
			<div class="grid grid-cols-1 gap-4 md:grid-cols-4">
				<div class="flex flex-col justify-between">
					<h3 class="text-lg font-medium">Total Approved Changes</h3>
					<p class="text-2xl font-bold lg:text-3xl">{approvedChanges.length}</p>
				</div>
				<div class="flex flex-col justify-between">
					<h3 class="text-lg font-medium">Total Design Fees</h3>
					<p class="text-2xl font-bold lg:text-3xl">{formatCurrency(totalDesignFees)}</p>
				</div>
				<div class="flex flex-col justify-between">
					<h3 class="text-lg font-medium">Total Construction</h3>
					<p class="text-2xl font-bold lg:text-3xl">{formatCurrency(totalConstructionTotal)}</p>
				</div>
				<div class="flex flex-col justify-between">
					<h3 class="text-lg font-medium">Total Value</h3>
					<p class="text-2xl font-bold lg:text-3xl">
						{formatCurrency(totalDesignFees + totalConstructionTotal)}
					</p>
				</div>
			</div>
		</CardContent>
	</Card>

	<!-- Approved Changes Table -->
	<div class="rounded-md border">
		<Table class="table-auto xl:table-fixed">
			<TableHeader>
				<TableRow>
					<TableHead class=" xl:w-60">Title</TableHead>
					<TableHead class=" xl:w-40">WBS Item</TableHead>
					<TableHead class=" text-right"><p class="text-wrap">Original Budget</p></TableHead>
					<TableHead class=" text-right"><p class="text-wrap">Design Fees</p></TableHead>
					<TableHead class=" text-right">Construction</TableHead>
					<TableHead class=" text-right"><p class="text-wrap">Total Value</p></TableHead>
					<TableHead class=" text-right">Date</TableHead>
					<TableHead class="">Change Owner</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{#each approvedChanges as change (change.risk_id)}
					<TableRow>
						<TableCell class="font-medium whitespace-normal">
							<p class="">{change.title}</p>
							<p class="text-muted-foreground text-xs" title={change.description}>
								{change.description}
							</p>
						</TableCell>
						<TableCell>
							<p class="text-wrap">
								{change.wbs_item ? `${change.wbs_item.code} - ${change.wbs_item.description}` : '-'}
							</p>
						</TableCell>
						<TableCell class=" text-right">
							{change.wbs_library_item_id
								? formatCurrency(getOriginalBudgetAmount(change.wbs_library_item_id))
								: '-'}
						</TableCell>
						<TableCell class="text-right">
							{change.design_fees != null ? formatCurrency(change.design_fees) : '-'}
						</TableCell>
						<TableCell class=" text-right">
							{change.construction_total != null ? formatCurrency(change.construction_total) : '-'}
						</TableCell>
						<TableCell class="text-right">
							{formatCurrency((change.construction_total ?? 0) + (change.design_fees ?? 0))}
						</TableCell>
						<TableCell class=" text-right">{formatDate(change.date_identified)}</TableCell>
						<TableCell>{getChangeOwnerDisplay(change)}</TableCell>
					</TableRow>
				{:else}
					<TableRow>
						<TableCell class="py-4 text-center" colspan={7}>No approved changes found</TableCell>
					</TableRow>
				{/each}
			</TableBody>
		</Table>
	</div>
</div>
