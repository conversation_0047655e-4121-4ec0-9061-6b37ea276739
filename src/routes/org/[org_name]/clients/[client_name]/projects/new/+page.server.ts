import { projectSchema, projectShortId } from '$lib/schemas/project';
import { fail } from '@sveltejs/kit';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import type { Actions, PageServerLoad } from './$types';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';
import { StandardRibaStages, StandardICMSStages } from '$lib/project_utils';
import { getPostHogClient } from '$lib/server/posthog';

export const load: PageServerLoad = async ({ locals, cookies, params }) => {
	await requireUser();

	const { supabase } = locals;

	// check if user is a client admin
	const { client_name, org_name } = params;
	const { data: client, error: clientError } = await supabase
		.from('client')
		.select('client_id, organization(name)')
		.eq('organization.name', org_name)
		.eq('name', client_name)
		.limit(1)
		.maybeSingle();

	if (clientError) {
		locals.log.error({ msg: 'Error fetching client:', clientError });
		return redirect(
			`/org/${org_name}/clients`,
			{ type: 'error', message: 'Something went wrong trying to access that client.' },
			cookies,
		);
	}

	if (!client) {
		return redirect('/', { type: 'error', message: 'Client not found.' }, cookies);
	}

	const { data: isAdmin, error: isAdminError } = await supabase.rpc('is_client_admin', {
		client_id_param: client.client_id,
	});

	if (isAdminError) {
		locals.log.error({ msg: 'Error checking client admin status:', isAdminError });
	}

	if (!isAdmin) {
		return redirect(
			`/org/${org_name}/clients`,
			{ type: 'error', message: 'You do not have permission to create a project.' },
			cookies,
		);
	}

	// Get WBS libraries for dropdown
	const { data: wbsLibraries, error: wbsLibrariesError } = await supabase
		.from('wbs_library')
		.select('wbs_library_id, name')
		.order('name');

	if (wbsLibrariesError) {
		locals.log.error({ msg: 'Error fetching WBS libraries:', wbsLibrariesError });
	}

	// Create a form using the project schema with default values
	const form = await superValidate(
		{
			stage_selection_type: 'icms' as const, // Default to ICMS stages
			selected_icms_stages: StandardICMSStages.map((_, index) => index), // Default all ICMS stages selected
			selected_riba_stages: StandardRibaStages.map((_, index) => index), // Default all RIBA stages selected
			custom_stages: [{ name: '', description: '' }], // Default empty custom stage
		},
		zod(projectSchema),
		{ errors: false },
	);

	return {
		form,
		wbsLibraries: wbsLibraries || [],
	};
};

export const actions: Actions = {
	default: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;

		// Validate form data
		const form = await superValidate(request, zod(projectSchema));

		locals.log.info({ msg: 'creating project', data: form.data });

		if (!form.valid) {
			locals.log.error({ msg: 'Invalid form data:', errors: form.errors });
			return fail(400, { form });
		}

		// get the client
		const { data: client, error: clientError } = await supabase
			.from('client')
			.select('client_id, organization(name)')
			.eq('name', params.client_name)
			.eq('organization.name', params.org_name)
			.limit(1)
			.maybeSingle();

		if (clientError || !client) {
			locals.log.error({ msg: 'Error fetching client:', clientError });
			return message(form, { type: 'error', text: 'Error fetching client.' }, { status: 500 });
		}

		// Insert the project
		const { error: insertError } = await supabase.from('project').insert({
			name: form.data.name,
			description: form.data.description || null,
			client_id: client.client_id,
			wbs_library_id: form.data.wbs_library_id,
		});

		if (insertError) {
			locals.log.error({ msg: 'Error creating project:', insertError });
			return message(form, { type: 'error', text: 'Error creating project.' }, { status: 400 });
		}

		getPostHogClient().capture({
			distinctId: locals.user!.id,
			event: 'project_created',
			properties: {
				org_id: locals.orgId,
				client_id: client.client_id,
				stage_selection_type: form.data.stage_selection_type,
			},
		});

		// Get the created project
		const { data: project, error: projectError } = await supabase
			.from('project')
			.select('project_id')
			.eq('name', form.data.name)
			.limit(1)
			.maybeSingle();

		if (projectError || !project) {
			locals.log.error({ msg: 'Error fetching project:', projectError });
			return message(
				form,
				{ type: 'error', text: 'Error creating project stages.' },
				{ status: 500 },
			);
		}

		// Determine which stages to insert based on selection type
		let stagesToInsert: Array<{
			name: string;
			stage_order: number;
			stage?: number;
			description?: string;
			project_id: string;
		}> = [];
		let constructionStageOrder: number | null = null;

		if (form.data.stage_selection_type === 'icms') {
			// Only create stages for selected ICMS stages
			stagesToInsert = form.data.selected_icms_stages
				.map((stageIndex) => StandardICMSStages[stageIndex])
				.filter(Boolean) // Remove any undefined stages
				.map((stage, newIndex) => ({
					...stage,
					stage_order: newIndex, // Reorder based on selection
					project_id: project.project_id,
				}));

			// Find construction stage (ICMS stage called "Construction")
			const constructionStageIndex = stagesToInsert.findIndex((stage) =>
				stage.name.toLowerCase().includes('construction'),
			);
			if (constructionStageIndex !== -1) {
				constructionStageOrder = stagesToInsert[constructionStageIndex].stage_order;
			}
		} else if (form.data.stage_selection_type === 'riba') {
			// Only create stages for selected RIBA stages
			stagesToInsert = form.data.selected_riba_stages
				.map((stageIndex) => StandardRibaStages[stageIndex])
				.filter(Boolean) // Remove any undefined stages
				.map((stage, newIndex) => ({
					...stage,
					stage_order: newIndex, // Reorder based on selection
					project_id: project.project_id,
				}));

			// Find construction stage (RIBA stage called "Manufacturing and Construction")
			const constructionStageIndex = stagesToInsert.findIndex((stage) =>
				stage.name.toLowerCase().includes('construction'),
			);
			if (constructionStageIndex !== -1) {
				constructionStageOrder = stagesToInsert[constructionStageIndex].stage_order;
			}
		} else if (form.data.stage_selection_type === 'custom') {
			stagesToInsert = form.data.custom_stages.map((stage, index) => ({
				name: stage.name,
				stage_order: index,
				description: stage.description || undefined,
				project_id: project.project_id,
			}));

			// Find construction stage from custom stages
			const constructionStageIndex = form.data.custom_stages.findIndex(
				(stage) => stage.is_construction_stage,
			);
			if (constructionStageIndex !== -1) {
				constructionStageOrder = constructionStageIndex;
			}
		}

		const { data: insertedStages, error: stagesError } = await supabase
			.from('project_stage')
			.insert(stagesToInsert)
			.select('project_stage_id, stage_order');

		if (stagesError) {
			locals.log.error({ msg: 'Error inserting project stages:', stagesError });
			return message(
				form,
				{ type: 'error', text: 'Error creating project stages.' },
				{ status: 500 },
			);
		}

		// Update project with construction_stage_id if we found a construction stage
		if (constructionStageOrder !== null && insertedStages) {
			const constructionStage = insertedStages.find(
				(stage) => stage.stage_order === constructionStageOrder,
			);
			if (constructionStage) {
				const { error: updateError } = await supabase
					.from('project')
					.update({ construction_stage_id: constructionStage.project_stage_id })
					.eq('project_id', project.project_id);

				if (updateError) {
					locals.log.error({ msg: 'Error updating project with construction stage:', updateError });
					// Don't fail the entire operation, just log the error
				}
			}
		}

		// Redirect based on whether user wants to import from CostX
		if (form.data.import_from_costx) {
			return redirect(
				`/org/${encodeURIComponent(params.org_name)}/clients/${encodeURIComponent(params.client_name)}/projects/${encodeURIComponent(projectShortId(project.project_id))}/budget/import`,
				{
					type: 'success',
					message: 'Project created successfully. You can now import your CostX budget.',
				},
				cookies,
			);
		} else {
			return redirect(
				`/org/${encodeURIComponent(params.org_name)}/clients/${encodeURIComponent(params.client_name)}/projects/${encodeURIComponent(projectShortId(project.project_id))}`,
				{ type: 'success', message: 'Project created successfully.' },
				cookies,
			);
		}
	},
};
