import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { editInvoiceSchema } from '$lib/schemas/invoice';
import { requireUser } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	const { user } = await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_id_short, invoice_id } = params;

	const project_id = projectUUID(project_id_short);

	// Get invoice data with purchase order to verify project access
	const { data: invoice, error: invoiceError } = await supabase
		.from('invoice')
		.select('*, purchase_order!inner(project_id)')
		.eq('invoice_id', invoice_id)
		.eq('purchase_order.project_id', project_id)
		.single();

	if (invoiceError || !invoice) {
		locals.log.error({ msg: 'Error fetching invoice', invoiceError });
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/actuals`,
			{ type: 'error', message: 'Invoice not found' },
			cookies,
		);
	}

	// Get vendors accessible for this project
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		entity_type_param: 'project',
		entity_id_param: project_id,
		user_id_param: user.id,
	});

	if (vendorsError) {
		locals.log.error({ msg: 'Error fetching vendors', vendorsError });
		throw new Error('Failed to fetch vendors');
	}

	// Get purchase orders for this project
	const { data: purchaseOrders, error: purchaseOrdersError } = await supabase.rpc(
		'get_accessible_purchase_orders',
		{
			project_id_param: project_id,
		},
	);

	if (purchaseOrdersError) {
		locals.log.error({ msg: 'Error fetching purchase orders', purchaseOrdersError });
		throw new Error('Failed to fetch purchase orders');
	}

	// Initialize form with invoice data
	const form = await superValidate(invoice, zod(editInvoiceSchema));

	return {
		form,
		vendors: vendors || [],
		purchaseOrders: purchaseOrders || [],
		invoice,
	};
};

export const actions: Actions = {
	default: async ({ request, locals, cookies, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_id_short, invoice_id } = params;

		const form = await superValidate(request, zod(editInvoiceSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Remove invoice_id from the update data
		const { invoice_id: _, ...updateData } = form.data;

		// Update the invoice
		const { data: invoice, error: invoiceError } = await supabase
			.from('invoice')
			.update(updateData)
			.eq('invoice_id', invoice_id)
			.select('invoice_id, purchase_order_id, purchase_order:purchase_order_id(po_number)')
			.single();

		if (invoiceError) {
			locals.log.error({ msg: 'Error updating invoice', invoiceError });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update invoice' },
			});
		}

		return redirect(
			303,
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/actuals`,
			{
				type: 'success',
				message: `Invoice ${invoice.purchase_order?.po_number || 'Unknown PO'} updated successfully`,
			},
			cookies,
		);
	},
};
