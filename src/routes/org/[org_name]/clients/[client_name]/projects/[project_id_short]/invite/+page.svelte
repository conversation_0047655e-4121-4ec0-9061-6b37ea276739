<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { superForm } from 'sveltekit-superforms';
	import type { PageProps } from './$types';
	import { toast } from 'svelte-sonner';

	const { data }: PageProps = $props();

	const project = $derived(data.project);

	const { form, enhance, errors, constraints } = superForm(data.form, {
		resetForm: true,
		onUpdated: ({ form }) => {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
</script>

<div class="container mx-auto max-w-2xl py-8">
	<div class="mb-6">
		<h1>Invite Team Members to {project.name}</h1>
		<p class="text-muted-foreground mt-1">Add team members to collaborate on this project.</p>
	</div>

	<div class="@container flex max-w-xl flex-col gap-6">
		<!-- Invite Form Section -->
		<div class="rounded-lg border p-6">
			<h2 class="mb-4 text-xl font-semibold">Send Invitation</h2>
			<form method="POST" use:enhance>
				<div class="mb-4">
					<label for="email" class="mb-1 block text-sm font-medium">Email Address</label>
					<Input
						type="email"
						id="email"
						name="email"
						bind:value={$form.email}
						placeholder="<EMAIL>"
						class="w-full"
						{...$constraints.email}
					/>
					{#if $errors.email}
						<p class="mt-1 text-sm text-red-500">{$errors.email}</p>
					{/if}
				</div>

				<div class="mb-4">
					<label for="role" class="mb-1 block text-sm font-medium">Role</label>
					<div class="relative">
						<select
							id="role"
							name="role"
							bind:value={$form.role}
							class="w-full rounded-md border bg-transparent px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
						>
							<option value="viewer">Viewer (can only view)</option>
							<option value="editor">Editor (can edit but not invite users)</option>
							<option value="owner">Owner (full access, can invite users)</option>
						</select>
					</div>
					{#if $errors.role}
						<p class="mt-1 text-sm text-red-500">{$errors.role}</p>
					{/if}
				</div>

				<div class="mt-4">
					<Button type="submit" class="w-full">Send Invitation</Button>
				</div>
			</form>
		</div>
	</div>
</div>
