import { redirect } from 'sveltekit-flash-message/server';
import { requireProject } from '$lib/server/auth';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { fail } from '@sveltejs/kit';
import { getBudgetLineItems, upsertBudgetLineItem } from '$lib/project_utils';
import type { PageServerLoad } from './$types';
import { budgetItemSchema, projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals, url, cookies }) => {
	const { supabase } = locals;
	const budget_line_item_id = url.searchParams.get('id');
	const { org_name, client_name, project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	if (!budget_line_item_id) {
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/budget`,
			{ type: 'error', message: 'Budget item not found' },
			cookies,
		);
	}

	// Get budget items and find the specific budget item
	let budgetItems: Awaited<ReturnType<typeof getBudgetLineItems>> = [];
	try {
		budgetItems = await getBudgetLineItems(supabase, project_id);
	} catch (error) {
		locals.log.error({ msg: 'Error fetching budget items', error });
	}

	// Find the specific budget item
	const budgetItem = budgetItems.find((item) => item.budget_line_item_id === budget_line_item_id);

	if (!budgetItem) {
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/budget`,
			{ type: 'error', message: 'Budget item not found' },
			cookies,
		);
	}

	// Get the WBS item for this budget item
	const { data: wbsItem } = await supabase
		.from('wbs_library_item')
		.select('wbs_library_item_id, code, description')
		.eq('wbs_library_item_id', budgetItem.wbs_library_item_id)
		.limit(1)
		.maybeSingle();

	if (!wbsItem) {
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/budget`,
			{ type: 'error', message: 'WBS item not found' },
			cookies,
		);
	}

	// Check user permissions - moved after main data fetching
	const { data: canEditProject } = await supabase.rpc('can_modify_project', {
		project_id_param: project_id,
	});

	if (!canEditProject) {
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/budget`,
			{ type: 'error', message: 'You do not have permission to edit this project' },
			cookies,
		);
	}

	// Create the form with the budget item schema and prefill it
	const form = await superValidate(budgetItem, zod(budgetItemSchema));

	return {
		wbsItem,
		form,
	};
};

export const actions = {
	updateBudgetItem: async ({ request, locals, cookies }) => {
		const { supabase } = locals;

		// Validate the form
		const form = await superValidate(request, zod(budgetItemSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: form.data.project_id,
		});

		if (!canEdit) {
			return message(
				form,
				{ type: 'error', text: 'You do not have permission to edit this project' },
				{ status: 403 },
			);
		}

		// Update the budget line item
		try {
			// Use the upsertBudgetLineItem utility to ensure proper cost calculation
			await upsertBudgetLineItem(supabase, form.data, 'Updated from edit page');
		} catch (error) {
			locals.log.error({ msg: 'Error updating budget line item', error });
			return message(
				form,
				{ type: 'error', text: 'Error saving budget line item' },
				{ status: 500 },
			);
		}
		redirect(
			`../budget`,
			{ type: 'success', message: 'Budget line item updated successfully' },
			cookies,
		);
	},
};
