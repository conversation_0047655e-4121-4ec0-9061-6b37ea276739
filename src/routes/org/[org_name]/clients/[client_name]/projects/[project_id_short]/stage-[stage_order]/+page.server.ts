import { getGatewayChecklistItems } from '$lib/project_utils';
import { projectUUID } from '$lib/schemas/project';
import { requireProject } from '$lib/server/auth';
import { redirect } from '@sveltejs/kit';
import type { ServerLoad } from '@sveltejs/kit';

export const load: ServerLoad = async ({ params, locals }) => {
	const { supabase } = locals;
	const { stage_order } = params;

	const { client_name, project_id_short, org_name } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Combined query to fetch project with client, organization, and all project stages in one query
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select(
			`
			*,
			project_stages:project_stage!project_stage_project_id_fkey(*)
		`,
		)
		.eq('project_id', project_id)
		.single();

	if (projectError) {
		locals.log.error({ msg: 'Error fetching project:', projectError });
		throw redirect(
			303,
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
		);
	}

	// Extract related data from the combined query
	const allStages = projectData.project_stages;

	// Sort stages by stage_order
	allStages.sort((a, b) => a.stage_order - b.stage_order);

	// Find the requested stage
	const stageData = allStages.find((stage) => stage.stage_order === Number(stage_order));

	if (!stageData) {
		throw redirect(
			303,
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/stage-manager`,
		);
	}

	// Find the current active stage (first incomplete stage)
	const currentStage = allStages.find((stage) => !stage.date_completed) || allStages[0];

	// Check if this stage is accessible (completed stages or current stage)
	const isAccessible =
		stageData.date_completed || (currentStage && stageData.stage_order <= currentStage.stage_order);

	// Get gateway checklist items for this stage
	const checklistItems = await getGatewayChecklistItems(supabase, stageData.project_stage_id);

	// Calculate checklist completion percentage
	const totalItems = checklistItems?.length || 0;
	const completedItems = checklistItems?.filter((item) => item.status === 'Complete')?.length || 0;
	const completionPercentage = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;

	return {
		project: projectData,
		stage: stageData,
		isAccessible,
		checklist: {
			items: checklistItems || [],
			total: totalItems,
			completed: completedItems,
			completionPercentage,
		},
	};
};
