import { error, fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { wbsLibraryItemSchema, wbsLibraryItemWithIdSchema } from '$lib/schemas/wbs';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { buildWbsItemTree } from '$lib/wbs_utils';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	// Check authentication
	await requireUser();

	const { supabase } = locals;
	const { client_name, project_id_short, org_name } = params;

	const project_id = projectUUID(project_id_short);

	// Combined query to fetch project with client and WBS library data
	const { data: project, error: projectError } = await supabase
		.from('project')
		.select(
			`
			*,
			wbs_library(*)
		`,
		)
		.eq('project_id', project_id)
		.limit(1)
		.maybeSingle();

	if (projectError || !project) {
		locals.log.error({ msg: 'Error fetching project:', projectError });
		return redirect(
			`/org/${org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found.' },
			cookies,
		);
	}

	// Extract WBS library from the combined query
	const wbsLibrary = project.wbs_library;

	if (!wbsLibrary) {
		locals.log.error({ msg: 'WBS library not found for project' });
		throw error(500, { message: 'Error loading WBS library' });
	}

	// Fetch all WBS library items in parallel
	const { data, error: wbsItemsError } = await supabase
		.from('wbs_library_item')
		.select('*')
		.or(
			`and(wbs_library_id.eq.${project.wbs_library_id},item_type.eq.Standard),and(client_id.eq.${project.client_id},item_type.eq.Custom,project_id.is.null),and(client_id.eq.${project.client_id},item_type.eq.Custom,project_id.eq.${project.project_id})`,
		)
		.order('code');

	if (wbsItemsError) throw wbsItemsError;

	const mainWbsLibraryItems = data.filter(
		(item) => item.item_type === 'Standard' && item.wbs_library_id === project.wbs_library_id,
	);

	const clientItems = data.filter(
		(item) =>
			item.item_type === 'Custom' &&
			item.client_id === project.client_id &&
			item.project_id === null,
	);

	const projectItems = data.filter(
		(item) =>
			item.item_type === 'Custom' &&
			item.client_id === project.client_id &&
			item.project_id === project.project_id,
	);

	if (wbsItemsError) {
		locals.log.error({ msg: 'Error fetching WBS library items:', wbsItemsError });
		throw error(500, { message: 'Error loading WBS library items' });
	}

	// Check if user has permission to access this project
	const { data: hasPermission, error: permissionError } = await supabase.rpc('can_access_project', {
		project_id_param: project.project_id,
	});

	if (permissionError) {
		locals.log.error({ msg: 'Error checking project permissions:', permissionError });
		throw error(500, { message: 'Error checking permissions' });
	}

	if (!hasPermission) {
		throw error(403, { message: 'You do not have permission to access this project' });
	}

	// Convert flat items structures to hierarchical trees
	const mainItemsTree = buildWbsItemTree(mainWbsLibraryItems);
	const clientItemsTree = buildWbsItemTree(clientItems);
	const projectItemsTree = buildWbsItemTree(projectItems);

	// Create form with schema validation
	const form = await superValidate({ item_type: 'Custom' as const }, zod(wbsLibraryItemSchema));

	return {
		project,
		wbsLibrary,
		mainWbsLibraryItems,
		mainItemsTree,
		clientItems,
		clientItemsTree,
		projectItems,
		projectItemsTree,
		form,
	};
};

export const actions: Actions = {
	createItem: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const { client_name, project_id_short, org_name } = params;

		const project_id = projectUUID(project_id_short);

		// Validate form data
		const form = await superValidate(request, zod(wbsLibraryItemSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Fetch the client and project
		const { data: project, error: projectError } = await supabase
			.from('project')
			.select('*')
			.eq('project_id', project_id)
			.limit(1)
			.maybeSingle();

		if (projectError || !project) {
			locals.log.error({ msg: 'Error fetching project:', projectError });
			return redirect(
				`/org/${org_name}/clients/${client_name}`,
				{ type: 'error', message: 'Project not found.' },
				cookies,
			);
		}

		// Check if user has permission to access this project
		const { data: hasPermission, error: permissionError } = await supabase.rpc(
			'can_modify_project',
			{
				project_id_param: project_id,
			},
		);

		if (permissionError) {
			locals.log.error({ msg: 'Error checking project permissions:', permissionError });
			throw error(500, { message: 'Error checking permissions' });
		}

		if (!hasPermission) {
			throw error(403, { message: 'You do not have permission to access this project' });
		}

		// Ensure client_id matches the current client
		if (form.data.client_id !== project.client_id) {
			return fail(400, { form, error: 'Invalid client ID' });
		}

		// Ensure project_id matches the current project
		if (form.data.project_id !== project.project_id) {
			return fail(400, { form, error: 'Invalid project ID' });
		}

		// Confirm that the code starts with the parent_id's code
		if (form.data.parent_item_id) {
			const { data: parentItem, error: parentError } = await supabase
				.from('wbs_library_item')
				.select('code')
				.eq('wbs_library_item_id', form.data.parent_item_id)
				.limit(1)
				.maybeSingle();

			if (parentError || !parentItem) {
				locals.log.error({ msg: 'Error fetching parent item:', parentError });
				return fail(400, { form, error: 'Parent item not found' });
			}

			if (!form.data.code.startsWith(`${parentItem.code}.`)) {
				return fail(400, { form, error: "Code must start with the parent item's code" });
			}
		}

		// Insert the new WBS item
		const { error: insertError } = await supabase.from('wbs_library_item').insert({
			wbs_library_id: project.wbs_library_id,
			code: form.data.code,
			description: form.data.description,
			cost_scope: form.data.cost_scope,
			parent_item_id: form.data.parent_item_id,
			level: form.data.level,
			in_level_code: form.data.in_level_code,
			item_type: 'Custom',
			client_id: form.data.client_id,
			project_id: form.data.project_id,
		});

		if (insertError) {
			locals.log.error({ msg: 'Error creating WBS item:', insertError });
			return fail(500, { form, error: 'Failed to create WBS item' });
		}

		return message(form, { type: 'success', text: 'WBS item created successfully' });
	},

	deleteItem: async ({ request, locals }) => {
		const { supabase } = locals;
		// Validate form data
		const form = await superValidate(request, zod(wbsLibraryItemWithIdSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const id = form.data.wbs_library_item_id;
		if (!id) {
			return fail(400, { error: 'Item ID is required' });
		}

		// Delete the WBS item
		const { error: deleteError } = await supabase
			.from('wbs_library_item')
			.delete()
			.eq('wbs_library_item_id', id)
			.eq('item_type', 'Custom');

		if (deleteError) {
			locals.log.error({ msg: 'Error deleting WBS item:', deleteError });
			return fail(500, { error: 'Failed to delete WBS item' });
		}

		return message(form, { type: 'success', text: 'WBS item deleted successfully' });
	},

	updateItemShare: async ({ request, locals }) => {
		const { supabase } = locals;
		const form = await superValidate(request, zod(wbsLibraryItemWithIdSchema));
		if (!form.valid) {
			return fail(400, { form });
		}
		const id = form.data.wbs_library_item_id;
		const makeClientWide = !form.data.project_id;

		if (!id) {
			return fail(400, { error: 'Item ID is required' });
		}

		// Update the project_id field based on sharing preference
		const { error: updateError } = await supabase
			.from('wbs_library_item')
			.update({
				project_id: makeClientWide ? null : form.data.project_id,
			})
			.eq('wbs_library_item_id', id)
			.eq('item_type', 'Custom');

		if (updateError) {
			locals.log.error({ msg: 'Error updating WBS item:', updateError });
			return fail(500, { error: 'Failed to update WBS item sharing' });
		}

		return message(form, {
			type: 'success',
			text: makeClientWide
				? 'WBS item is now shared with all client projects'
				: 'WBS item is now specific to this project only',
		});
	},
};
