import type { PageServerLoad } from './$types';
import { requireProject } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals }) => {
	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch invoices using the RPC function
	const { data: invoices, error: invoicesError } = await supabase.rpc('get_accessible_invoices', {
		project_id_param: project_id,
	});

	if (invoicesError) {
		locals.log.error({ msg: 'Error fetching invoices', invoicesError });
		throw new Error('Failed to fetch invoices');
	}

	return {
		invoices: invoices || [],
	};
};
