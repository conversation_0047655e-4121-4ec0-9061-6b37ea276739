import { fail, error } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { purchaseOrderSchema } from '$lib/schemas/purchase_order';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { createVendor, createVendorModal } from '$lib/components/forms/vendor/vendor_form_actions';
import { vendorSchema } from '$lib/schemas/vendor';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals }) => {
	const { user } = await requireUser();

	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch accessible vendors for this project
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		user_id_param: user.id,
		entity_type_param: 'project',
		entity_id_param: project_id,
	});

	if (vendorsError) {
		locals.log.error({ msg: 'Error fetching vendors:', vendorsError });
		throw new Error('Failed to fetch vendors');
	}

	// Fetch work packages for selection
	const { data: workPackages, error: workPackagesError } = await supabase.rpc(
		'get_work_packages_for_project',
		{
			project_id_param: project_id,
		},
	);

	if (workPackagesError) {
		locals.log.error({ msg: 'Error fetching work packages:', workPackagesError });
		throw error(500, 'Failed to fetch work packages');
	}

	const form = await superValidate(zod(purchaseOrderSchema));
	const newVendorForm = await superValidate(zod(vendorSchema));

	return {
		form,
		newVendorForm,
		vendors: vendors || [],
		workPackages: workPackages || [],
	};
};

export const actions: Actions = {
	createVendor,
	createVendorModal,
	createPurchaseOrder: async ({ request, locals, cookies }) => {
		const { user } = await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_id_short } = requireProject();

		const form = await superValidate(request, zod(purchaseOrderSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const project_id = projectUUID(project_id_short);

		// Prepare the purchase order data
		const purchaseOrderData = {
			...form.data,
			project_id: project_id,
			created_by_user_id: user.id,
		};

		// Insert the purchase order
		const { data: purchaseOrder, error: purchaseOrderError } = await supabase
			.from('purchase_order')
			.insert(purchaseOrderData)
			.select('purchase_order_id, po_number')
			.single();

		if (purchaseOrderError) {
			locals.log.error({ msg: 'Error creating purchase order:', purchaseOrderError });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create purchase order' },
			});
		}

		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/purchase-order`,
			{
				type: 'success',
				message: `Purchase order ${purchaseOrder.po_number} created successfully`,
			},
			cookies,
		);
	},
};
