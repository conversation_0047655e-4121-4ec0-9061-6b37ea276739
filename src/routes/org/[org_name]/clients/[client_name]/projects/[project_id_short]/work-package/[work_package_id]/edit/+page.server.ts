import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { editWorkPackageSchema } from '$lib/schemas/work_package';
import { redirect } from 'sveltekit-flash-message/server';
import { requireProject } from '$lib/server/auth';
import { error } from '@sveltejs/kit';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ params, locals }) => {
	const { supabase } = locals;
	const { project_id_short, work_package_id } = params;
	requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch the work package with project information
	const { data: workPackage, error: workPackageError } = await supabase
		.from('work_package')
		.select(
			`
			*,
			project!inner(
				project_id
			)
		`,
		)
		.eq('work_package_id', work_package_id)
		.eq('project.project_id', project_id)
		.single();

	if (workPackageError || !workPackage) {
		locals.log.error({ msg: 'Error fetching work package:', workPackageError });
		throw error(404, 'Work package not found');
	}

	// Fetch WBS library items for selection
	const { data: wbsItems, error: wbsItemsError } = await supabase.rpc(
		'get_wbs_items_for_work_package',
		{
			project_id_param: workPackage.project.project_id,
		},
	);

	if (wbsItemsError) {
		locals.log.error({ msg: 'Error fetching WBS items:', wbsItemsError });
		throw error(500, 'Failed to fetch WBS items');
	}

	// Prepare form data
	const formData = {
		work_package_id: workPackage.work_package_id,
		name: workPackage.name,
		description: workPackage.description,
		wbs_library_item_id: workPackage.wbs_library_item_id,
	};

	const form = await superValidate(formData, zod(editWorkPackageSchema));

	return {
		form,
		workPackage,
		wbsItems: wbsItems || [],
	};
};

export const actions: Actions = {
	default: async ({ request, locals, cookies, params }) => {
		const { supabase } = locals;
		const { org_name, client_name, project_id_short, work_package_id } = params;

		const form = await superValidate(request, zod(editWorkPackageSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Remove work_package_id from the update data
		const { work_package_id: _, ...updateData } = form.data;

		// Update the work package
		const { data: workPackage, error: workPackageError } = await supabase
			.from('work_package')
			.update(updateData)
			.eq('work_package_id', work_package_id)
			.select('work_package_id, name')
			.single();

		if (workPackageError) {
			locals.log.error({ msg: 'Error updating work package:', workPackageError });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update work package' },
			});
		}

		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
				client_name,
			)}/projects/${encodeURIComponent(project_id_short)}/work-package`,
			{
				type: 'success',
				message: `Work package "${workPackage.name}" updated successfully`,
			},
			cookies,
		);
	},
};
