import { requireProject } from '$lib/server/auth';
import { requireUser } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import type { PageServerLoad } from './$types';
import { projectUUID } from '$lib/schemas/project';

export const load = (async ({ locals, cookies, depends }) => {
	depends('project:budget');

	await requireUser();

	const { supabase } = locals;
	const { org_name, client_name, project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select(
			`*,
				wbs_library:wbs_library(wbs_library_item(*)),
				budget_line_item_current(*)`,
		)
		.eq('project_id', project_id)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		locals.log.error({ msg: 'Error fetching project', projectError });
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Extract related data returned from the join
	const allWbsItems = projectData.wbs_library?.wbs_library_item || [];
	const rawCurrentItems =
		projectData.budget_line_item_current.map((bli) => {
			return {
				...bli,
				label: 'current',
			};
		}) || [];

	const wbsItems =
		allWbsItems?.map((i) => ({
			label: `${i.code}: ${i.description}`,
			value: i.wbs_library_item_id,
		})) || [];

	return {
		wbsItems,
		allWbsItems: allWbsItems || [],
		rawCurrentItems: rawCurrentItems || [],
	};
}) satisfies PageServerLoad;
