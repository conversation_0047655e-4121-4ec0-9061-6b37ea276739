<script lang="ts">
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import DotsThreeVerticalIcon from 'phosphor-svelte/lib/DotsThreeVertical';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import * as Dialog from '$lib/components/ui/dialog';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { formatCurrency, formatDate } from '$lib/schemas/invoice';
	import { formatAccountingPeriod } from '$lib/schemas/invoice';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import { toast } from 'svelte-sonner';

	const { data }: PageProps = $props();

	const invoices = $derived(data.invoices);
	const project = $derived(data.project);

	let deleteDialogOpen = $state(false);
	let invoiceToDelete = $state<(typeof invoices)[0] | null>(null);
	let isDeleting = $state(false);

	function openDeleteDialog(invoice: (typeof invoices)[0]) {
		invoiceToDelete = invoice;
		deleteDialogOpen = true;
	}

	async function deleteInvoice() {
		if (!invoiceToDelete) return;

		isDeleting = true;
		try {
			const response = await fetch(
				`/org/${encodeURIComponent(data.org_name)}/clients/${encodeURIComponent(data.client_name)}/projects/${encodeURIComponent(data.project_id_short)}/actuals/${invoiceToDelete.invoice_id}`,
				{
					method: 'DELETE',
				},
			);

			if (response.ok) {
				toast.success(`Invoice ${invoiceToDelete.po_number} deleted successfully`);
				// Refresh the page to update the list
				goto(page.url, { invalidateAll: true });
			} else {
				toast.error('Failed to delete invoice');
			}
		} catch (error) {
			console.error('Error deleting invoice:', error);
			toast.error('Failed to delete invoice');
		} finally {
			isDeleting = false;
			deleteDialogOpen = false;
			invoiceToDelete = null;
		}
	}
</script>

<div class="container mx-auto py-8">
	<div class="mb-6 flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-semibold">Actuals</h1>
			<p class="text-muted-foreground mt-1">
				Manage invoices and actual costs for {project.name}
			</p>
		</div>
		<Button
			href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
				data.client_name,
			)}/projects/{encodeURIComponent(data.project_id_short)}/actuals/new"
			class="gap-2"
		>
			<PlusIcon class="size-4" />
			New Invoice
		</Button>
	</div>

	{#if invoices.length === 0}
		<div class="rounded-lg border border-dashed p-8 text-center">
			<p class="text-muted-foreground">
				No invoices found. Create your first invoice to get started.
			</p>
		</div>
	{:else}
		<div class="rounded-md border">
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead class="w-32">PO Number</TableHead>
						<TableHead class="w-48">Vendor</TableHead>
						<TableHead class="w-64">Description</TableHead>
						<TableHead class="w-32">Invoice Date</TableHead>
						<TableHead class="w-32">Account</TableHead>
						<TableHead class="w-32 text-right">Amount</TableHead>
						<TableHead class="w-32">Period</TableHead>
						<TableHead class="w-32">Post Date</TableHead>
						<TableHead class="w-20 pr-4 text-right">Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each invoices as invoice (invoice.invoice_id)}
						<TableRow>
							<TableCell class="font-medium">
								{invoice.po_number}
							</TableCell>
							<TableCell>
								{invoice.vendor_name || 'Unknown Vendor'}
							</TableCell>
							<TableCell>
								<div class="max-w-64 truncate">
									{invoice.description || 'No description'}
								</div>
							</TableCell>
							<TableCell>
								{formatDate(invoice.invoice_date)}
							</TableCell>
							<TableCell>
								{invoice.account}
							</TableCell>
							<TableCell class="text-right font-medium">
								{formatCurrency(invoice.amount)}
							</TableCell>
							<TableCell>
								{invoice.period ? formatAccountingPeriod(invoice.period) : '—'}
							</TableCell>
							<TableCell>
								{formatDate(invoice.post_date)}
							</TableCell>
							<TableCell class="text-right">
								<DropdownMenu.Root>
									<DropdownMenu.Trigger>
										{#snippet child({ props })}
											<Button {...props} variant="ghost" class="size-8 p-0">
												<span class="sr-only">Open menu</span>
												<DotsThreeVerticalIcon class="size-4" />
											</Button>
										{/snippet}
									</DropdownMenu.Trigger>
									<DropdownMenu.Content align="end">
										<DropdownMenu.Item>
											<a
												href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
													data.client_name,
												)}/projects/{encodeURIComponent(
													data.project_id_short,
												)}/actuals/{invoice.invoice_id}/edit"
												class="flex w-full"
											>
												Edit
											</a>
										</DropdownMenu.Item>
										<DropdownMenu.Item onclick={() => openDeleteDialog(invoice)}>
											Delete
										</DropdownMenu.Item>
									</DropdownMenu.Content>
								</DropdownMenu.Root>
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		</div>
	{/if}
</div>

<!-- Delete Confirmation Dialog -->
<Dialog.Root bind:open={deleteDialogOpen}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Delete Invoice</Dialog.Title>
			<Dialog.Description>
				Are you sure you want to delete invoice "{invoiceToDelete?.po_number}"? This action cannot
				be undone.
			</Dialog.Description>
		</Dialog.Header>
		<Dialog.Footer>
			<Button variant="outline" onclick={() => (deleteDialogOpen = false)} disabled={isDeleting}>
				Cancel
			</Button>
			<Button variant="destructive" onclick={deleteInvoice} disabled={isDeleting}>
				{isDeleting ? 'Deleting...' : 'Delete'}
			</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
