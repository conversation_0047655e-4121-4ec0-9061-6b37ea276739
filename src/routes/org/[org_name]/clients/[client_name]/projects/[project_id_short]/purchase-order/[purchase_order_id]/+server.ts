import { error, json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';

export const DELETE: RequestHandler = async ({ params, locals }) => {
	await requireUser();

	const { supabase } = locals;
	const { project_id_short, purchase_order_id } = params;
	requireProject();

	const project_id = projectUUID(project_id_short);

	// Verify the purchase order exists and belongs to the correct project
	const { data: purchaseOrder, error: fetchError } = await supabase
		.from('purchase_order')
		.select(
			`
			purchase_order_id,
			po_number,
			project(project_id)
		`,
		)
		.eq('purchase_order_id', purchase_order_id)
		.eq('project.project_id', project_id)
		.single();

	if (fetchError || !purchaseOrder) {
		console.error('Error fetching purchase order for deletion:', fetchError);
		throw error(404, 'Purchase order not found');
	}

	// Delete the purchase order
	const { error: deleteError } = await supabase
		.from('purchase_order')
		.delete()
		.eq('purchase_order_id', purchase_order_id);

	if (deleteError) {
		console.error('Error deleting purchase order:', deleteError);
		throw error(500, 'Failed to delete purchase order');
	}

	return json({
		success: true,
		message: `Purchase order ${purchaseOrder.po_number} deleted successfully`,
	});
};
