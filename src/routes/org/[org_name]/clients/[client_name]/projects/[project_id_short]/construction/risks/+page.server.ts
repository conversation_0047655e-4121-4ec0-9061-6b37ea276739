import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { type Actions, fail } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { riskFilterSchema, riskItemSchema } from '$lib/schemas/risk';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals, cookies, url, depends }) => {
	depends('project:risks');

	await requireUser();

	const { supabase } = locals;
	const { org_name, client_name, project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Get filter parameters from URL
	const dateFromFilter = url.searchParams.get('date_from') || undefined;
	const dateToFilter = url.searchParams.get('date_to') || undefined;
	const wbsItemFilter = url.searchParams.get('wbs_item') || undefined;

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*')
		.eq('project_id', project_id)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		locals.log.error({ msg: 'Error fetching project:', projectError });
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Build query for risks
	let riskQuery = supabase
		.from('risk_register')
		.select(
			`
				*,
				wbs_item:wbs_library_item(wbs_library_item_id, code, description),
				risk_owner:profile(full_name, email)
			`,
		)
		.eq('project_id', projectData.project_id)
		.eq('status', 'risk')
		.order('date_identified', { ascending: false });

	// Apply filters
	if (dateFromFilter) {
		riskQuery = riskQuery.gte('date_identified', dateFromFilter);
	}

	if (dateToFilter) {
		riskQuery = riskQuery.lte('date_identified', dateToFilter);
	}

	// Fetch WBS items, risks, and project members in parallel
	const [{ data: wbsItems }, { data: risks, error: risksError }, { data: projectMembers }] =
		await Promise.all([
			supabase
				.from('wbs_library_item')
				.select('*')
				.or(
					`and(wbs_library_id.eq.${projectData.wbs_library_id},item_type.eq.Standard),and(client_id.eq.${projectData.client_id},item_type.eq.Custom,project_id.is.null),and(client_id.eq.${projectData.client_id},item_type.eq.Custom,project_id.eq.${projectData.project_id})`,
				)
				.order('code'),
			riskQuery,
			// TODO: fix rpc to use only project_id
			supabase.rpc('profiles_with_project_access', {
				_project_name: projectData.name,
				_client_name: client_name,
			}),
		]);

	if (risksError) {
		locals.log.error({ msg: 'Error fetching risks:', risksError });
	}

	let filteredRisks = risks;
	if (wbsItemFilter) {
		// Handle special cases
		if (wbsItemFilter === '__project_budget_root__') {
			// Skip filter and include all risks
			filteredRisks = risks;
		} else if (wbsItemFilter === '__unallocated_risks__') {
			// Include only risks that do not have an associated wbs code
			filteredRisks = risks
				? risks.filter((risk) => !risk.wbs_library_item_id || !risk.wbs_item)
				: null;
		} else {
			// Normal WBS filtering
			const filterCode = wbsItems?.find((item) => item.wbs_library_item_id === wbsItemFilter)?.code;
			if (filterCode) {
				filteredRisks = risks
					? risks.filter((risk) => {
							if (
								risk.wbs_item?.code === filterCode ||
								risk.wbs_item?.code.startsWith(`${filterCode}.`)
							) {
								return true;
							}
							return false;
						})
					: null;
			}
		}
	}

	// Create the form with the risk item schema
	const form = await superValidate({ project_id: projectData.project_id }, zod(riskItemSchema), {
		errors: false,
	});

	// Create the filter form (no status filter for risks page)
	const filterSchema = riskFilterSchema.pick({
		date_from: true,
		date_to: true,
		wbs_library_item_id: true,
	});

	const filterForm = await superValidate(
		{
			date_from: dateFromFilter,
			date_to: dateToFilter,
			wbs_library_item_id: wbsItemFilter,
		},
		zod(filterSchema),
	);

	return {
		title: `Risk Register - ${projectData.name} - ${client_name}`,
		project: projectData,
		risks: filteredRisks || [],
		wbsItems: wbsItems || [],
		form,
		filterForm,
		projectMembers: projectMembers || [],
	};
};

export const actions: Actions = {
	// Upsert a risk item
	upsertRisk: async ({ request, locals }) => {
		const { supabase } = locals;

		// Validate the form
		const form = await superValidate(request, zod(riskItemSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: form.data.project_id,
		});

		if (!canEdit) {
			return message(
				form,
				{ type: 'error', text: 'You do not have permission to edit this project' },
				{ status: 403 },
			);
		}

		// Prepare data for upsert
		const { risk_id, ...riskDataWithoutId } = form.data;

		// Perform upsert
		const { error: upsertError } = form.data.risk_id
			? await supabase
					.from('risk_register')
					.update(riskDataWithoutId)
					.eq('risk_id', form.data.risk_id)
			: await supabase.from('risk_register').insert(riskDataWithoutId);

		if (upsertError) {
			locals.log.error({ msg: 'Error upserting risk:', upsertError });
			return message(form, { type: 'error', text: upsertError.message });
		}

		return message(form, {
			type: 'success',
			text: `Risk ${form.data.risk_id ? 'updated' : 'created'} successfully`,
		});
	},

	// Delete a risk item
	deleteRisk: async ({ request, locals }) => {
		const { supabase } = locals;
		const formData = await request.formData();
		const riskId = formData.get('risk_id')?.toString();
		const projectId = formData.get('project_id')?.toString();

		if (!riskId || !projectId) {
			return fail(400, { message: 'Missing required fields' });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: projectId,
		});

		if (!canEdit) {
			return fail(403, { message: 'You do not have permission to delete this risk' });
		}

		// Delete the risk
		const { error: deleteError } = await supabase
			.from('risk_register')
			.delete()
			.eq('risk_id', riskId);

		if (deleteError) {
			locals.log.error({ msg: 'Error deleting risk:', deleteError });
			return fail(500, { message: deleteError.message });
		}

		return { success: true };
	},

	// Apply filters
	applyFilters: async ({ request }) => {
		const filterSchema = riskFilterSchema.pick({
			date_from: true,
			date_to: true,
			wbs_library_item_id: true,
		});
		const form = await superValidate(request, zod(filterSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Return the form data to be used for URL parameters
		return { filterForm: form };
	},
};
