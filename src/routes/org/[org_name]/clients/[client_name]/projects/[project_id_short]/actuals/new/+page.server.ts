import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { invoiceSchema } from '$lib/schemas/invoice';
import { vendorSchema } from '$lib/schemas/vendor';
import {
	createInvoice,
	createInvoiceModal,
} from '$lib/components/forms/invoice/invoice_form_actions';
import { createVendor, createVendorModal } from '$lib/components/forms/vendor/vendor_form_actions';
import { requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import type { Actions, PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals }) => {
	if (!locals.session) return error(401, 'Unauthorized');

	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Get vendors accessible for this project
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		entity_type_param: 'project',
		entity_id_param: project_id,
		user_id_param: locals.session.user.id,
	});

	if (vendorsError) {
		locals.log.error({ msg: 'Error fetching vendors', vendorsError });
		throw new Error('Failed to fetch vendors');
	}

	// Get purchase orders for this project
	const { data: purchaseOrders, error: purchaseOrdersError } = await supabase.rpc(
		'get_accessible_purchase_orders',
		{
			project_id_param: project_id,
		},
	);

	if (purchaseOrdersError) {
		locals.log.error({ msg: 'Error fetching purchase orders', purchaseOrdersError });
		throw new Error('Failed to fetch purchase orders');
	}

	// Initialize forms
	const form = await superValidate(zod(invoiceSchema));

	const newVendorForm = await superValidate(zod(vendorSchema));

	return {
		form,
		newVendorForm,
		vendors: vendors || [],
		purchaseOrders: purchaseOrders || [],
	};
};

export const actions: Actions = {
	createInvoice: async (event) => {
		const result = await createInvoice(event);

		if (result && 'form' in result && result.form.valid) {
			const { org_name, client_name, project_id_short } = requireProject();
			return redirect(
				303,
				`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/actuals`,
				{ type: 'success', message: 'Invoice created successfully' },
				event.cookies,
			);
		}

		return result;
	},
	createInvoiceModal,
	createVendor,
	createVendorModal,
};
