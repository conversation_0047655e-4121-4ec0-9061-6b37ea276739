import { editProjectSchema, projectUUID } from '$lib/schemas/project';
import { fail } from '@sveltejs/kit';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import type { Actions, PageServerLoad } from './$types';
import { redirect } from 'sveltekit-flash-message/server';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	const { supabase } = locals;
	const { project_id_short, client_name, org_name } = params;

	const project_id = projectUUID(project_id_short);

	// Combined query to fetch project with organization and client data in one query
	const { data: project, error } = await supabase
		.from('project')
		.select('*')
		.eq('project_id', project_id)
		.limit(1)
		.maybeSingle();

	if (error || !project) {
		locals.log.error({ msg: 'Error fetching project:', error });
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found.' },
			cookies,
		);
	}

	// Fetch available WBS libraries for the dropdown
	const { data: wbsLibraries } = await supabase
		.from('wbs_library')
		.select('wbs_library_id, name')
		.order('name');

	// Create a form using the edit project schema and pre-populate with project data
	const projectFormData = {
		name: project.name,
		description: project.description,
		wbs_library_id: project.wbs_library_id,
	};
	const form = await superValidate(projectFormData, zod(editProjectSchema));

	return {
		form,
		project,
		wbsLibraries: wbsLibraries || [],
	};
};

export const actions: Actions = {
	default: async ({ request, locals, params }) => {
		const { supabase } = locals;
		const { project_id_short } = params;

		const project_id = projectUUID(project_id_short);

		// Validate form data
		const form = await superValidate(request, zod(editProjectSchema));

		console.log({ form });

		if (!form.valid) {
			return fail(400, { form });
		}

		// Check if user has permission to edit this project - moved after main data fetching
		const { data: hasPermission, error: permissionError } = await supabase.rpc(
			'can_modify_project',
			{
				project_id_param: project_id,
			},
		);

		if (permissionError || !hasPermission) {
			locals.log.error({ msg: 'Permission error:', permissionError });
			return message(
				form,
				{ type: 'error', text: 'You do not have permission to edit this project.' },
				{ status: 403 },
			);
		}

		// Update the project
		const { error: updateError } = await supabase
			.from('project')
			.update(form.data)
			.eq('project_id', project_id);

		if (updateError) {
			locals.log.error({ msg: 'Error updating project:', updateError });
			return message(form, { type: 'error', text: 'Error updating project.' }, { status: 400 });
		}

		return message(form, { type: 'success', text: 'Project updated successfully.' });
	},
};
