import { redirect } from 'sveltekit-flash-message/server';
import { requireProject } from '$lib/server/auth';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { type Actions, fail } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { riskFilterSchema } from '$lib/schemas/risk';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals, cookies, url, depends }) => {
	depends('project:approved-changes');

	const { supabase } = locals;
	const { org_name, client_name, project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Get filter parameters from URL
	const dateFromFilter = url.searchParams.get('date_from') || undefined;
	const dateToFilter = url.searchParams.get('date_to') || undefined;
	const wbsItemFilter = url.searchParams.get('wbs_item') || undefined;

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*')
		.eq('project_id', project_id)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		locals.log.error({ msg: 'Error fetching project', projectError });
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Build query for approved changes from risk register
	let approvedChangesQuery = supabase
		.from('risk_register')
		.select(
			`
				*,
				wbs_item:wbs_library_item(wbs_library_item_id, code, description),
				risk_owner:profile!risk_owner_user_id(full_name, email)
      `,
		)
		.eq('project_id', project_id)
		.eq('status', 'approved')
		.order('date_identified', { ascending: false });

	if (dateFromFilter) {
		approvedChangesQuery = approvedChangesQuery.gte('date_identified', dateFromFilter);
	}

	if (dateToFilter) {
		approvedChangesQuery = approvedChangesQuery.lte('date_identified', dateToFilter);
	}

	// Fetch WBS items, approved changes, and budget data in parallel
	const [
		{ data: wbsItems },
		{ data: approvedChanges, error: approvedChangesError },
		{ data: budgetData },
	] = await Promise.all([
		supabase
			.from('wbs_library_item')
			.select('*')
			.or(
				`and(wbs_library_id.eq.${projectData.wbs_library_id},item_type.eq.Standard),and(client_id.eq.${projectData.client_id},item_type.eq.Custom,project_id.is.null),and(client_id.eq.${projectData.client_id},item_type.eq.Custom,project_id.eq.${project_id})`,
			)
			.order('code'),
		approvedChangesQuery,
		supabase
			.from('budget_line_item_current')
			.select('wbs_library_item_id, quantity, unit_rate')
			.eq('project_id', project_id),
	]);

	if (approvedChangesError) {
		locals.log.error({ msg: 'Error fetching approved changes', approvedChangesError });
	}

	let filteredApprovedChanges = approvedChanges;
	if (wbsItemFilter) {
		// Handle special cases
		if (wbsItemFilter === '__project_budget_root__') {
			// Skip filter and include all risks
			filteredApprovedChanges = approvedChanges;
		} else if (wbsItemFilter === '__unallocated_risks__') {
			// Include only risks that do not have an associated wbs code
			filteredApprovedChanges = approvedChanges
				? approvedChanges.filter((change) => !change.wbs_library_item_id || !change.wbs_item)
				: null;
		} else {
			// Normal WBS filtering
			const filterCode = wbsItems?.find((item) => item.wbs_library_item_id === wbsItemFilter)?.code;
			if (filterCode) {
				filteredApprovedChanges = approvedChanges
					? approvedChanges.filter((change) => {
							if (
								change.wbs_item?.code === filterCode ||
								change.wbs_item?.code.startsWith(`${filterCode}.`)
							) {
								return true;
							}
							return false;
						})
					: null;
			}
		}
	}

	// Create a map of WBS item to total budget amount
	const budgetMap = new Map<string, number>();
	if (budgetData) {
		budgetData.forEach((item) => {
			const currentTotal = budgetMap.get(item.wbs_library_item_id) || 0;
			budgetMap.set(item.wbs_library_item_id, currentTotal + item.quantity * item.unit_rate);
		});
	}

	// Create the filter form
	const filterSchema = riskFilterSchema.pick({
		date_from: true,
		date_to: true,
		wbs_library_item_id: true,
	});

	const filterForm = await superValidate(
		{
			date_from: dateFromFilter,
			date_to: dateToFilter,
			wbs_library_item_id: wbsItemFilter,
		},
		zod(filterSchema),
	);

	return {
		title: `Approved Changes - ${projectData.name} - ${client_name}`,
		project: projectData,
		approvedChanges: filteredApprovedChanges || [],
		wbsItems: wbsItems || [],
		budgetMap: Object.fromEntries(budgetMap),
		filterForm,
		filters: {
			date_from: dateFromFilter,
			date_to: dateToFilter,
			wbs_item: wbsItemFilter,
		},
	};
};

export const actions: Actions = {
	// Apply filters
	applyFilters: async ({ request }) => {
		const filterSchema = riskFilterSchema.pick({
			date_from: true,
			date_to: true,
			wbs_library_item_id: true,
		});
		const form = await superValidate(request, zod(filterSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Return the form data to be used for URL parameters
		return { filterForm: form };
	},
};
