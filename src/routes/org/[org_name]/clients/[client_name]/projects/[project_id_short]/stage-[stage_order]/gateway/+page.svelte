<script lang="ts">
	import type { PageProps } from './$types';
	import * as Form from '$lib/components/ui/form';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Collapsible from '$lib/components/ui/collapsible';
	import { superForm } from 'sveltekit-superforms';
	import { toast } from 'svelte-sonner';
	import { Constants } from '$lib/database.types';
	import { uid } from 'uid';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import TrashIcon from 'phosphor-svelte/lib/Trash';
	import CaretDownIcon from 'phosphor-svelte/lib/CaretDown';
	import CaretRightIcon from 'phosphor-svelte/lib/CaretRight';

	const { data }: PageProps = $props();
	const currentStage = $derived(data.currentStage);
	const checklistItems = $derived(data.checklistItems);
	const canEditGateway = $derived(
		data.canEditProject && data.currentStage?.date_completed === null,
	);
	const readOnly = $derived(!canEditGateway);

	// Initialize Superforms with validation
	const gatewayStageInfoSuperForm = superForm(data.gatewayStageInfoForm, {
		resetForm: false,
		dataType: 'json',
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: gatewayStageInfoForm, enhance: gatewayStageInfoEnhance } =
		gatewayStageInfoSuperForm;

	const checklistSuperForm = superForm(data.checklistForm, {
		dataType: 'json',
		resetForm: false,
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: checklistForm, enhance: checklistEnhance } = checklistSuperForm;

	const qualitativeScorecardSuperForm = superForm(data.qualitativeScorecardForm, {
		dataType: 'json',
		resetForm: false,
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: qualitativeScorecardForm, enhance: qualitativeScorecardEnhance } =
		qualitativeScorecardSuperForm;

	// Initialize stage readiness check form
	const stageReadinessSuperForm = superForm(data.stageReadinessForm, {
		onResult({ result }) {
			if ('data' in result) {
				if (result.type === 'success') {
					toast.success(result.data?.message.text);
					stageReadyForCompletion = true;
				} else if (result.data?.message.type === 'error') {
					toast.error(result.data?.message.text);
					stageReadyForCompletion = false;
				}
			} else {
				stageReadyForCompletion = false;
				toast.info('Please check all checklist items and scoring before completing the stage.');
			}
		},
	});

	const { enhance: stageReadinessEnhance } = stageReadinessSuperForm;

	// Initialize stage completion form
	const stageCompletionSuperForm = superForm(data.stageCompletionForm, {
		dataType: 'json',
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: stageCompletionFormData, enhance: stageCompletionEnhance } =
		stageCompletionSuperForm;

	// Create reactive state variables
	let stageCompletionInProgress = $state(false);
	let stageReadyForCompletion = $state(false);

	// Collapsible section state - only "DETAILS OF BUILDING" is expanded by default
	let buildingDetailsExpanded = $state(true);
	let qualitativeScorecardExpanded = $state(false);
	let checklistExpanded = $state(false);

	// Function to add a new name-value pair
	function addAdditionalDataPair() {
		$gatewayStageInfoForm.additional_data = $gatewayStageInfoForm.additional_data
			? [...$gatewayStageInfoForm.additional_data, [uid(), '', '']]
			: [[uid(), '', '']];
	}

	// Function to remove a name-value pair
	function removeAdditionalDataPair(removeId: string) {
		$gatewayStageInfoForm.additional_data = $gatewayStageInfoForm.additional_data
			? $gatewayStageInfoForm.additional_data.filter(([id, ,]) => id !== removeId)
			: [];
		if ($gatewayStageInfoForm.additional_data.length === 0) {
			addAdditionalDataPair();
		}
	}

	// Qualitative Scorecard Functions
	function addCriterion() {
		if (!$qualitativeScorecardForm.scorecard || !$qualitativeScorecardForm.scorecard.length) {
			// Initialize with default structure if empty
			$qualitativeScorecardForm.scorecard = [
				['Dimension Name', 'Criterion 1', 'New Criterion'],
				['Technical specifications', null, null],
			];
			return;
		}

		// Add a new criterion column to each row
		const newCriterionName = `Criterion ${$qualitativeScorecardForm.scorecard[0].length}`;

		$qualitativeScorecardForm.scorecard = $qualitativeScorecardForm.scorecard.map((row, index) => {
			if (index === 0) {
				// Add the new criterion name to the header row
				return [...row, newCriterionName];
			} else {
				// Add null score for each dimension row
				return [...row, null];
			}
		});
	}

	function addDimension() {
		if (!$qualitativeScorecardForm.scorecard || !$qualitativeScorecardForm.scorecard.length) {
			// Initialize with default structure if empty
			$qualitativeScorecardForm.scorecard = [
				['Dimension Name', 'Criterion 1', 'Criterion 2', 'Criterion 3'],
				['New Dimension', null, null, null],
			];
			return;
		}

		// Create a new dimension row with null scores for each criterion
		const headerRow = $qualitativeScorecardForm.scorecard[0];
		const newRow: (string | number | null)[] = ['New Dimension'];

		// Add null scores for each criterion
		for (let i = 1; i < headerRow.length; i++) {
			newRow.push(null);
		}

		$qualitativeScorecardForm.scorecard = [...$qualitativeScorecardForm.scorecard, newRow];
	}

	function removeCriterion(index: number) {
		if (
			!$qualitativeScorecardForm.scorecard ||
			!$qualitativeScorecardForm.scorecard.length ||
			index === 0
		) {
			return; // Don't remove the dimension name column
		}

		// Remove the criterion column from each row
		$qualitativeScorecardForm.scorecard = $qualitativeScorecardForm.scorecard.map((row) => {
			return [...row.slice(0, index), ...row.slice(index + 1)];
		});
	}

	function removeDimension(index: number) {
		if (
			!$qualitativeScorecardForm.scorecard ||
			!$qualitativeScorecardForm.scorecard.length ||
			index === 0
		) {
			return; // Don't remove the header row
		}

		// Remove the dimension row
		$qualitativeScorecardForm.scorecard = [
			...$qualitativeScorecardForm.scorecard.slice(0, index),
			...$qualitativeScorecardForm.scorecard.slice(index + 1),
		];
	}

	// These functions are now handled by server actions
</script>

<div class="max-w-(--breakpoint-xl) p-8">
	<div class="mb-8">
		<h1 class="text-3xl font-bold">Stage Gateway - {currentStage?.name || 'Unknown Stage'}</h1>
		<p class="mt-1 text-slate-600">
			Complete checklist items and scoring to proceed to the next stage
		</p>
		{#if readOnly}
			<p class="mt-2 text-sm text-slate-600">This stage is completed and is therefore read-only</p>
		{/if}
	</div>

	<div class="grid grid-cols-1 gap-8">
		<!-- Building Details -->
		<section class="rounded-lg border bg-white shadow-xs">
			<Collapsible.Root bind:open={buildingDetailsExpanded} class="group/collapsible">
				<Collapsible.Trigger
					class="flex w-full items-center justify-between p-6 transition-colors hover:bg-slate-50"
				>
					<div>
						<h2 class="text-left text-xl font-semibold">DETAILS OF BUILDING</h2>
						<p class="mt-1 text-sm text-slate-600">
							Provide information about the building specifications.
						</p>
					</div>
					<div class="ml-4 flex-shrink-0">
						{#if buildingDetailsExpanded}
							<CaretDownIcon class="size-5 text-slate-500 transition-transform duration-200" />
						{:else}
							<CaretRightIcon class="size-5 text-slate-500 transition-transform duration-200" />
						{/if}
					</div>
				</Collapsible.Trigger>
				<Collapsible.Content class="px-6 pb-6"
					><div class="border-t pt-6">
						<form method="POST" action="?/updateGatewayStageInfo" use:gatewayStageInfoEnhance>
							<input type="hidden" name="project_stage_id" value={currentStage?.project_stage_id} />
							<fieldset class="space-y-6" disabled={readOnly}>
								<!-- Floor Areas Section -->
								<section class="rounded-lg border">
									<h3 class="bg-slate-100 px-4 py-2 font-medium">Floor Areas</h3>
									<div class="p-4">
										<div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
											<!-- Left column -->
											<div class="space-y-4">
												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="basement_floors"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Basement floors</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.basement_floors}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="ground_floor"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Ground floor</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.ground_floor}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="upper_floors"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Upper floors</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.upper_floors}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="total_gross_internal_floor_area"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Total (Gross Internal Floor Area)</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.total_gross_internal_floor_area}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="spaces_not_enclosed"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Spaces not enclosed</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.spaces_not_enclosed}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="internal_cube"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Internal cube</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.internal_cube}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m³</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="site_area"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Site area</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.site_area}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>
											</div>

											<!-- Middle column -->
											<div class="space-y-4">
												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="usable_area"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Usable Area</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.usable_area}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="circulation_area"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Circulation Area</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.circulation_area}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="ancillary_areas"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Ancillary Areas</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.ancillary_areas}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="internal_divisions"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Internal Divisions</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.internal_divisions}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="total_gross_internal_floor_area_2"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Total (Gross Internal Floor Area)</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={
																		$gatewayStageInfoForm.total_gross_internal_floor_area_2
																	}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="area_of_lowest_floor"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Area of lowest floor</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.area_of_lowest_floor}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="number_of_units"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Number of units</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.number_of_units}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>
											</div>
										</div>
									</div>
								</section>

								<!-- Storeys Section -->
								<section class="rounded-lg border">
									<h3 class="bg-slate-100 px-4 py-2 font-medium">Storeys</h3>
									<div class="p-4">
										<div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
											<!-- Left column -->
											<div class="space-y-4">
												<div class="flex items-center">
													<h2>Nr of storeys</h2>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="nr_of_storeys_primary"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Nr of storeys (Primary)</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.nr_of_storeys_primary}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">Nr</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="nr_of_storeys_secondary"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Nr of storeys (Secondary)</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.nr_of_storeys_secondary}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">Nr</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="basement_storeys_included_above"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Basement storeys included above</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.basement_storeys_included_above}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">Nr</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="external_vertical_envelope"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label
																	>External vertical envelope: (Ext. walls, windows & doors)</Form.Label
																>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.external_vertical_envelope}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m²</span>
												</div>
											</div>

											<!-- Right column -->
											<div class="space-y-4">
												<div class="flex items-center">
													<h2>Average storey height</h2>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="below_ground_floors"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Below ground floor(s)</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.below_ground_floors}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="ground_floor_height"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Ground floor</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.ground_floor_height}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m</span>
												</div>

												<div class="flex items-center">
													<Form.Field
														form={gatewayStageInfoSuperForm}
														name="above_ground_floors"
														class="flex-1"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Form.Label>Above ground floor(s)</Form.Label>
																<Input
																	{...props}
																	type="number"
																	step="0.01"
																	bind:value={$gatewayStageInfoForm.above_ground_floors}
																/>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<span class="mt-6 ml-2 w-8">m</span>
												</div>
											</div>
										</div>
									</div>
								</section>

								<!-- Additional Data Section -->
								<section class="rounded-lg border">
									<h3 class="bg-slate-100 px-4 py-2 font-medium">Additional Data</h3>
									<div class="p-4">
										<div class="mb-4">
											<p class="text-sm text-slate-600">
												Add custom name-value pairs for additional building information.
											</p>
										</div>

										<!-- Dynamic Additional Data Fields -->
										<div class="space-y-4">
											{#each $gatewayStageInfoForm.additional_data ?? [] as [id], i (id)}
												<div class="flex items-center gap-8">
													<div class="flex-1">
														<label
															for={`additional-name-${id}`}
															class="mb-2 block text-sm font-medium">Name</label
														>
														<Input
															id={`additional-name-${id}`}
															type="text"
															placeholder="Field name"
															bind:value={$gatewayStageInfoForm.additional_data![i][1]}
														/>
													</div>
													<div class="flex-1">
														<label
															for={`additional-value-${id}`}
															class="mb-2 block text-sm font-medium">Value</label
														>
														<Input
															id={`additional-value-${id}`}
															type="text"
															placeholder="Field value"
															bind:value={$gatewayStageInfoForm.additional_data![i][2]}
														/>
													</div>
													<div class="mt-6">
														<Dialog.Root>
															<Dialog.Trigger type="button">
																<Button
																	class="border-red-500/50 text-red-500/50 hover:bg-red-500 hover:text-white"
																	type="button"
																	variant="outline"
																	size="icon"
																>
																	<TrashIcon />
																</Button>
															</Dialog.Trigger>
															<Dialog.Content class="sm:max-w-[425px]">
																<Dialog.Header>
																	<Dialog.Title>Remove Field</Dialog.Title>
																	<Dialog.Description>
																		Are you sure you want to remove this additional data field?
																	</Dialog.Description>
																</Dialog.Header>
																<div class="py-4">
																	<p class="text-muted-foreground text-sm">
																		This action will delete this field and its value from the
																		building details.
																	</p>
																</div>
																<Dialog.Footer>
																	<Dialog.Close>
																		<Button variant="outline" class="mr-2">Cancel</Button>
																	</Dialog.Close>
																	<Button
																		variant="destructive"
																		onclick={() => removeAdditionalDataPair(id)}
																	>
																		Remove Field
																	</Button>
																</Dialog.Footer>
															</Dialog.Content>
														</Dialog.Root>
													</div>
												</div>
											{/each}

											{#if !readOnly}
												<div class="flex justify-end">
													<Button
														type="button"
														variant="outline"
														onclick={addAdditionalDataPair}
														class="mt-2"
													>
														<PlusIcon />
														Add Field
													</Button>
												</div>
											{/if}
										</div>

										<!-- Hidden field to store the JSON data -->
										<input
											type="hidden"
											name="additional_data"
											bind:value={$gatewayStageInfoForm.additional_data}
										/>
									</div>
								</section>

								{#if !readOnly}
									<div class="flex justify-end">
										<Button type="submit" disabled={readOnly}>Save Building Details</Button>
									</div>
								{/if}
							</fieldset>
						</form>
					</div>
				</Collapsible.Content>
			</Collapsible.Root>
		</section>

		<!-- Qualitative Scorecard -->
		<section class="rounded-lg border bg-white shadow-xs">
			<Collapsible.Root bind:open={qualitativeScorecardExpanded} class="group/collapsible">
				<Collapsible.Trigger
					class="flex w-full items-center justify-between p-6 transition-colors hover:bg-slate-50"
				>
					<div>
						<h2 class="text-left text-xl font-semibold">Gateway Qualitative Scorecard</h2>
						<p class="mt-1 text-sm text-slate-600">
							Evaluate the project on these key dimensions and criteria before proceeding to the
							next stage.
						</p>
					</div>
					<div class="ml-4 flex-shrink-0">
						{#if qualitativeScorecardExpanded}
							<CaretDownIcon class="size-5 text-slate-500 transition-transform duration-200" />
						{:else}
							<CaretRightIcon class="size-5 text-slate-500 transition-transform duration-200" />
						{/if}
					</div>
				</Collapsible.Trigger>
				<Collapsible.Content class="px-6 pb-6"
					><div class="border-t pt-6">
						<form
							method="POST"
							action="?/updateQualitativeScorecard"
							class="space-y-6"
							use:qualitativeScorecardEnhance
						>
							<input type="hidden" name="project_stage_id" value={currentStage?.project_stage_id} />
							<fieldset disabled={readOnly}>
								{#if !$qualitativeScorecardForm.scorecard || !$qualitativeScorecardForm.scorecard.length}
									<div class="rounded-lg border bg-slate-50 p-8 text-center">
										<p class="text-slate-600">
											No scorecard data available. Click below to initialize.
										</p>
										<Button
											variant="outline"
											class="mt-4"
											type="button"
											onclick={() => {
												$qualitativeScorecardForm.scorecard = [
													['Dimension Name', 'Criterion 1', 'Criterion 2', 'Criterion 3'],
													['Technical Feasibility', null, null, null],
												];
											}}
										>
											Initialize Scorecard
										</Button>
									</div>
								{:else}
									<div class="overflow-x-auto">
										<!-- TODO: instead of max-content, figure out how to wrap at some appropriate max width -->
										<div
											class="grid"
											style="grid-template-columns: minmax(max-content, 20rem) repeat({$qualitativeScorecardForm
												.scorecard[0].length}, minmax(10rem, 1fr));"
										>
											<!-- Header Row -->
											{#each $qualitativeScorecardForm.scorecard[0] as header, colIndex (colIndex)}
												<div class="flex items-center border bg-slate-100 p-2 font-medium">
													{#if colIndex === 0}
														<div class="flex justify-start">{header}</div>
													{:else}
														<div class="flex items-center justify-between">
															<Input
																type="text"
																class="border-0 bg-transparent p-0 text-center"
																bind:value={$qualitativeScorecardForm.scorecard[0][colIndex]}
															/>
															<Button
																variant="ghost"
																size="icon"
																class="size-6 text-red-500 hover:bg-red-50"
																type="button"
																onclick={() => removeCriterion(colIndex)}
															>
																<TrashIcon class="size-4" />
															</Button>
														</div>
													{/if}
												</div>
											{/each}
											<div class="flex items-center justify-center border">
												<Button variant="outline" size="sm" type="button" onclick={addCriterion}>
													<PlusIcon class="mr-1 size-4" /> Add Criterion
												</Button>
											</div>

											<!-- Data Rows -->
											{#each $qualitativeScorecardForm.scorecard.slice(1) as row, rowIndex (rowIndex)}
												<!-- eslint-disable-next-line @typescript-eslint/no-unused-vars -->
												{#each row as _, colIndex (rowIndex + '-' + colIndex)}
													<div class="border p-2">
														{#if colIndex === 0}
															<!-- Dimension Name -->
															<div class="flex items-center justify-between">
																<Input
																	type="text"
																	class="border-0 bg-transparent p-0 font-medium"
																	bind:value={
																		$qualitativeScorecardForm.scorecard[rowIndex + 1][colIndex]
																	}
																/>
																<Button
																	variant="ghost"
																	size="icon"
																	class="size-6 p-1 text-red-500 hover:bg-red-50"
																	type="button"
																	onclick={() => removeDimension(rowIndex + 1)}
																>
																	<TrashIcon class="size-4" />
																</Button>
															</div>
														{:else}
															<!-- Score Cell -->
															<Input
																type="number"
																min="0"
																max="10"
																step="0.1"
																placeholder="0-10"
																class="text-center"
																bind:value={
																	$qualitativeScorecardForm.scorecard[rowIndex + 1][colIndex]
																}
															/>
														{/if}
													</div>
												{/each}
												<div class="border"></div>
											{/each}
											<div class="col-span-1 border p-4">
												<Button variant="outline" size="sm" type="button" onclick={addDimension}>
													<PlusIcon class="mr-1 size-4" /> Add Dimension
												</Button>
											</div>
											{#each { length: $qualitativeScorecardForm.scorecard[0].length }}
												<div class="border"></div>
											{/each}
										</div>
									</div>

									<div class="mt-4 flex justify-end">
										<Button type="submit" disabled={readOnly}>Save Scorecard</Button>
									</div>
								{/if}
							</fieldset>
						</form>
					</div>
				</Collapsible.Content>
			</Collapsible.Root>
		</section>

		<!-- Checklist Items -->
		<section class="rounded-lg border bg-white shadow-xs">
			<Collapsible.Root bind:open={checklistExpanded} class="group/collapsible">
				<Collapsible.Trigger
					class="flex w-full items-center justify-between p-6 transition-colors hover:bg-slate-50"
				>
					<div>
						<h2 class="text-left text-xl font-semibold">Gateway Checklist</h2>
						<p class="mt-1 text-sm text-slate-600">
							All items must be marked as completed or deferred before proceeding to the next stage.
						</p>
					</div>
					<div class="ml-4 flex-shrink-0">
						{#if checklistExpanded}
							<CaretDownIcon class="size-5 text-slate-500 transition-transform duration-200" />
						{:else}
							<CaretRightIcon class="size-5 text-slate-500 transition-transform duration-200" />
						{/if}
					</div>
				</Collapsible.Trigger>
				<Collapsible.Content class="px-6 pb-6"
					><div class="border-t pt-6">
						{#if canEditGateway}
							<div class="mb-4 flex justify-end">
								<a
									href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
										data.client_name,
									)}/projects/{encodeURIComponent(
										data.project_id_short,
									)}/stage-{currentStage?.stage_order}/gateway/checklist"
								>
									<Button variant="outline" size="sm">Manage Checklist Items</Button>
								</a>
							</div>
						{/if}

						{#if checklistItems.length === 0}
							<div class="rounded-lg border bg-slate-50 p-8 text-center">
								<p class="text-slate-600">No checklist items defined for this stage.</p>
								{#if canEditGateway}
									<a
										href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
											data.client_name,
										)}/projects/{encodeURIComponent(
											data.project_id_short,
										)}/stage-{currentStage?.stage_order}/gateway/checklist/new"
									>
										<Button variant="outline" class="mt-4">Add Checklist Item</Button>
									</a>
								{/if}
							</div>
						{:else}
							<form method="POST" action="?/updateChecklist" class="space-y-4" use:checklistEnhance>
								<fieldset disabled={readOnly}>
									<!-- {#if $checklistErrors?.items}
						<Form.Message class="text-red-500">{$checklistErrors.items}</Form.Message>
					{/if} -->

									{#each checklistItems as item, i (item.gateway_checklist_item_id)}
										<div class="rounded border bg-gray-50 p-4">
											<div class="flex items-start justify-between">
												<div>
													<h3 class="font-medium">{item.name}</h3>
													{#if item.description}
														<p class="mt-1 text-sm text-gray-600">{item.description}</p>
													{/if}
												</div>
												<div class="flex space-x-2">
													<Form.Field
														class="w-40"
														form={checklistSuperForm}
														name="items[{i}].status"
													>
														<Form.Control>
															{#snippet children({ props })}
																<Select.Root
																	type="single"
																	bind:value={$checklistForm.items[i].status}
																	name={props.name}
																>
																	<Select.Trigger {...props} class="w-full pl-9">
																		{$checklistForm.items[i].status}
																	</Select.Trigger>
																	<Select.Content>
																		{#each Constants.public.Enums.checklist_item_status as status (status)}
																			<Select.Item value={status} label={status} />
																		{/each}
																	</Select.Content>
																</Select.Root>
															{/snippet}
														</Form.Control>
														<Form.FieldErrors />
													</Form.Field>
													<input
														type="hidden"
														name="items[{i}].gateway_checklist_item_id"
														value={item.gateway_checklist_item_id}
													/>
												</div>
											</div>
										</div>
									{/each}

									<div class="flex justify-end">
										<Button type="submit" disabled={readOnly}>Save Checklist Progress</Button>
									</div>
								</fieldset>
							</form>
						{/if}
					</div>
				</Collapsible.Content>
			</Collapsible.Root>
		</section>
	</div>

	{#if canEditGateway}
		<!-- Stage Completion Section -->
		<div class="mt-8 rounded-lg border bg-white p-6 shadow-xs">
			<h2 class="mb-4 text-xl font-semibold">Complete Stage Gateway</h2>
			<p class="mb-6 text-slate-600">
				When all checklist items are completed or deferred and scoring is finalized, you can
				complete this stage and proceed to the next. This will create a snapshot of the current
				budget.
			</p>

			<!-- Stage Readiness Check Form -->
			<form method="POST" action="?/checkStageReadiness" class="mb-6" use:stageReadinessEnhance>
				<input type="hidden" name="project_stage_id" value={currentStage?.project_stage_id} />

				<div class="flex">
					<Button type="submit" variant="outline" disabled={stageCompletionInProgress}>
						Check Completion Readiness
					</Button>
				</div>
			</form>

			<!-- Stage Completion Form -->
			<form method="POST" action="?/completeStage" class="space-y-4" use:stageCompletionEnhance>
				<input type="hidden" name="project_stage_id" value={currentStage?.project_stage_id} />

				<div class="mb-4">
					<Form.Field form={stageCompletionSuperForm} name="notes">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Stage Completion Notes</Form.Label>
								<Textarea
									{...props}
									rows={4}
									bind:value={$stageCompletionFormData.notes}
									placeholder="Add any notes about this stage completion..."
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div>
					<Button
						type="submit"
						disabled={!stageReadyForCompletion || stageCompletionInProgress}
						class="bg-blue-600 text-white hover:bg-blue-700 disabled:text-black"
					>
						Mark Stage as Complete
					</Button>
				</div>
			</form>
		</div>
	{/if}
</div>
