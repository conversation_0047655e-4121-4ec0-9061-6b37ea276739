import type { Actions, PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import { error, fail } from '@sveltejs/kit';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals }) => {
	await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch work packages using the RPC function
	const { data: workPackages, error: workPackagesError } = await supabase.rpc(
		'get_accessible_work_packages',
		{
			project_id_param: project_id,
		},
	);

	if (workPackagesError) {
		locals.log.error({ msg: 'Error fetching work packages:', workPackagesError });
		throw error(500, 'Failed to fetch work packages');
	}

	return {
		workPackages: workPackages || [],
	};
};

export const actions: Actions = {
	delete: async ({ request, locals, cookies }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_id_short } = requireProject();

		const formData = await request.formData();
		const workPackageId = formData.get('work_package_id') as string;

		if (!workPackageId) {
			return fail(400, {
				message: { type: 'error', text: 'Work package ID is required' },
			});
		}

		// Get work package name for success message
		const { data: workPackageData, error: workPackageError } = await supabase
			.from('work_package')
			.select('name')
			.eq('work_package_id', workPackageId)
			.single();

		if (workPackageError || !workPackageData) {
			locals.log.error({ msg: 'Error fetching work package for deletion:', workPackageError });
			return fail(404, {
				message: { type: 'error', text: 'Work package not found' },
			});
		}

		// Delete the work package
		const { error: deleteError } = await supabase
			.from('work_package')
			.delete()
			.eq('work_package_id', workPackageId);

		if (deleteError) {
			locals.log.error({ msg: 'Error deleting work package:', deleteError });
			return fail(500, {
				message: { type: 'error', text: 'Failed to delete work package' },
			});
		}

		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
				client_name,
			)}/projects/${encodeURIComponent(project_id_short)}/work-package`,
			{
				type: 'success',
				message: `Work package "${workPackageData.name}" deleted successfully`,
			},
			cookies,
		);
	},
};
