import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { type Actions, fail } from '@sveltejs/kit';
import { redirect } from 'sveltekit-flash-message/server';
import { gatewayChecklistItemSchema } from '$lib/schemas/gateway-checklist';
import { requireProject } from '$lib/server/auth';
import { upsertGatewayChecklistItem } from '$lib/project_utils';
import { projectUUID } from '$lib/schemas/project';

export async function load({ locals, params, cookies }) {
	const { supabase } = locals;
	const { org_name, client_name, project_id_short, stage_order } = params;

	requireProject();

	const project_id = projectUUID(project_id_short);

	// Get the project with project stages
	const { data: project, error: projectError } = await supabase
		.from('project')
		.select('*, project_stage!project_stage_project_id_fkey(*)')
		.eq('project_id', project_id)
		.eq('project_stage.stage_order', Number(stage_order))
		.limit(1)
		.maybeSingle();

	if (projectError || !project) {
		locals.log.error({ msg: 'Error fetching project:', projectError });
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Find the current active stage (first incomplete stage)
	const allStages = Array.isArray(project.project_stage)
		? project.project_stage
		: project.project_stage
			? [project.project_stage]
			: [];
	const currentStage = allStages.find((stage) => !stage.date_completed) || allStages[0];

	if (!currentStage) {
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}`,
			{ type: 'error', message: 'No active project stage found' },
			cookies,
		);
	}

	// Create a form with the gateway checklist item schema
	const form = await superValidate(zod(gatewayChecklistItemSchema));

	return {
		project,
		currentStage,
		form,
	};
}

export const actions: Actions = {
	default: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const { org_name, client_name, project_id_short } = requireProject();

		const project_id = projectUUID(project_id_short);

		// Get form data
		const form = await superValidate(request, zod(gatewayChecklistItemSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { data: project } = await supabase
			.from('project')
			.select('*')
			.eq('project_id', project_id)
			.limit(1)
			.maybeSingle();

		if (!project) {
			return fail(404, { form, message: { type: 'error', text: 'Project not found' } });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: project.project_id,
		});

		if (!canEdit) {
			return fail(403, {
				form,
				message: { type: 'error', text: 'You do not have permission to edit this project' },
			});
		}

		// Create the new checklist item with initial 'Incomplete' status
		const { data: upsertData, error: upsertError } = await upsertGatewayChecklistItem(
			supabase,
			{
				project_stage_id: form.data.project_stage_id,
				name: form.data.name,
				description: form.data.description,
			},
			'Incomplete', // Set initial status explicitly
		);

		console.log({ upsertData });
		if (upsertError) {
			locals.log.error({ msg: 'Error creating checklist item:', upsertError });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create checklist item' },
			});
		}

		// Redirect back to the checklist page with a success message
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/stage-${encodeURIComponent(params.stage_order!)}/gateway/checklist`,
			{ type: 'success', message: 'Checklist item created successfully' },
			cookies,
		);
	},
};
