<script lang="ts">
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import * as Dialog from '$lib/components/ui/dialog';
	import PencilSimpleIcon from 'phosphor-svelte/lib/PencilSimple';
	import TrashIcon from 'phosphor-svelte/lib/Trash';
	import { goto } from '$app/navigation';
	import { enhance } from '$app/forms';

	const { data }: PageProps = $props();

	const workPackage = $derived(data.workPackage);
	const purchaseOrders = $derived(data.purchaseOrders);

	let deleteDialogOpen = $state(false);

	function handleEdit() {
		goto(
			`/org/${encodeURIComponent(data.org_name)}/clients/${encodeURIComponent(
				data.client_name,
			)}/projects/${encodeURIComponent(data.project_id_short)}/work-package/${workPackage.work_package_id}/edit`,
		);
	}

	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('en-GB', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	}
</script>

<div class="container mx-auto py-8">
	<div class="mb-6 flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-semibold">{workPackage.name}</h1>
			<p class="text-muted-foreground mt-1">
				Work package in {data.project.name}
			</p>
		</div>
		<div class="flex gap-2">
			<Button onclick={handleEdit} class="gap-2">
				<PencilSimpleIcon class="size-4" />
				Edit
			</Button>
			<Button variant="destructive" onclick={() => (deleteDialogOpen = true)} class="gap-2">
				<TrashIcon class="size-4" />
				Delete
			</Button>
		</div>
	</div>

	<div class="grid gap-6">
		<!-- Basic Information -->
		<Card.Root>
			<Card.Header>
				<Card.Title>Basic Information</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div>
					<h4 class="text-muted-foreground text-sm font-medium">Name</h4>
					<p class="text-sm">{workPackage.name}</p>
				</div>
				{#if workPackage.description}
					<div>
						<h4 class="text-muted-foreground text-sm font-medium">Description</h4>
						<p class="text-sm">{workPackage.description}</p>
					</div>
				{/if}
				<div>
					<h4 class="text-muted-foreground text-sm font-medium">Created</h4>
					<p class="text-sm">{formatDate(workPackage.created_at)}</p>
				</div>
			</Card.Content>
		</Card.Root>

		<!-- WBS Information -->
		<Card.Root>
			<Card.Header>
				<Card.Title>WBS Library Item</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div>
					<h4 class="text-muted-foreground text-sm font-medium">Code</h4>
					<p class="font-mono text-sm">{workPackage.wbs_library_item.code}</p>
				</div>
				<div>
					<h4 class="text-muted-foreground text-sm font-medium">Description</h4>
					<p class="text-sm">{workPackage.wbs_library_item.description}</p>
				</div>
				{#if workPackage.wbs_library_item.cost_scope}
					<div>
						<h4 class="text-muted-foreground text-sm font-medium">Cost Scope</h4>
						<p class="text-sm">{workPackage.wbs_library_item.cost_scope}</p>
					</div>
				{/if}
			</Card.Content>
		</Card.Root>

		<!-- Relationships -->
		<Card.Root>
			<Card.Header>
				<Card.Title>Relationships</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div>
					<h4 class="text-muted-foreground text-sm font-medium">Purchase Orders</h4>
					{#if purchaseOrders.length > 0}
						<div class="space-y-3">
							{#each purchaseOrders as purchaseOrder (purchaseOrder.purchase_order_id)}
								<div class="border-muted border-l-2 pl-3 text-sm">
									<p class="font-medium">{purchaseOrder.po_number}</p>
									{#if purchaseOrder.vendor}
										<p class="text-muted-foreground">
											Vendor: {purchaseOrder.vendor.name}
										</p>
									{/if}
									{#if purchaseOrder.description}
										<p class="text-muted-foreground">{purchaseOrder.description}</p>
									{/if}
									{#if purchaseOrder.original_amount || purchaseOrder.co_amount}
										<p class="text-muted-foreground">
											Amount: {purchaseOrder.original_amount || 0}
											{#if purchaseOrder.co_amount}+ {purchaseOrder.co_amount} CO{/if}
										</p>
									{/if}
								</div>
							{/each}
						</div>
					{:else}
						<p class="text-muted-foreground text-sm">No purchase orders assigned</p>
					{/if}
				</div>
			</Card.Content>
		</Card.Root>
	</div>
</div>

<!-- Delete Confirmation Dialog -->
<Dialog.Root bind:open={deleteDialogOpen}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Delete Work Package</Dialog.Title>
			<Dialog.Description>
				Are you sure you want to delete "{workPackage.name}"? This action cannot be undone.
			</Dialog.Description>
		</Dialog.Header>
		<Dialog.Footer>
			<Button variant="outline" onclick={() => (deleteDialogOpen = false)}>Cancel</Button>
			<form method="POST" action="?/delete" use:enhance>
				<Button type="submit" variant="destructive">Delete</Button>
			</form>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
