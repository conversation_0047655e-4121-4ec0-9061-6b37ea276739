import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ params, locals, cookies, depends }) => {
	depends('project:budget-snapshot');

	await requireUser();

	const { supabase } = locals;
	const { org_name, client_name, project_id_short } = requireProject();
	const { stage_order } = params;

	const project_id = projectUUID(project_id_short);

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*')
		.eq('project_id', project_id)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		locals.log.error({ msg: 'Error fetching project:', projectError });
		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Parse stage_order from URL parameter
	const stageOrderNum = parseInt(stage_order, 10);
	if (isNaN(stageOrderNum)) {
		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}`,
			{ type: 'error', message: 'Invalid stage order' },
			cookies,
		);
	}

	// Find the project stage by stage_order
	const { data: stageData, error: stageError } = await supabase
		.from('project_stage')
		.select('*')
		.eq('project_id', project_id)
		.eq('stage_order', stageOrderNum)
		.limit(1)
		.maybeSingle();

	if (stageError || !stageData) {
		locals.log.error({ msg: 'Error fetching project stage:', stageError });
		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}`,
			{ type: 'error', message: 'Project stage not found' },
			cookies,
		);
	}

	// Fetch budget snapshots for this stage
	const { data: budgetSnapshots, error: snapshotsError } = await supabase
		.from('budget_snapshot')
		.select('*')
		.eq('project_stage_id', stageData.project_stage_id)
		.order('freeze_date', { ascending: false });

	if (snapshotsError) {
		locals.log.error({ msg: 'Error fetching budget snapshots:', snapshotsError });
		throw error(500, { message: 'Error loading budget snapshots' });
	}

	// If no snapshots exist, show the current budget for this stage
	if (!budgetSnapshots || budgetSnapshots.length === 0) {
		// Fetch the complete WBS library for this project
		const { data: allWbsItems, error: wbsError } = await supabase
			.from('wbs_library_item')
			.select('*')
			.eq('wbs_library_id', projectData.wbs_library_id)
			.or(
				`client_id.eq.${projectData.client_id},project_id.eq.${projectData.project_id},item_type.eq.Standard`,
			);

		if (wbsError) {
			locals.log.error({ msg: 'Error fetching WBS items:', wbsError });
			throw error(500, { message: 'Error loading WBS library data' });
		}

		// Fetch current budget line items for this project
		const { data: currentBudgetItems, error: currentBudgetError } = await supabase
			.from('budget_line_item_current')
			.select('*')
			.eq('project_id', project_id);

		if (currentBudgetError) {
			locals.log.error({ msg: 'Error fetching current budget items:', currentBudgetError });
			throw error(500, { message: 'Error loading current budget data' });
		}

		return {
			project: projectData,
			stage: stageData,
			budgetSnapshots: [],
			allWbsItems: allWbsItems || [],
			rawSnapshotItems: currentBudgetItems || [],
			hasSnapshots: false,
			showingCurrentBudget: true,
		};
	}

	// Get the most recent snapshot (first in the ordered list)
	const latestSnapshot = budgetSnapshots[0];

	// Fetch the complete WBS library for this project
	const { data: allWbsItems, error: wbsError } = await supabase
		.from('wbs_library_item')
		.select('*')
		.eq('wbs_library_id', projectData.wbs_library_id)
		.or(
			`client_id.eq.${projectData.client_id},project_id.eq.${projectData.project_id},item_type.eq.Standard`,
		);

	if (wbsError) {
		locals.log.error({ msg: 'Error fetching WBS items:', wbsError });
		throw error(500, { message: 'Error loading WBS library data' });
	}

	// Fetch budget snapshot line items for the latest snapshot
	const { data: rawSnapshotItems, error: itemsError } = await supabase
		.from('budget_snapshot_line_item')
		.select('*')
		.eq('budget_snapshot_id', latestSnapshot.budget_snapshot_id);

	if (itemsError) {
		locals.log.error({ msg: 'Error fetching budget snapshot line items:', itemsError });
		throw error(500, { message: 'Error loading budget snapshot data' });
	}

	return {
		project: projectData,
		stage: stageData,
		budgetSnapshots,
		latestSnapshot,
		allWbsItems: allWbsItems || [],
		rawSnapshotItems: rawSnapshotItems || [],
		hasSnapshots: true,
		showingCurrentBudget: false,
	};
};
