import { json, error } from '@sveltejs/kit';
import { requireUser } from '$lib/server/auth';
import type { RequestHandler } from './$types';
import { projectUUID } from '$lib/schemas/project';

export const DELETE: RequestHandler = async ({ locals, params }) => {
	try {
		await requireUser();
		const { supabase } = locals;
		const { project_id_short, invoice_id } = params;

		const project_id = projectUUID(project_id_short);

		// Verify invoice exists and belongs to the project through purchase order
		const { data: invoice, error: invoiceCheckError } = await supabase
			.from('invoice')
			.select(
				'invoice_id, purchase_order_id, purchase_order:purchase_order_id(po_number, project_id)',
			)
			.eq('invoice_id', invoice_id)
			.single();

		if (invoiceCheckError || !invoice) {
			console.error('Error fetching invoice:', invoiceCheckError);
			throw error(404, 'Invoice not found');
		}

		// Verify the invoice belongs to the correct project through purchase order
		if (invoice.purchase_order?.project_id !== project_id) {
			throw error(404, 'Invoice not found');
		}

		// Delete the invoice (this will trigger the audit function)
		const { error: deleteError } = await supabase
			.from('invoice')
			.delete()
			.eq('invoice_id', invoice_id);

		if (deleteError) {
			console.error('Error deleting invoice:', deleteError);
			throw error(500, 'Failed to delete invoice');
		}

		return json({
			success: true,
			message: `Invoice ${invoice.purchase_order?.po_number || 'Unknown PO'} deleted successfully`,
		});
	} catch (err) {
		console.error('Error in DELETE handler:', err);
		if (err instanceof Error && 'status' in err) {
			throw err;
		}
		throw error(500, 'Internal server error');
	}
};
