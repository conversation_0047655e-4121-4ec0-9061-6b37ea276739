import type { ServerLoad } from '@sveltejs/kit';
import type { Tables } from '$lib/database.types';
import { getGatewayChecklistItems, type getProjectWithStages } from '$lib/project_utils';

export const load: ServerLoad = async ({ parent, locals }) => {
	const { project, currentStage } = (await parent()) as {
		project: Awaited<ReturnType<typeof getProjectWithStages>>['project'];
		currentStage: Tables<'project_stage'> | undefined;
	};
	if (!project) {
		throw new Error('Project not found');
	}

	// For each completed stage, get its gateway checklist items completion status
	const stagesWithChecklist = await Promise.all(
		(project.project_stage || []).map(async (stage) => {
			const checklistItems = await getGatewayChecklistItems(
				locals.supabase,
				stage.project_stage_id,
			);

			const totalItems = checklistItems?.length || 0;
			const completedItems =
				checklistItems?.filter((item) => item.status === 'Complete')?.length || 0;

			return {
				...stage,
				checklist: {
					total: totalItems,
					completed: completedItems,
					completionPercentage: totalItems > 0 ? (completedItems / totalItems) * 100 : 0,
				},
			};
		}),
	);

	return {
		project,
		projectStages: stagesWithChecklist,
		currentStage,
	};
};
