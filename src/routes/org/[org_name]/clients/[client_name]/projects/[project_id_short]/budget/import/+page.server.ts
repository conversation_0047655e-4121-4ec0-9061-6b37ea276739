import type { PageServerLoad } from './$types';
import { requireProject } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals }) => {
	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Check user permissions
	const { data: canEditProject } = await supabase.rpc('can_modify_project', {
		project_id_param: project_id,
	});

	if (!canEditProject) {
		throw new Error('You do not have permission to import budget data for this project');
	}

	return {
		title: `Import Budget`,
	};
};
