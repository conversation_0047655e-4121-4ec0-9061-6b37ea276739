import { superValidate, message } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import { type Actions, fail } from '@sveltejs/kit';
import { getGatewayChecklistItems, upsertGatewayChecklistItem } from '$lib/project_utils';
import { redirect } from 'sveltekit-flash-message/server';
import { Constants } from '$lib/database.types';
import { requireProject } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';

// Create a type-safe array of valid checklist item statuses
const validStatuses = Constants.public.Enums.checklist_item_status;

// Base schema for checklist items
const checklistFormSchema = z.object({
	items: z.array(
		z.object({
			gateway_checklist_item_id: z.uuid(),
			name: z.string().min(1, 'Checklist item name is required'),
			description: z.string().optional().nullable(),
			status: z.enum(validStatuses),
		}),
	),
});

export async function load({ locals, cookies }) {
	const { supabase } = locals;
	const { org_name, client_name, project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Get the project with project stages
	const { data: project, error: projectError } = await supabase
		.from('project')
		.select('*, project_stage!project_stage_project_id_fkey(*)')
		.eq('project_id', project_id)
		.limit(1)
		.maybeSingle();

	if (projectError || !project) {
		locals.log.error({ msg: 'Error fetching project:', projectError });
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Find the current active stage (first incomplete stage)
	const allStages = Array.isArray(project.project_stage)
		? project.project_stage
		: project.project_stage
			? [project.project_stage]
			: [];
	const currentStage = allStages.find((stage) => !stage.date_completed) || allStages[0];

	if (!currentStage) {
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}`,
			{ type: 'error', message: 'No active project stage found' },
			cookies,
		);
	}

	// Get checklist items for the current stage
	let checklistItems: Awaited<ReturnType<typeof getGatewayChecklistItems>> = [];
	try {
		checklistItems = await getGatewayChecklistItems(supabase, currentStage.project_stage_id);
	} catch (error) {
		locals.log.error({ msg: 'Error fetching checklist items:', error });
	}

	// Prepare initial form data for checklist items
	const checklistFormData = {
		items: checklistItems.map((item) => ({
			gateway_checklist_item_id: item.gateway_checklist_item_id,
			name: item.name,
			description: item.description,
			status: item.status,
		})),
	};

	// Prepare form with initial data
	const checklistForm = await superValidate(checklistFormData, zod(checklistFormSchema));

	return {
		project,
		currentStage,
		checklistItems,
		checklistForm,
	};
}

export const actions: Actions = {
	// Update checklist items
	updateChecklist: async ({ request, locals }) => {
		const { supabase } = locals;
		const { project_id_short } = requireProject();

		const project_id = projectUUID(project_id_short);

		// Get form data
		const form = await superValidate(request, zod(checklistFormSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: project_id,
		});

		if (!canEdit) {
			return fail(403, {
				form,
				message: { type: 'error', text: 'You do not have permission to edit this project' },
			});
		}

		// Get current project stage
		const { data: stages } = await supabase
			.from('project_stage')
			.select('*')
			.eq('project_id', project_id)
			.order('stage_order', { ascending: true });

		const currentStage = stages?.find((stage) => !stage.date_completed) || stages?.[0];

		if (!currentStage) {
			return fail(404, { form, message: { type: 'error', text: 'No active project stage found' } });
		}

		// Process each checklist item update
		for (const item of form.data.items) {
			// Use the upsertGatewayChecklistItem function to handle both item update and status change
			const { error, statusUpdated } = await upsertGatewayChecklistItem(
				supabase,
				{
					gateway_checklist_item_id: item.gateway_checklist_item_id,
					name: item.name,
					description: item.description,
					project_stage_id: currentStage.project_stage_id,
				},
				item.status,
			);

			if (error) {
				locals.log.error({ msg: 'Error updating checklist item:', error });
				return fail(500, {
					form,
					message: { type: 'error', text: 'Failed to update checklist item' },
				});
			}

			if (!statusUpdated) {
				locals.log.error({
					msg: 'Status was not updated for item:',
					gateway_checklist_item_id: item.gateway_checklist_item_id,
				});
			}
		}

		// Return success message
		return message(form, { type: 'success', text: 'Checklist items updated successfully' });
	},
};
