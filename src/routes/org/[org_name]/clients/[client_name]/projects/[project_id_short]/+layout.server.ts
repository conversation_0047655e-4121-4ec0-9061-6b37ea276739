import type { ServerLoad } from '@sveltejs/kit';
import { redirect } from 'sveltekit-flash-message/server';
import { requireProject } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';

export const load: ServerLoad = async ({ locals, cookies }) => {
	const { supabase } = locals;
	const { org_name, client_name, project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	const { data: project, error: projectError } = await supabase
		.from('project')
		.select('*, project_stage!project_stage_project_id_fkey(*)')
		.eq('project_id', project_id)
		.order('stage_order', {
			referencedTable: 'project_stage',
			ascending: true,
		})
		.limit(1)
		.maybeSingle();

	if (projectError || !project) {
		locals.log.error({ msg: 'Error fetching project', projectError });
		return redirect(
			`/org/${org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// 	// Check if user can edit the project
	const { data: canEditProject, error: canEditProjectError } = await supabase.rpc(
		'can_modify_project',
		{
			project_id_param: project_id,
		},
	);

	if (canEditProjectError) {
		locals.log.error({ msg: 'Error checking project edit permissions', canEditProjectError });
	}

	const firstIncompleteStage = project.project_stage.find((stage) => !stage.date_completed);

	// Find the current active stage (first incomplete stage)
	// If all stages are completed, then there is no current stage (return undefined)
	const currentStage = project.project_stage.every((stage) => stage.date_completed)
		? undefined
		: firstIncompleteStage || project.project_stage[0];

	const isConstructionStage = currentStage?.project_stage_id === project.construction_stage_id;

	return {
		client_name: client_name,
		org_name: org_name,
		project_id_short: project_id_short,
		project,
		projectStages: project.project_stage,
		currentStage,
		isConstructionStage,
		canEditProject: canEditProject || false,
	};
};
