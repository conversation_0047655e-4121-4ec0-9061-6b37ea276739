<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import * as Form from '$lib/components/ui/form';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { toast } from 'svelte-sonner';
	import { formatCurrency } from '$lib/utils.js';
	import SpinnerGapIcon from 'phosphor-svelte/lib/SpinnerGap';
	import FloppyDiskIcon from 'phosphor-svelte/lib/FloppyDisk';

	const { data } = $props();

	const project = $derived(data.project);
	const wbsItem = $derived(data.wbsItem);

	// Setup form
	const budgetForm = superForm(data.form, {
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					// redirected in form action
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: formData, enhance, delayed, submitting } = budgetForm;

	// Calculate cost based on inputs
	const unit_rate = $derived.by(() => {
		const quantity = parseFloat($formData.quantity?.toString() || '0');
		const materialRate = parseFloat($formData.material_rate?.toString() || '0');
		const laborRate = parseFloat($formData.labor_rate?.toString() || '0');
		const productivity = parseFloat($formData.productivity_per_hour?.toString() || '1');

		let cost = quantity * materialRate;

		// Add labor costs if applicable
		if (laborRate && productivity > 0) {
			cost += (quantity * laborRate) / productivity;
		}

		return cost;
	});

	$effect(() => {
		$formData.unit_rate = unit_rate;
	});
</script>

<div class="container mx-auto max-w-(--breakpoint-xl) py-8">
	<div class="mb-4 flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold">{project.name} - Edit Budget Item</h1>
			<p class="mt-1 text-slate-600">
				Editing budget item for {wbsItem.code}: {wbsItem.description}
			</p>
		</div>
	</div>

	<div class="rounded-lg border bg-white p-6 shadow-xs">
		<form method="POST" action="?/updateBudgetItem" class="space-y-6" use:enhance>
			<input type="hidden" name="project_id" value={$formData.project_id} />
			<input type="hidden" name="budget_line_item_id" value={$formData.budget_line_item_id || ''} />
			<input type="hidden" name="wbs_library_item_id" value={$formData.wbs_library_item_id || ''} />

			<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
				<Form.Field form={budgetForm} name="quantity">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Quantity</Form.Label>
							<Input {...props} type="number" step="0.01" bind:value={$formData.quantity} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={budgetForm} name="unit">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Unit</Form.Label>
							<Input {...props} type="text" bind:value={$formData.unit} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			</div>

			<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
				<Form.Field form={budgetForm} name="material_rate">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Material Rate</Form.Label>
							<Input {...props} type="number" step="0.01" bind:value={$formData.material_rate} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={budgetForm} name="labor_rate">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Labor Rate</Form.Label>
							<Input {...props} type="number" step="0.01" bind:value={$formData.labor_rate} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			</div>

			<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
				<Form.Field form={budgetForm} name="productivity_per_hour">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Productivity Per Hour</Form.Label>
							<Input
								{...props}
								type="number"
								step="0.01"
								bind:value={$formData.productivity_per_hour}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<div>
					<Form.Field form={budgetForm} name="unit_rate">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>
									{$formData.unit_rate_manual_override
										? 'Manual Unit Cost'
										: 'Calculated Unit Cost'}
								</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									bind:value={$formData.unit_rate}
									disabled={!$formData.unit_rate_manual_override}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<div class="mt-2">
						<label class="inline-flex items-center">
							<input
								type="checkbox"
								bind:checked={$formData.unit_rate_manual_override}
								name="unit_rate_manual_override"
								class="mr-1 size-4"
							/>
							<span class="text-sm">Manual cost override</span>
						</label>
					</div>
				</div>
			</div>

			<div>
				Subtotal: {formatCurrency(($formData.unit_rate || 0) * ($formData.quantity || 0))}
			</div>

			<Form.Field form={budgetForm} name="remarks">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Remarks</Form.Label>
						<Textarea {...props} bind:value={$formData.remarks} rows={3} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<div class="flex justify-end space-x-2 pt-4">
				<Button
					type="button"
					variant="outline"
					href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
						data.client_name,
					)}/projects/{encodeURIComponent(data.project_id_short)}/budget">Cancel</Button
				>
				<Form.Button disabled={$submitting}>
					{#if $delayed}
						<SpinnerGapIcon class="text-primary size-4 animate-spin" />
					{:else}
						<FloppyDiskIcon class="size-4" />
					{/if}
					Save Budget Line Item</Form.Button
				>
			</div>
		</form>
	</div>
</div>
