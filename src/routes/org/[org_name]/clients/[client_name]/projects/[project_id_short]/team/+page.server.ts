import { fail, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { redirect } from 'sveltekit-flash-message/server';
import { requireProject } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';

export const load = (async ({ params, locals, cookies, depends }) => {
	depends('project:team');
	const { supabase } = locals;

	const { project_id_short, org_name } = params;

	const project_id = projectUUID(project_id_short);

	// Combined query to fetch project with memberships and invites
	const { data: project, error: projectError } = await supabase
		.from('project')
		.select(
			`*,
			client (name)`,
		)
		.eq('project_id', project_id)
		.limit(1)
		.maybeSingle();

	if (projectError || !project) {
		locals.log.error({ msg: 'Error fetching project:', projectError });
		return redirect(
			404,
			`/org/${org_name}/clients/${params.client_name}`,
			{ message: 'Project not found', type: 'error' },
			cookies,
		);
	}

	// Fetch all related data in parallel after getting project
	// TODO: fix rpc to use only project_id
	const [
		{ data: projectMembersWithDuplicates, error: membersError },
		{ data: memberships, error: membershipError },
		{ data: invites, error: invitesError },
	] = await Promise.all([
		supabase.rpc('profiles_with_project_access', {
			_project_name: project.name,
			_client_name: params.client_name,
		}),
		supabase
			.from('membership')
			.select('membership_id, role, user_id, profile (*)')
			.eq('entity_id', project.project_id)
			.eq('entity_type', 'project'),
		supabase
			.from('invite')
			.select('*')
			.eq('resource_id', project.project_id)
			.eq('resource_type', 'project')
			.eq('status', 'pending'),
	]);

	if (membersError) locals.log.error({ msg: membersError });
	if (membershipError) locals.log.error({ msg: membershipError });
	if (invitesError) locals.log.error({ msg: 'Error fetching invites:', invitesError });

	type ProjectMember = NonNullable<typeof projectMembersWithDuplicates>[number];

	type ProjectMemberWithRole = ProjectMember & {
		role?: string;
		membership_id?: string;
	};

	// Remove duplicates
	const projectMembers =
		projectMembersWithDuplicates?.reduce((acc, curr) => {
			const existingMember = acc.find((member) => member.user_id === curr?.user_id);
			if (!existingMember) {
				acc.push(curr);
			}
			return acc;
		}, [] as ProjectMemberWithRole[]) || [];

	// Merge membership data with project members
	for (const membership of memberships || []) {
		const member = projectMembers.find((m) => m.user_id === membership.user_id);
		if (member) {
			member.role = membership.role;
			member.membership_id = membership.membership_id;
		}
	}

	// Build names map
	const names = (memberships || [])
		.map((m) => m.profile)
		.reduce((acc: Record<string, string>, profile) => {
			// Use full_name if available, otherwise use email
			if (profile && profile.user_id)
				acc[profile.user_id] = profile.full_name ?? profile.email ?? 'Unknown user';
			return acc;
		}, {});

	const isProjectAdmin = memberships?.some(
		(m) => m.user_id === locals.user?.id && m.role === 'admin',
	);

	return {
		projectMembers,
		names,
		invites: invites || [],
		isProjectAdmin,
		project,
		user: locals.user,
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	remove: async ({ request, locals }) => {
		const data = await request.formData();
		const permissionId = data.get('permissionId')?.toString();

		if (!permissionId) {
			return fail(400, { error: 'Invalid permission ID' });
		}

		const { supabase } = locals;
		const { project_id_short } = requireProject();

		const project_id = projectUUID(project_id_short);

		// Check if user has permission to manage project
		const { data: isProjectAdmin } = await supabase.rpc('current_user_has_entity_role', {
			entity_type_param: 'project',
			entity_id_param: project_id,
			min_role_param: 'admin',
		});

		if (!isProjectAdmin) {
			return fail(403, { error: 'You do not have permission to manage team members' });
		}

		// Delete the membership
		const { error: err } = await supabase
			.from('membership')
			.delete()
			.eq('membership_id', permissionId);

		if (err) {
			locals.log.error({ msg: 'Error removing permission:', err });
			return fail(500, { error: 'Failed to remove user from project' });
		}

		return { success: true };
	},
};
