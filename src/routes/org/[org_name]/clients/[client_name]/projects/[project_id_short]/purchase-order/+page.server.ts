import type { PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals, depends }) => {
	depends('project:purchase-orders');
	await requireUser();

	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch purchase orders using the RPC function
	const { data: purchaseOrders, error: purchaseOrdersError } = await supabase.rpc(
		'get_accessible_purchase_orders',
		{
			project_id_param: project_id,
		},
	);

	if (purchaseOrdersError) {
		locals.log.error({ msg: 'Error fetching purchase orders:', purchaseOrdersError });
		throw new Error('Failed to fetch purchase orders');
	}

	return {
		purchaseOrders: purchaseOrders || [],
	};
};
