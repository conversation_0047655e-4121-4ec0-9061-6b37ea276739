import { fail, redirect } from '@sveltejs/kit';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { superValidate } from 'sveltekit-superforms';
import { orgSelectionSchema } from '$lib/schemas/org-selection';
import { ORG_COOKIE_NAME, ORG_COOKIE_OPTIONS } from '$lib/current-org.svelte';
import type { Actions } from '@sveltejs/kit';

export const actions: Actions = {
	selectOrganization: async ({ request, cookies }) => {
		// Validate the form data using zod schema
		const form = await superValidate(request, zod(orgSelectionSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const { orgId } = form.data;

		// Set the cookie with a 30-day expiry
		cookies.set(ORG_COOKIE_NAME, orgId, ORG_COOKIE_OPTIONS);

		if (form.data.route.includes('client')) {
			return redirect(303, '/');
		} else {
			return redirect(
				303,
				form.data.route.replace(/\/org\/([^/]+)/, `/org/${encodeURIComponent(form.data.orgName)}`),
			);
		}
	},
};
