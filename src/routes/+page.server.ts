import type { PageServerLoad } from './$types';

// Type for project data
type ProjectData = {
	project_id: string;
	name: string;
	description: string | null;
	created_at: string;
	updated_at: string;
	client: {
		name: string;
		organization: {
			name: string;
		};
	};
	is_project_admin: boolean;
	is_client_admin: boolean;
	is_org_admin: boolean;
	is_external_access?: boolean;
};

// Type for client data
type ClientData = {
	client_id: string;
	name: string;
	description: string | null;
	logo_url: string | null;
	client_url: string | null;
	internal_url: string | null;
	internal_url_description: string | null;
	org_id: string;
	created_at: string;
	updated_at: string;
	created_by_user_id: string;
	organization: {
		name: string;
	};
	project_count: number;
	projectCount: number; // For compatibility with frontend
	is_client_admin: boolean;
	is_org_admin: boolean;
	is_external_access?: boolean;
};

export const load = (async ({ locals }) => {
	const { orgId, supabase } = locals;

	type InternalDashboardData = {
		projects: Array<ProjectData>;
		clients: Array<ClientData>;
	};
	type ExternalDashboardData = {
		external_projects: Array<ProjectData>;
		external_clients: Array<ClientData>;
	};
	let inOrgData: InternalDashboardData = {
		projects: [],
		clients: [],
	};
	if (orgId) {
		const { data, error } = await supabase.rpc('get_dashboard_data', {
			org_id_param: orgId,
		});

		if (error) {
			locals.log.error({ msg: 'Error fetching dashboard data', error });
			return { projects: [], clients: [] };
		}
		inOrgData = data as { projects: Array<ProjectData>; clients: Array<ClientData> };
	}

	const { data: externalData, error: externalError } = await supabase.rpc(
		'get_external_dashboard_data',
	);

	if (externalError) {
		locals.log.error({ msg: 'Error fetching external dashboard data', externalError });
	}

	// Map clients to include projectCount for frontend compatibility
	const clientsWithProjectCount = (inOrgData?.clients ?? []).map((client) => ({
		...client,
		projectCount: client.project_count,
	}));

	// Map external clients to include projectCount for frontend compatibility
	const externalClientsWithProjectCount = (
		(externalData as ExternalDashboardData)?.external_clients ?? []
	).map((client) => ({
		...client,
		projectCount: client.project_count,
	}));

	return {
		title: 'Dashboard',
		projects: inOrgData?.projects ?? [],
		clients: clientsWithProjectCount,
		external_projects: (externalData as ExternalDashboardData)?.external_projects ?? [],
		external_clients: externalClientsWithProjectCount,
	};
}) satisfies PageServerLoad;
