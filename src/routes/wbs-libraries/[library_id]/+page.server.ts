import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { buildWbsItemTree } from '$lib/wbs_utils';

export const load: PageServerLoad = async ({ locals, params }) => {
	const { supabase } = locals;
	const { library_id } = params;

	if (!library_id) {
		throw error(404, { message: 'Library not found' });
	}

	// Fetch the library details
	const { data: library, error: libraryError } = await supabase
		.from('wbs_library')
		.select('*')
		.eq('wbs_library_id', library_id)
		.limit(1)
		.maybeSingle();

	if (libraryError || !library) {
		locals.log.error({ msg: 'Error fetching WBS library:', libraryError });
		throw error(404, { message: 'Library not found' });
	}

	// Fetch all items in the library
	const { data: items, error: itemsError } = await supabase
		.from('wbs_library_item')
		.select('*')
		.eq('wbs_library_id', library_id)
		.order('code');

	if (itemsError) {
		locals.log.error({ msg: 'Error fetching WBS library items:', itemsError });
		throw error(500, { message: 'Error loading library items' });
	}

	// Convert flat items structure to hierarchical tree
	const itemsTree = buildWbsItemTree(items);

	return {
		title: `${library.name} - WBS Library`,
		library,
		items,
		itemsTree,
	};
};
