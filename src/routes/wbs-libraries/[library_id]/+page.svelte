<script lang="ts">
	import type { PageProps } from './$types';
	import WbsTreeItem from '$lib/components/wbs-tree-item.svelte';

	let { data }: PageProps = $props();
	let expandedCategories = $state<Record<number, boolean>>({});

	// Check if any items have scope defined to show the column
	const showScopeColumn = $derived(data.items.some((item) => item.cost_scope));
</script>

<div class="container max-w-(--breakpoint-xl) py-8">
	<div class="mb-4 flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold">{data.library.name}</h1>
			{#if data.library.description}
				<p class="mt-2 text-slate-600">{data.library.description}</p>
			{/if}
		</div>
	</div>

	<div class="mb-6">
		<div class="rounded-lg bg-slate-100 p-4">
			<div class="mb-2 text-sm text-slate-500">Total Items: {data.items.length}</div>
		</div>
	</div>

	{#if data.items.length === 0}
		<div class="rounded-lg border bg-slate-50 p-8 text-center">
			<h2 class="mb-2 text-xl font-semibold">No Items Available</h2>
			<p class="mb-6 text-slate-600">This WBS library has no items yet.</p>
		</div>
	{:else}
		<div class="rounded-lg border bg-white p-6">
			<h2 class="mb-4 text-xl font-semibold">WBS Structure</h2>

			<div class="overflow-x-auto">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Code
							</th>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Description
							</th>
							{#if showScopeColumn}
								<th
									scope="col"
									class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
								>
									Scope
								</th>
							{/if}
						</tr>
					</thead>
					<tbody class="divide-y divide-gray-200 bg-white">
						{#each data.itemsTree as item (item.wbs_library_item_id)}
							<WbsTreeItem {item} depth={0} showScope={showScopeColumn} bind:expandedCategories />
						{/each}
					</tbody>
				</table>
			</div>
		</div>
	{/if}
</div>
