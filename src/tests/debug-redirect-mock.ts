import { vi } from 'vitest';
import type { Redirect } from '@sveltejs/kit';

interface RedirectError extends Error, Redirect {}

// Original mock implementation from both files
const redirectMockFromSigninFlow = vi
	.fn()
	.mockImplementation((status: Redirect['status'], location: string) => {
		console.log('Redirect mock from signin-flow called with:', { status, location });
		const error = new Error(`Redirect to ${location}`) as RedirectError;
		error.status = status;
		throw error;
	});

const redirectMockFromAuthState = vi
	.fn()
	.mockImplementation((status: Redirect['status'], location: string) => {
		console.log('Redirect mock from auth-state called with:', { status, location });
		const error = new Error(`Redirect to ${location}`) as RedirectError;
		error.status = status;
		error.location = location;
		throw error;
	});

const setupMockFromTest = vi
	.fn()
	.mockImplementation((status: Redirect['status'], location: string) => {
		console.log('Redirect mock from setup.ts called with:', { status, location });
		const error = new Error(`Redirect to ${location}`) as RedirectError;
		error.status = status;
		error.location = location;
		throw error;
	});

// Debug version with explicit logging
const debugRedirectMock = vi
	.fn()
	.mockImplementation((status: Redirect['status'], location: string) => {
		console.log('Debug redirect mock called with:', { status, location });

		// Create an error with all properties
		const error = new Error(`Redirect to ${location}`) as RedirectError;
		error.status = status;
		error.location = location;

		// Log the error before throwing
		console.log('About to throw error:', error);

		throw error;
	});

// Function to test catching the redirect
function testRedirectCatching(
	mockFn: (status: Redirect['status'], location: string) => void,
): Error | false {
	try {
		mockFn(303, '/test');
		console.log('This should not be reached if redirect throws correctly');
		return false;
	} catch (error: unknown) {
		console.log('Caught error:', error);
		const e = error as RedirectError;
		console.log('Error message:', e.message);
		console.log('Error status:', e.status);
		console.log('Error location:', e.location);
		return e;
	}
}

// Export for testing
export {
	redirectMockFromSigninFlow,
	redirectMockFromAuthState,
	setupMockFromTest,
	debugRedirectMock,
	testRedirectCatching,
};
