import { describe, it, expect } from 'vitest';
import {
	signUpSchema,
	signInSchema,
	resetPasswordSchema,
	changePasswordSchema,
} from '$lib/schemas/auth';

describe('Auth Form Validation', () => {
	describe('Sign Up Schema', () => {
		it('should validate valid signup data', () => {
			const validData = {
				email: '<EMAIL>',
				password: 'password123',
			};

			const result = signUpSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should reject invalid email formats', () => {
			const invalidEmails = [
				'not-an-email',
				'missing@domain',
				'@missinguser.com',
				'spaces <EMAIL>',
				'',
			];

			invalidEmails.forEach((email) => {
				const data = { email, password: 'password123' };
				const result = signUpSchema.safeParse(data);
				expect(result.success).toBe(false);
			});
		});

		it('should reject short passwords', () => {
			const data = { email: '<EMAIL>', password: 'short' };
			const result = signUpSchema.safeParse(data);
			expect(result.success).toBe(false);
		});

		it('should reject empty passwords', () => {
			const data = { email: '<EMAIL>', password: '' };
			const result = signUpSchema.safeParse(data);
			expect(result.success).toBe(false);
		});
	});

	describe('Sign In Schema', () => {
		it('should validate valid signin data', () => {
			const validData = {
				email: '<EMAIL>',
				password: 'password123',
			};

			const result = signInSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should reject invalid email formats', () => {
			const invalidEmails = [
				'not-an-email',
				'missing@domain',
				'@missinguser.com',
				'spaces <EMAIL>',
				'',
			];

			invalidEmails.forEach((email) => {
				const data = { email, password: 'password123' };
				const result = signInSchema.safeParse(data);
				expect(result.success).toBe(false);
			});
		});
	});

	describe('Reset Password Schema', () => {
		it('should validate valid email', () => {
			const validData = { email: '<EMAIL>' };
			const result = resetPasswordSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should reject invalid email formats', () => {
			const invalidEmails = [
				'not-an-email',
				'missing@domain',
				'@missinguser.com',
				'spaces <EMAIL>',
				'',
			];

			invalidEmails.forEach((email) => {
				const data = { email };
				const result = resetPasswordSchema.safeParse(data);
				expect(result.success).toBe(false);
			});
		});
	});

	describe('Change Password Schema', () => {
		it('should validate matching passwords', () => {
			const validData = {
				password: 'newpassword123',
				confirmPassword: 'newpassword123',
			};

			const result = changePasswordSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should reject non-matching passwords', () => {
			const invalidData = {
				password: 'newpassword123',
				confirmPassword: 'differentpassword123',
			};

			const result = changePasswordSchema.safeParse(invalidData);
			expect(result.success).toBe(false);
		});

		it('should reject short passwords', () => {
			const invalidData = {
				password: 'short',
				confirmPassword: 'short',
			};

			const result = changePasswordSchema.safeParse(invalidData);
			expect(result.success).toBe(false);
		});
	});
});
