/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { describe, it, expect, vi, beforeEach } from 'vitest';

vi.mock('@sveltejs/kit', async () => {
	const original = await vi.importActual('@sveltejs/kit');
	return {
		...original,
		fail: vi.fn().mockImplementation((code, data) => ({ status: code, ...data })),
	};
});

vi.mock('sveltekit-flash-message/server', () => ({ redirect: vi.fn() }));

vi.mock('$lib/project_utils', () => ({
	getGatewayChecklistItems: vi.fn(),
	upsertGatewayChecklistItem: vi.fn(),
	isStageReadyForCompletion: vi.fn(),
	completeProjectStage: vi.fn(),
}));

vi.mock('$lib/server/auth', () => ({
	requireProject: vi.fn().mockReturnValue({
		org_name: 'org',
		client_name: 'client',
		project_id_short: 'abc123def456',
	}),
}));

vi.mock('sveltekit-superforms/server', () => ({
	superValidate: vi.fn(),
	message: vi.fn(),
}));
vi.mock('sveltekit-superforms/adapters', () => ({
	zod: vi.fn().mockImplementation((schema) => schema),
	zod4: vi.fn().mockImplementation((schema) => schema),
}));

import { actions } from '../../../routes/org/[org_name]/clients/[client_name]/projects/[project_id_short]/stage-[stage_order]/gateway/+page.server';
import { upsertGatewayChecklistItem } from '$lib/project_utils';
import { superValidate } from 'sveltekit-superforms/server';

describe('completed stage guards', () => {
	let supabase;
	let params;
	let request;

	beforeEach(() => {
		vi.clearAllMocks();

		const projectQuery = {
			select: vi.fn().mockReturnThis(),
			eq: vi.fn().mockReturnThis(),
			limit: vi.fn().mockReturnThis(),
			maybeSingle: vi
				.fn()
				.mockResolvedValue({ data: { project_id: 'proj', client: { name: 'client' } } }),
		};
		const stageQuery = {
			select: vi.fn().mockReturnThis(),
			eq: vi.fn().mockReturnThis(),
			order: vi.fn().mockReturnThis(),
			limit: vi.fn().mockReturnThis(),
			maybeSingle: vi.fn().mockResolvedValue({
				data: { project_stage_id: 'stage1', project_id: 'proj', date_completed: '2024-01-01' },
			}),
			update: vi.fn().mockResolvedValue({ error: null }),
		};
		const gatewayInfoQuery = {
			select: vi.fn().mockReturnThis(),
			eq: vi.fn().mockReturnThis(),
			maybeSingle: vi.fn().mockResolvedValue({ data: null }),
			update: vi.fn(),
			insert: vi.fn(),
		};
		supabase = {
			from: vi.fn((table) => {
				if (table === 'project') return projectQuery;
				if (table === 'project_stage') return stageQuery;
				if (table === 'project_gateway_stage_info') return gatewayInfoQuery;
				return {
					select: vi.fn().mockReturnThis(),
					eq: vi.fn().mockReturnThis(),
					maybeSingle: vi.fn().mockResolvedValue({ data: null }),
				};
			}),
			rpc: vi.fn().mockResolvedValue({ data: true }),
		};
		params = { stage_order: '1' };
		request = {};
	});

	it('updateGatewayStageInfo rejects when stage completed', async () => {
		superValidate.mockResolvedValue({ valid: true, data: { project_stage_id: 'stage1' } });
		const result = await actions.updateGatewayStageInfo({
			request,
			locals: { supabase },
			params,
			cookies: {},
		});
		expect(result.status).toBe(403);
		expect(supabase.from('project_gateway_stage_info').update).not.toHaveBeenCalled();
		expect(supabase.from('project_gateway_stage_info').insert).not.toHaveBeenCalled();
	});

	it('updateChecklist rejects when stage completed', async () => {
		superValidate.mockResolvedValue({
			valid: true,
			data: { items: [{ gateway_checklist_item_id: '1', status: 'Complete' }] },
		});
		const result = await actions.updateChecklist({ request, locals: { supabase }, params });
		expect(result.status).toBe(403);
		expect(upsertGatewayChecklistItem).not.toHaveBeenCalled();
	});

	it('updateQualitativeScorecard rejects when stage completed', async () => {
		superValidate.mockResolvedValue({
			valid: true,
			data: { project_stage_id: 'stage1', scorecard: [[]] },
		});
		const result = await actions.updateQualitativeScorecard({
			request,
			locals: { supabase },
			params,
		});
		expect(result.status).toBe(403);
		expect(supabase.from('project_stage').update).not.toHaveBeenCalled();
	});
});
