import { describe, it, expect } from 'vitest';

describe('Vendor Form Auto-Selection Logic', () => {
	it('should detect project context when only one project and no organizations', () => {
		// Simulate the data structure passed to NewVendor component in project context
		const projectContextData = {
			organizations: [],
			clients: [
				{
					client: {
						client_id: 'client-1',
						name: 'Test Client',
						organization: {
							org_id: 'org-1',
							name: 'Test Org',
						},
					},
					role: 'editor',
				},
			],
			projects: [
				{
					project: {
						project_id: 'project-1',
						name: 'Test Project',
						client: {
							client_id: 'client-1',
							name: 'Test Client',
							organization: {
								org_id: 'org-1',
								name: 'Test Org',
							},
						},
					},
					role: 'editor',
				},
			],
		};

		// Test the auto-detection logic
		const shouldAutoDetect =
			projectContextData.projects.length === 1 && projectContextData.organizations.length === 0;

		expect(shouldAutoDetect).toBe(true);

		// Test that we get the correct project
		const autoDetectedProject = shouldAutoDetect ? projectContextData.projects[0].project : null;
		expect(autoDetectedProject).not.toBeNull();
		expect(autoDetectedProject?.project_id).toBe('project-1');
		expect(autoDetectedProject?.name).toBe('Test Project');
		expect(autoDetectedProject?.client?.client_id).toBe('client-1');
	});

	it('should not detect project context when multiple projects exist', () => {
		// Simulate the data structure for global vendor creation
		const globalContextData = {
			organizations: [
				{
					organization: { org_id: 'org-1', name: 'Test Org' },
					role: 'admin',
				},
			],
			clients: [
				{
					client: { client_id: 'client-1', name: 'Test Client' },
					role: 'editor',
				},
			],
			projects: [
				{
					project: { project_id: 'project-1', name: 'Test Project 1' },
					role: 'editor',
				},
				{
					project: { project_id: 'project-2', name: 'Test Project 2' },
					role: 'editor',
				},
			],
		};

		// Test the auto-detection logic
		const shouldAutoDetect =
			globalContextData.projects.length === 1 && globalContextData.organizations.length === 0;

		expect(shouldAutoDetect).toBe(false);
	});

	it('should not detect project context when organizations exist', () => {
		// Simulate the data structure when user has organization access
		const orgContextData = {
			organizations: [
				{
					organization: { org_id: 'org-1', name: 'Test Org' },
					role: 'admin',
				},
			],
			clients: [],
			projects: [
				{
					project: { project_id: 'project-1', name: 'Test Project' },
					role: 'editor',
				},
			],
		};

		// Test the auto-detection logic
		const shouldAutoDetect =
			orgContextData.projects.length === 1 && orgContextData.organizations.length === 0;

		expect(shouldAutoDetect).toBe(false);
	});

	it('should handle empty data gracefully', () => {
		const emptyData = {
			organizations: [],
			clients: [],
			projects: [],
		};

		const shouldAutoDetect =
			emptyData.projects.length === 1 && emptyData.organizations.length === 0;

		expect(shouldAutoDetect).toBe(false);
	});
});
