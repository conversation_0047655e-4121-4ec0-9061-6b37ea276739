import { describe, it, expect } from 'vitest';
import { editProjectSchema } from '$lib/schemas/project';

describe('Project Edit Schema', () => {
	it('should validate valid project edit data', () => {
		const validData = {
			name: 'Test Project',
			description: 'A test project description',
			wbs_library_id: '550e8400-e29b-41d4-a716-************',
		};

		const result = editProjectSchema.safeParse(validData);
		expect(result.success).toBe(true);

		if (result.success) {
			expect(result.data.name).toBe('Test Project');
			expect(result.data.description).toBe('A test project description');
			expect(result.data.wbs_library_id).toBe('550e8400-e29b-41d4-a716-************');
		}
	});

	it('should validate project edit data with null description', () => {
		const validData = {
			name: 'Test Project',
			description: null,
			wbs_library_id: '550e8400-e29b-41d4-a716-446655440002',
		};

		const result = editProjectSchema.safeParse(validData);
		expect(result.success).toBe(true);

		if (result.success) {
			expect(result.data.name).toBe('Test Project');
			expect(result.data.description).toBe(null);
			expect(result.data.wbs_library_id).toBe('550e8400-e29b-41d4-a716-446655440002');
		}
	});

	it('should reject invalid project edit data - missing name', () => {
		const invalidData = {
			description: 'A test project description',
			wbs_library_id: '550e8400-e29b-41d4-a716-************',
		};

		const result = editProjectSchema.safeParse(invalidData);
		expect(result.success).toBe(false);

		if (!result.success) {
			expect(result.error.issues).toHaveLength(1);
			expect(result.error.issues[0].path).toEqual(['name']);
			expect(result.error.issues[0].message).toBe(
				'Invalid input: expected string, received undefined',
			);
		}
	});

	it('should reject invalid project edit data - empty name', () => {
		const invalidData = {
			name: '',
			description: 'A test project description',
			wbs_library_id: '550e8400-e29b-41d4-a716-************',
		};

		const result = editProjectSchema.safeParse(invalidData);
		expect(result.success).toBe(false);

		if (!result.success) {
			expect(result.error.issues).toHaveLength(1);
			expect(result.error.issues[0].path).toEqual(['name']);
			expect(result.error.issues[0].message).toBe('Project name is required');
		}
	});

	it('should reject invalid project edit data - missing wbs_library_id', () => {
		const invalidData = {
			name: 'Test Project',
			description: 'A test project description',
		};

		const result = editProjectSchema.safeParse(invalidData);
		expect(result.success).toBe(false);

		if (!result.success) {
			expect(result.error.issues).toHaveLength(1);
			expect(result.error.issues[0].path).toEqual(['wbs_library_id']);
			expect(result.error.issues[0].message).toBe('Please select a WBS library');
		}
	});

	it('should reject invalid project edit data - invalid uuid wbs_library_id', () => {
		const invalidData = {
			name: 'Test Project',
			description: 'A test project description',
			wbs_library_id: '1', // This should be a valid UUID, not a simple string
		};

		const result = editProjectSchema.safeParse(invalidData);
		expect(result.success).toBe(false);

		if (!result.success) {
			expect(result.error.issues).toHaveLength(1);
			expect(result.error.issues[0].path).toEqual(['wbs_library_id']);
			expect(result.error.issues[0].code).toBe('invalid_format');
		}
	});

	it('should reject invalid project edit data - number wbs_library_id', () => {
		const invalidData = {
			name: 'Test Project',
			description: 'A test project description',
			wbs_library_id: 0, // Should be a UUID string, not a number
		};

		const result = editProjectSchema.safeParse(invalidData);
		expect(result.success).toBe(false);

		if (!result.success) {
			expect(result.error.issues).toHaveLength(1);
			expect(result.error.issues[0].path).toEqual(['wbs_library_id']);
			expect(result.error.issues[0].message).toBe('Please select a WBS library');
		}
	});

	it('should reject invalid project edit data - negative number wbs_library_id', () => {
		const invalidData = {
			name: 'Test Project',
			description: 'A test project description',
			wbs_library_id: -1, // Should be a UUID string, not a number
		};

		const result = editProjectSchema.safeParse(invalidData);
		expect(result.success).toBe(false);

		if (!result.success) {
			expect(result.error.issues).toHaveLength(1);
			expect(result.error.issues[0].path).toEqual(['wbs_library_id']);
			expect(result.error.issues[0].message).toBe('Please select a WBS library');
		}
	});

	it('should not include stage selection fields', () => {
		// This test verifies that the edit schema doesn't include the problematic fields
		const dataWithStageFields = {
			name: 'Test Project',
			description: 'A test project description',
			wbs_library_id: '550e8400-e29b-41d4-a716-************',
			import_from_costx: false,
			stage_selection_type: 'icms',
			selected_icms_stages: [1, 2, 3],
			selected_riba_stages: [],
			custom_stages: [],
		};

		const result = editProjectSchema.safeParse(dataWithStageFields);
		expect(result.success).toBe(true);

		if (result.success) {
			// The schema should only include the basic fields, ignoring the stage fields
			expect(result.data).toEqual({
				name: 'Test Project',
				description: 'A test project description',
				wbs_library_id: '550e8400-e29b-41d4-a716-************',
			});

			// Verify that stage fields are not included
			expect('import_from_costx' in result.data).toBe(false);
			expect('stage_selection_type' in result.data).toBe(false);
			expect('selected_icms_stages' in result.data).toBe(false);
			expect('selected_riba_stages' in result.data).toBe(false);
			expect('custom_stages' in result.data).toBe(false);
		}
	});
});
