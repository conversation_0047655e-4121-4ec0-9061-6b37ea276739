import { describe, it, expect } from 'vitest';
import { buildBudgetTree, calculateUnitRate, type RawNode } from '$lib/budget_utils';

// Helper function to build a RawNode with minimal fields
function makeRawNode(
	id: string,
	parentId: string | null,
	quantity: number,
	unitRate: number,
): RawNode {
	return {
		client_id: null,
		code: `code-${id}`,
		cost_scope: null,
		created_at: '',
		description: `Node ${id}`,
		in_level_code: `node-${id}`,
		item_type: 'Standard',
		level: parentId ? 2 : 1,
		parent_item_id: parentId,
		project_id: null,
		updated_at: '',
		wbs_library_id: 1,
		wbs_library_item_id: id,
		budget_line_item_current: [
			{
				budget_line_item_id: 1, // Keep as number since this is a different ID
				cost_certainty: null,
				created_at: '',
				design_certainty: null,
				labor_rate: null,
				material_rate: unitRate,
				productivity_per_hour: null,
				project_id: 'proj',
				quantity,
				remarks: null,
				unit: null,
				unit_rate: unitRate,
				unit_rate_manual_override: false,
				updated_at: '',
				wbs_library_item_id: id,
			},
		],
	} as unknown as RawNode;
}

describe('calculateUnitRate', () => {
	it('returns manual unit rate when override flag is set', () => {
		const item: Partial<RawNode['budget_line_item_current'][number]> = {
			unit_rate_manual_override: true,
			unit_rate: 12,
		};
		expect(calculateUnitRate(item)).toBe(12);
	});

	it('defaults missing manual unit rate to zero', () => {
		const item: Partial<RawNode['budget_line_item_current'][number]> = {
			unit_rate_manual_override: true,
		};
		expect(calculateUnitRate(item)).toBe(0);
		expect(item.unit_rate).toBe(0);
	});

	it('calculates rate from material and labor', () => {
		const item: Partial<RawNode['budget_line_item_current'][number]> = {
			material_rate: 10,
			labor_rate: 20,
			productivity_per_hour: 4,
		};
		expect(calculateUnitRate(item)).toBe(10 + 20 / 4);
	});
});

describe('buildBudgetTree', () => {
	it('computes direct, children and total costs', () => {
		const raw = [
			makeRawNode('00000000-0000-0000-0000-000000000001', null, 2, 10),
			makeRawNode(
				'00000000-0000-0000-0000-000000000002',
				'00000000-0000-0000-0000-000000000001',
				3,
				5,
			),
		];
		const tree = buildBudgetTree(raw);
		expect(tree).toHaveLength(1);
		const root = tree[0];
		expect(root.directCost).toBe(20); // 2 * 10
		expect(root.children).toHaveLength(1);
		const child = root.children[0];
		expect(child.totalCost).toBe(15); // 3 * 5
		expect(root.childrenCost).toBe(child.totalCost);
		expect(root.totalCost).toBe(root.directCost + child.totalCost);
	});

	it('includes parent nodes without budget items in the tree', () => {
		const raw = [
			// Parent node with no budget items (empty array)
			{
				...makeRawNode('parent-1', null, 0, 0),
				budget_line_item_current: [], // No budget items
			},
			// Child node with budget items
			makeRawNode('child-1', 'parent-1', 5, 20),
		];

		const tree = buildBudgetTree(raw);
		expect(tree).toHaveLength(1);

		const parent = tree[0];
		expect(parent.wbs_library_item_id).toBe('parent-1');
		expect(parent.directCost).toBe(0); // No direct budget items
		expect(parent.children).toHaveLength(1);

		const child = parent.children[0];
		expect(child.wbs_library_item_id).toBe('child-1');
		expect(child.directCost).toBe(100); // 5 * 20
		expect(child.totalCost).toBe(100);

		// Parent should roll up child costs
		expect(parent.childrenCost).toBe(100);
		expect(parent.totalCost).toBe(100); // 0 direct + 100 children
	});

	it('handles multiple root nodes correctly', () => {
		const raw = [
			makeRawNode('root-1', null, 1, 10),
			makeRawNode('root-2', null, 2, 20),
			makeRawNode('child-1', 'root-1', 3, 5),
		];

		const tree = buildBudgetTree(raw);
		expect(tree).toHaveLength(2); // Two root nodes

		const root1 = tree.find((n) => n.wbs_library_item_id === 'root-1');
		const root2 = tree.find((n) => n.wbs_library_item_id === 'root-2');

		expect(root1).toBeDefined();
		expect(root2).toBeDefined();

		expect(root1!.children).toHaveLength(1);
		expect(root2!.children).toHaveLength(0);

		expect(root1!.totalCost).toBe(25); // 10 direct + 15 from child
		expect(root2!.totalCost).toBe(40); // 40 direct
	});

	it('handles complex hierarchy with mixed parent/child nodes', () => {
		const raw = [
			// Root node with no budget items
			{
				...makeRawNode('root', null, 0, 0),
				budget_line_item_current: [],
			},
			// Level 1 parent with budget items
			makeRawNode('level1-parent', 'root', 2, 50),
			// Level 1 parent with no budget items
			{
				...makeRawNode('level1-empty', 'root', 0, 0),
				budget_line_item_current: [],
			},
			// Level 2 children under level1-parent
			makeRawNode('level2-child1', 'level1-parent', 1, 25),
			makeRawNode('level2-child2', 'level1-parent', 3, 10),
			// Level 2 child under level1-empty
			makeRawNode('level2-child3', 'level1-empty', 4, 15),
		];

		const tree = buildBudgetTree(raw);
		expect(tree).toHaveLength(1); // One root

		const root = tree[0];
		expect(root.wbs_library_item_id).toBe('root');
		expect(root.directCost).toBe(0);
		expect(root.children).toHaveLength(2);

		const level1Parent = root.children.find((n) => n.wbs_library_item_id === 'level1-parent');
		const level1Empty = root.children.find((n) => n.wbs_library_item_id === 'level1-empty');

		expect(level1Parent).toBeDefined();
		expect(level1Empty).toBeDefined();

		// level1-parent: has direct costs + children costs
		expect(level1Parent!.directCost).toBe(100); // 2 * 50
		expect(level1Parent!.children).toHaveLength(2);
		expect(level1Parent!.childrenCost).toBe(55); // 25 + 30
		expect(level1Parent!.totalCost).toBe(155); // 100 + 55

		// level1-empty: no direct costs, only children costs
		expect(level1Empty!.directCost).toBe(0);
		expect(level1Empty!.children).toHaveLength(1);
		expect(level1Empty!.childrenCost).toBe(60); // 4 * 15
		expect(level1Empty!.totalCost).toBe(60);

		// Root should roll up all costs
		expect(root.childrenCost).toBe(215); // 155 + 60
		expect(root.totalCost).toBe(215); // 0 + 215
	});
});
