import { describe, it, expect } from 'vitest';
import type { VendorCreationHierarchyItem } from '$lib/schemas/vendor';

// Mock the vendor utility functions that would be in vendor utils
const processVendorCreationHierarchy = (hierarchyData: VendorCreationHierarchyItem[]) => {
	const organizations = new Map();
	const clients = new Map();
	const projects = new Map();

	hierarchyData.forEach((item) => {
		if (item.entity_type === 'organization') {
			organizations.set(item.entity_id, {
				org_id: item.entity_id,
				name: item.entity_name,
				user_role: item.user_role,
			});
		} else if (item.entity_type === 'client') {
			clients.set(item.entity_id, {
				client_id: item.entity_id,
				name: item.entity_name,
				org_id: item.parent_entity_id,
				org_name: item.parent_entity_name,
				user_role: item.user_role,
			});
		} else if (item.entity_type === 'project') {
			projects.set(item.entity_id, {
				project_id: item.entity_id,
				name: item.entity_name,
				client_id: item.parent_entity_id,
				client_name: item.parent_entity_name,
				org_id: item.grandparent_entity_id,
				org_name: item.grandparent_entity_name,
				user_role: item.user_role,
			});
		}
	});

	return {
		organizations: Array.from(organizations.values()),
		clients: Array.from(clients.values()),
		projects: Array.from(projects.values()),
	};
};

const getVendorAccessLevel = (vendor: {
	org_id?: string | null;
	client_id?: string | null;
	project_id?: string | null;
}): 'organization' | 'client' | 'project' => {
	if (vendor.project_id) return 'project';
	if (vendor.client_id) return 'client';
	return 'organization';
};

const canUserCreateVendorAtLevel = (
	userRole: string,
	level: 'organization' | 'client' | 'project',
): boolean => {
	// Organization and client level vendors require admin role
	if (level === 'organization' || level === 'client') {
		return userRole === 'admin';
	}
	// Project level vendors can be created by editors or admins
	return userRole === 'admin' || userRole === 'editor';
};

describe('Vendor Utility Functions', () => {
	describe('processVendorCreationHierarchy', () => {
		it('should process hierarchy data correctly', () => {
			const mockHierarchyData: VendorCreationHierarchyItem[] = [
				{
					entity_type: 'organization',
					entity_id: 'org-1',
					entity_name: 'Test Org',
					parent_entity_type: null,
					parent_entity_id: null,
					parent_entity_name: null,
					grandparent_entity_type: null,
					grandparent_entity_id: null,
					grandparent_entity_name: null,
					user_role: 'admin',
				},
				{
					entity_type: 'client',
					entity_id: 'client-1',
					entity_name: 'Test Client',
					parent_entity_type: 'organization',
					parent_entity_id: 'org-1',
					parent_entity_name: 'Test Org',
					grandparent_entity_type: null,
					grandparent_entity_id: null,
					grandparent_entity_name: null,
					user_role: 'admin',
				},
				{
					entity_type: 'project',
					entity_id: 'project-1',
					entity_name: 'Test Project',
					parent_entity_type: 'client',
					parent_entity_id: 'client-1',
					parent_entity_name: 'Test Client',
					grandparent_entity_type: 'organization',
					grandparent_entity_id: 'org-1',
					grandparent_entity_name: 'Test Org',
					user_role: 'editor',
				},
			];

			const result = processVendorCreationHierarchy(mockHierarchyData);

			expect(result.organizations).toHaveLength(1);
			expect(result.organizations[0]).toEqual({
				org_id: 'org-1',
				name: 'Test Org',
				user_role: 'admin',
			});

			expect(result.clients).toHaveLength(1);
			expect(result.clients[0]).toEqual({
				client_id: 'client-1',
				name: 'Test Client',
				org_id: 'org-1',
				org_name: 'Test Org',
				user_role: 'admin',
			});

			expect(result.projects).toHaveLength(1);
			expect(result.projects[0]).toEqual({
				project_id: 'project-1',
				name: 'Test Project',
				client_id: 'client-1',
				client_name: 'Test Client',
				org_id: 'org-1',
				org_name: 'Test Org',
				user_role: 'editor',
			});
		});

		it('should handle empty hierarchy data', () => {
			const result = processVendorCreationHierarchy([]);

			expect(result.organizations).toHaveLength(0);
			expect(result.clients).toHaveLength(0);
			expect(result.projects).toHaveLength(0);
		});

		it('should handle multiple entities of the same type', () => {
			const mockHierarchyData: VendorCreationHierarchyItem[] = [
				{
					entity_type: 'organization',
					entity_id: 'org-1',
					entity_name: 'Test Org 1',
					parent_entity_type: null,
					parent_entity_id: null,
					parent_entity_name: null,
					grandparent_entity_type: null,
					grandparent_entity_id: null,
					grandparent_entity_name: null,
					user_role: 'admin',
				},
				{
					entity_type: 'organization',
					entity_id: 'org-2',
					entity_name: 'Test Org 2',
					parent_entity_type: null,
					parent_entity_id: null,
					parent_entity_name: null,
					grandparent_entity_type: null,
					grandparent_entity_id: null,
					grandparent_entity_name: null,
					user_role: 'admin',
				},
			];

			const result = processVendorCreationHierarchy(mockHierarchyData);

			expect(result.organizations).toHaveLength(2);
			expect(result.organizations.map((org) => org.name)).toEqual(['Test Org 1', 'Test Org 2']);
		});
	});

	describe('getVendorAccessLevel', () => {
		it('should return project level for project-scoped vendor', () => {
			const vendor = {
				org_id: null,
				client_id: null,
				project_id: 'project-1',
			};

			const level = getVendorAccessLevel(vendor);
			expect(level).toBe('project');
		});

		it('should return client level for client-scoped vendor', () => {
			const vendor = {
				org_id: null,
				client_id: 'client-1',
				project_id: null,
			};

			const level = getVendorAccessLevel(vendor);
			expect(level).toBe('client');
		});

		it('should return organization level for org-scoped vendor', () => {
			const vendor = {
				org_id: 'org-1',
				client_id: null,
				project_id: null,
			};

			const level = getVendorAccessLevel(vendor);
			expect(level).toBe('organization');
		});

		it('should prioritize project level when multiple IDs are present', () => {
			const vendor = {
				org_id: 'org-1',
				client_id: 'client-1',
				project_id: 'project-1',
			};

			const level = getVendorAccessLevel(vendor);
			expect(level).toBe('project');
		});
	});

	describe('canUserCreateVendorAtLevel', () => {
		it('should allow admin to create vendors at organization level', () => {
			const canCreate = canUserCreateVendorAtLevel('admin', 'organization');
			expect(canCreate).toBe(true);
		});

		it('should not allow editor to create vendors at organization level', () => {
			const canCreate = canUserCreateVendorAtLevel('editor', 'organization');
			expect(canCreate).toBe(false);
		});

		it('should allow admin to create vendors at client level', () => {
			const canCreate = canUserCreateVendorAtLevel('admin', 'client');
			expect(canCreate).toBe(true);
		});

		it('should not allow editor to create vendors at client level', () => {
			const canCreate = canUserCreateVendorAtLevel('editor', 'client');
			expect(canCreate).toBe(false);
		});

		it('should allow admin to create vendors at project level', () => {
			const canCreate = canUserCreateVendorAtLevel('admin', 'project');
			expect(canCreate).toBe(true);
		});

		it('should allow editor to create vendors at project level', () => {
			const canCreate = canUserCreateVendorAtLevel('editor', 'project');
			expect(canCreate).toBe(true);
		});

		it('should not allow viewer to create vendors at any level', () => {
			expect(canUserCreateVendorAtLevel('viewer', 'organization')).toBe(false);
			expect(canUserCreateVendorAtLevel('viewer', 'client')).toBe(false);
			expect(canUserCreateVendorAtLevel('viewer', 'project')).toBe(false);
		});
	});
});
