import { describe, it, expect } from 'vitest';
import { vendorSchema, editVendorSchema } from '$lib/schemas/vendor';

describe('Vendor Schema Validation', () => {
	describe('vendorSchema', () => {
		it('should validate valid vendor data with organization scope', () => {
			const validData = {
				name: 'Test Vendor',
				description: 'A test vendor description',
				org_id: '550e8400-e29b-41d4-a716-446655440001',
				contact_name: '<PERSON>',
				contact_email: '<EMAIL>',
				contact_phone: '******-123-4567',
				vendor_type: 'Contractor',
				is_active: true,
			};

			const result = vendorSchema.safeParse(validData);
			expect(result.success).toBe(true);

			if (result.success) {
				expect(result.data.name).toBe('Test Vendor');
				expect(result.data.org_id).toBe('550e8400-e29b-41d4-a716-446655440001');
			}
		});

		it('should validate valid vendor data with client scope', () => {
			const validData = {
				name: 'Client Vendor',
				client_id: '550e8400-e29b-41d4-a716-446655440002',
				contact_email: '<EMAIL>',
				vendor_type: 'Supplier',
			};

			const result = vendorSchema.safeParse(validData);
			expect(result.success).toBe(true);

			if (result.success) {
				expect(result.data.client_id).toBe('550e8400-e29b-41d4-a716-446655440002');
			}
		});

		it('should validate valid vendor data with project scope', () => {
			const validData = {
				name: 'Project Vendor',
				project_id: '550e8400-e29b-41d4-a716-446655440003',
				vendor_type: 'Subcontractor',
			};

			const result = vendorSchema.safeParse(validData);
			expect(result.success).toBe(true);

			if (result.success) {
				expect(result.data.project_id).toBe('550e8400-e29b-41d4-a716-446655440003');
			}
		});

		it('should reject vendor data with no scope selected', () => {
			const invalidData = {
				name: 'Invalid Vendor',
				org_id: null,
				client_id: null,
				project_id: null,
			};

			const result = vendorSchema.safeParse(invalidData);
			expect(result.success).toBe(false);

			if (!result.success) {
				expect(result.error.issues[0].message).toBe(
					'Exactly one of organization, client, or project must be selected',
				);
			}
		});

		it('should reject vendor data with multiple scopes selected', () => {
			const invalidData = {
				name: 'Invalid Vendor',
				org_id: '550e8400-e29b-41d4-a716-446655440001',
				client_id: '550e8400-e29b-41d4-a716-446655440002',
				project_id: null,
			};

			const result = vendorSchema.safeParse(invalidData);
			expect(result.success).toBe(false);

			if (!result.success) {
				expect(result.error.issues[0].message).toBe(
					'Exactly one of organization, client, or project must be selected',
				);
			}
		});

		it('should reject vendor data with empty name', () => {
			const invalidData = {
				name: '',
				org_id: '550e8400-e29b-41d4-a716-446655440001',
			};

			const result = vendorSchema.safeParse(invalidData);
			expect(result.success).toBe(false);

			if (!result.success) {
				expect(
					result.error.issues.some((issue) => issue.message === 'Vendor name is required'),
				).toBe(true);
			}
		});

		it('should reject invalid email format', () => {
			const invalidData = {
				name: 'Test Vendor',
				org_id: '550e8400-e29b-41d4-a716-446655440001',
				contact_email: 'invalid-email',
			};

			const result = vendorSchema.safeParse(invalidData);
			expect(result.success).toBe(false);

			if (!result.success) {
				expect(
					result.error.issues.some(
						(issue) => issue.message === 'Please enter a valid email address',
					),
				).toBe(true);
			}
		});

		it('should reject invalid website URL', () => {
			const invalidData = {
				name: 'Test Vendor',
				org_id: '550e8400-e29b-41d4-a716-446655440001',
				website: 'not-a-url',
			};

			const result = vendorSchema.safeParse(invalidData);
			expect(result.success).toBe(false);

			if (!result.success) {
				expect(
					result.error.issues.some((issue) => issue.message === 'Please enter a valid URL'),
				).toBe(true);
			}
		});

		it('should validate optional fields with null values', () => {
			const validData = {
				name: 'Minimal Vendor',
				org_id: '550e8400-e29b-41d4-a716-446655440001',
				description: null,
				contact_name: null,
				contact_email: null,
				contact_phone: null,
				contact_address: null,
				website: null,
				vendor_type: null,
				tax_id: null,
				payment_terms: null,
				payment_terms_days: null,
				credit_limit: null,
				certification_info: null,
				insurance_info: null,
				additional_data: null,
			};

			const result = vendorSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should validate payment terms days within valid range', () => {
			const validData = {
				name: 'Test Vendor',
				org_id: '550e8400-e29b-41d4-a716-446655440001',
				payment_terms_days: 30,
			};

			const result = vendorSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should reject payment terms days outside valid range', () => {
			const invalidData = {
				name: 'Test Vendor',
				org_id: '550e8400-e29b-41d4-a716-446655440001',
				payment_terms_days: 400, // Over 365 days
			};

			const result = vendorSchema.safeParse(invalidData);
			expect(result.success).toBe(false);
		});

		it('should validate JSONB fields as record objects', () => {
			const validData = {
				name: 'Test Vendor',
				org_id: '550e8400-e29b-41d4-a716-446655440001',
				certification_info: {
					iso_9001: 'Valid until 2025',
					safety_cert: 'OSHA compliant',
				},
				insurance_info: {
					liability: '$1M',
					workers_comp: 'Active',
				},
				additional_data: {
					notes: 'Preferred vendor',
					rating: '5 stars',
				},
			};

			const result = vendorSchema.safeParse(validData);
			expect(result.success).toBe(true);

			if (result.success) {
				expect(result.data.certification_info).toEqual({
					iso_9001: 'Valid until 2025',
					safety_cert: 'OSHA compliant',
				});
			}
		});
	});

	describe('editVendorSchema', () => {
		it('should validate valid edit vendor data', () => {
			const validData = {
				vendor_id: '550e8400-e29b-41d4-a716-446655440000',
				name: 'Updated Vendor',
				description: 'Updated description',
				org_id: '550e8400-e29b-41d4-a716-446655440001',
				contact_name: 'Jane Doe',
				is_active: false,
			};

			const result = editVendorSchema.safeParse(validData);
			expect(result.success).toBe(true);

			if (result.success) {
				expect(result.data.vendor_id).toBe('550e8400-e29b-41d4-a716-446655440000');
				expect(result.data.name).toBe('Updated Vendor');
				expect(result.data.is_active).toBe(false);
			}
		});

		it('should require vendor_id for edit operations', () => {
			const invalidData = {
				name: 'Updated Vendor',
				org_id: '550e8400-e29b-41d4-a716-446655440001',
			};

			const result = editVendorSchema.safeParse(invalidData);
			expect(result.success).toBe(false);

			if (!result.success) {
				expect(result.error.issues.some((issue) => issue.path.includes('vendor_id'))).toBe(true);
			}
		});

		it('should enforce scope validation for edit operations', () => {
			const invalidData = {
				vendor_id: '550e8400-e29b-41d4-a716-446655440000',
				name: 'Updated Vendor',
				org_id: null,
				client_id: null,
				project_id: null,
			};

			const result = editVendorSchema.safeParse(invalidData);
			expect(result.success).toBe(false);

			if (!result.success) {
				expect(result.error.issues[0].message).toBe(
					'Exactly one of organization, client, or project must be selected',
				);
			}
		});
	});
});
