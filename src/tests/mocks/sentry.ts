import { vi } from 'vitest';

// Mock Sentry functions for testing
export const mockSentry = {
	init: vi.fn(),
	captureException: vi.fn(),
	captureMessage: vi.fn(),
	addBreadcrumb: vi.fn(),
	setUser: vi.fn(),
	setTag: vi.fn(),
	setContext: vi.fn(),
	configureScope: vi.fn(),
	withScope: vi.fn((callback) => callback(mockSentry)),
	startTransaction: vi.fn(() => ({
		setTag: vi.fn(),
		setData: vi.fn(),
		finish: vi.fn(),
	})),
	getCurrentHub: vi.fn(() => ({
		getClient: vi.fn(),
		getScope: vi.fn(),
	})),
	// SvelteKit specific mocks
	handleErrorWithSentry: vi.fn(() => vi.fn()),
	sentryHandle: vi.fn(() => vi.fn()),
	wrapServerLoadWithSentry: vi.fn((fn) => fn),
	wrapLoadWithSentry: vi.fn((fn) => fn),
	replayIntegration: vi.fn(() => ({})),
};

// Mock the entire @sentry/sveltekit module
export const createSentryMock = () => ({
	...mockSentry,
	// Export all the functions that might be imported
	default: mockSentry,
});

// Helper to reset all mocks
export const resetSentryMocks = () => {
	Object.values(mockSentry).forEach((mock) => {
		if (typeof mock === 'function' && 'mockReset' in mock) {
			mock.mockReset();
		}
	});
};
