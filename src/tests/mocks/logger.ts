import { vi } from 'vitest';
import type pino from 'pino';

// Create a mock logger that implements the pino Logger interface
export function createMockLogger(): pino.Logger {
	const mockLogger = {
		// Core logging methods
		trace: vi.fn(),
		debug: vi.fn(),
		info: vi.fn(),
		warn: vi.fn(),
		error: vi.fn(),
		fatal: vi.fn(),

		// Level checking methods
		isLevelEnabled: vi.fn().mockReturnValue(true),

		// Child logger creation
		child: vi.fn().mockImplementation((_bindings?: pino.Bindings) => {
			// Return a new mock logger instance for child loggers
			return createMockLogger();
		}),

		// Serializers and other properties
		serializers: {},
		level: 'info',
		levelVal: 30,

		// Additional pino methods that might be used
		silent: vi.fn(),
		flush: vi.fn(),
		bindings: vi.fn().mockReturnValue({}),
	} as unknown as pino.Logger;

	return mockLogger;
}

// Export a default mock logger instance
export const mockLogger = createMockLogger();

// Reset function to clear all mock calls
export function resetLoggerMocks() {
	vi.clearAllMocks();
}
