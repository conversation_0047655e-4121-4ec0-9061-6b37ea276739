import { vi } from 'vitest';

// Error type for mocks allowing arbitrary properties
interface MockError {
	message: string;
	[key: string]: unknown;
}

interface MockResponseData {
	data?: unknown;
	error?: MockError | null;
	[key: string]: unknown;
}

type MockResponse = Record<string, MockResponseData>;

// Mock Supabase client for testing
export const createMockSupabaseClient = (mockResponses: MockResponse = {}) => {
	return {
		auth: {
			getSession: vi.fn().mockResolvedValue(
				mockResponses.getSession || {
					data: { session: null },
					error: null,
				},
			),
			getUser: vi.fn().mockResolvedValue(
				mockResponses.getUser || {
					data: { user: null },
					error: null,
				},
			),
			signUp: vi.fn().mockResolvedValue(
				mockResponses.signUp || {
					data: {},
					error: null,
				},
			),
			signInWithPassword: vi.fn().mockResolvedValue(
				mockResponses.signInWithPassword || {
					data: {},
					error: null,
				},
			),
			signOut: vi.fn().mockResolvedValue(
				mockResponses.signOut || {
					error: null,
				},
			),
			resetPasswordForEmail: vi.fn().mockResolvedValue(
				mockResponses.resetPasswordForEmail || {
					data: {},
					error: null,
				},
			),
			updateUser: vi.fn().mockResolvedValue(
				mockResponses.updateUser || {
					data: { user: null },
					error: null,
				},
			),
			verifyOtp: vi.fn().mockResolvedValue(
				mockResponses.verifyOtp || {
					data: { user: null },
					error: null,
				},
			),
			refreshSession: vi.fn().mockResolvedValue(
				mockResponses.refreshSession || {
					data: { session: null },
					error: null,
				},
			),
		},
		from: vi.fn().mockImplementation((table: string) => {
			const tableData = mockResponses[table]?.data;
			const filteredData: Record<string, unknown>[] = Array.isArray(tableData)
				? (tableData as Record<string, unknown>[])
				: [];
			let filterConditions: Record<string, unknown> = {};

			// Create a query object with chainable methods
			// Define the type first to avoid self-reference issues
			type QueryType = {
				// Public query methods
				select: ReturnType<typeof vi.fn>;
				insert: ReturnType<typeof vi.fn>;
				update: ReturnType<typeof vi.fn>;
				delete: ReturnType<typeof vi.fn>;
				eq: ReturnType<typeof vi.fn>;
				match: ReturnType<typeof vi.fn>;
				single: ReturnType<typeof vi.fn>;
				order: ReturnType<typeof vi.fn>;
				limit: ReturnType<typeof vi.fn>;
				maybeSingle: ReturnType<typeof vi.fn>;
				then: ReturnType<typeof vi.fn>;
				// Internal state tracking properties
				_updateData?: Record<string, unknown>;
				_operation?: 'update' | 'insert' | 'delete';
				_eqColumn?: string;
				_eqValue?: unknown;
			};

			const query: QueryType = {
				select: vi.fn().mockImplementation(() => query),
				insert: vi.fn().mockImplementation(() => query),
				update: vi.fn().mockImplementation((updateData) => {
					// Store update operation data for later use with 'then'
					query._updateData = updateData;
					query._operation = 'update';
					return query;
				}),
				delete: vi.fn().mockImplementation(() => query),
				eq: vi.fn().mockImplementation((column, value) => {
					// Store eq condition for later use
					query._eqColumn = column;
					query._eqValue = value;
					return query;
				}),
				match: vi.fn().mockImplementation((conditions: Record<string, unknown>) => {
					filterConditions = conditions;
					return query;
				}),
				single: vi.fn().mockImplementation(() => query),
				order: vi.fn().mockImplementation(() => query),
				limit: vi.fn().mockImplementation(() => query),
				maybeSingle: vi.fn().mockImplementation(() => {
					// Apply filters based on match conditions
					if (filterConditions && Object.keys(filterConditions).length > 0) {
						const filtered = filteredData.filter((item: Record<string, unknown>) =>
							Object.entries(filterConditions).every(([key, value]) => item[key] === value),
						);

						return {
							then: <T>(callback: (response: { data: unknown; error: unknown }) => T) =>
								Promise.resolve(
									callback({
										data: filtered.length > 0 ? filtered[0] : null,
										error: null,
									}),
								),
						};
					}

					return {
						then: <T>(callback: (response: { data: unknown; error: unknown }) => T) =>
							Promise.resolve(
								callback({
									data: null,
									error: null,
								}),
							),
					};
				}),
				then: vi
					.fn()
					.mockImplementation(<T>(callback: (response: { data: unknown; error: unknown }) => T) => {
						// Check if this is an update operation
						if (query._operation === 'update' && query._eqColumn && query._eqValue) {
							// For update operations, look for a specific mock response like 'updateOrg'
							const updateKey = 'updateOrg';
							const resp = mockResponses[updateKey];
							if (resp) {
								return Promise.resolve(
									callback({ data: resp.data ?? null, error: resp.error ?? null }),
								);
							}
						}

						// Default behavior if no specific mock
						return Promise.resolve(
							callback({
								data: filteredData,
								error: null,
							}),
						);
					}),
			};

			return query;
		}),
		rpc: vi.fn().mockImplementation((func: string) => ({
			then: vi
				.fn()
				.mockImplementation(<T>(callback: (response: { data: unknown; error: unknown }) => T) => {
					const resp = mockResponses[func];
					return Promise.resolve(
						callback({ data: resp?.data ?? null, error: resp?.error ?? null }),
					);
				}),
		})),
	};
};

// Mock locals object used in SvelteKit hooks and server functions
export const createMockEventLocals = (mockResponses: MockResponse = {}) => {
	const supabaseMock = createMockSupabaseClient(mockResponses);

	// Extract session and user from mockResponses.getSession if available
	let session: unknown = null;
	let user: unknown = null;
	if (mockResponses.getSession) {
		type SessionWithUser = { user?: unknown; [key: string]: unknown };
		const data = mockResponses.getSession.data as
			| { session: SessionWithUser; user?: unknown }
			| undefined;
		if (data?.session) {
			const sessionObj = data.session;
			session = sessionObj;
			user = data.user ?? sessionObj.user ?? null;
		}
	}

	return {
		supabase: supabaseMock,
		getSession: vi
			.fn()
			.mockResolvedValue(mockResponses.getSession?.data || { session: null, user: null }),
		// Use explicitly provided values or fallback to ones extracted from getSession
		session: mockResponses.session || session,
		user: mockResponses.user || user,
		orgId: mockResponses.orgId || null,
	};
};
