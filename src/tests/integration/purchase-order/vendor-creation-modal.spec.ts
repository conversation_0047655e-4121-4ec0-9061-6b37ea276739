/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createVendorModal } from '$lib/components/forms/vendor/vendor_form_actions';
import { superValidate } from 'sveltekit-superforms/server';
import { createMockLogger } from '$tests/mocks/logger';

// Mock the auth module
vi.mock('$lib/server/auth', () => ({
	requireUser: vi.fn().mockResolvedValue({ user: { id: 'test-user-id' } }),
}));

// Mock sveltekit-superforms
vi.mock('sveltekit-superforms/server', () => ({
	superValidate: vi.fn(),
	message: vi.fn(),
}));

// Mock the adapter
vi.mock('sveltekit-superforms/adapters', () => ({
	zod4: vi.fn().mockImplementation((schema) => schema),
}));

describe('Vendor Creation Modal Action', () => {
	let mockSupabase;
	let mockRequest;
	let mockLocals;
	let mockCookies;

	beforeEach(() => {
		vi.clearAllMocks();

		// Mock Supabase client
		mockSupabase = {
			from: vi.fn().mockReturnValue({
				insert: vi.fn().mockReturnValue({
					select: vi.fn().mockReturnValue({
						single: vi.fn().mockResolvedValue({
							data: {
								vendor_id: 'new-vendor-id',
								name: 'Test Vendor',
								description: 'Test Description',
								vendor_type: 'Contractor',
								contact_name: 'John Doe',
								contact_email: '<EMAIL>',
								contact_phone: '************',
								is_active: true,
							},
							error: null,
						}),
					}),
				}),
			}),
		};

		mockLocals = {
			supabase: mockSupabase,
			log: createMockLogger(),
		};
		mockCookies = {};
		mockRequest = new Request('http://localhost', {
			method: 'POST',
			body: new FormData(),
		});

		// Mock superValidate to return valid form data
		superValidate.mockResolvedValue({
			valid: true,
			data: {
				name: 'Test Vendor',
				description: 'Test Description',
				vendor_type: 'Contractor',
				contact_name: 'John Doe',
				contact_email: '<EMAIL>',
				contact_phone: '************',
				is_active: true,
				org_id: null,
				client_id: null,
				project_id: 'test-project-id',
			},
		});
	});

	it('should create vendor and return data without redirecting', async () => {
		const result = await createVendorModal({
			request: mockRequest,
			locals: mockLocals,
			cookies: mockCookies,
		});

		// Verify the vendor was inserted
		expect(mockSupabase.from).toHaveBeenCalledWith('vendor');
		expect(mockSupabase.from().insert).toHaveBeenCalledWith({
			name: 'Test Vendor',
			description: 'Test Description',
			vendor_type: 'Contractor',
			contact_name: 'John Doe',
			contact_email: '<EMAIL>',
			contact_phone: '************',
			is_active: true,
			org_id: null,
			client_id: null,
			project_id: 'test-project-id',
			created_by_user_id: 'test-user-id',
		});

		// Verify the result contains vendor data
		expect(result).toHaveProperty('vendor');
		expect(result.vendor).toEqual({
			vendor_id: 'new-vendor-id',
			name: 'Test Vendor',
			description: 'Test Description',
			vendor_type: 'Contractor',
			contact_name: 'John Doe',
			contact_email: '<EMAIL>',
			contact_phone: '************',
			is_active: true,
			access_level: 'project',
		});
	});

	it('should handle validation errors', async () => {
		// Mock invalid form data
		superValidate.mockResolvedValue({
			valid: false,
			errors: { name: ['Name is required'] },
		});

		const result = await createVendorModal({
			request: mockRequest,
			locals: mockLocals,
			cookies: mockCookies,
		});

		// Should return fail with form errors
		expect(result).toHaveProperty('status', 400);
		expect(mockSupabase.from).not.toHaveBeenCalled();
	});

	it('should handle database errors', async () => {
		// Mock database error
		mockSupabase.from.mockReturnValue({
			insert: vi.fn().mockReturnValue({
				select: vi.fn().mockReturnValue({
					single: vi.fn().mockResolvedValue({
						data: null,
						error: { message: 'Database error' },
					}),
				}),
			}),
		});

		const result = await createVendorModal({
			request: mockRequest,
			locals: mockLocals,
			cookies: mockCookies,
		});

		// Should return fail with error message
		expect(result).toHaveProperty('status', 500);
		expect(result).toHaveProperty('data');
		expect(result.data).toHaveProperty('message');
		expect(result.data.message).toEqual({
			type: 'error',
			text: 'Failed to create vendor',
		});
	});

	it('should determine correct access level based on entity IDs', async () => {
		// Test organization level
		superValidate.mockResolvedValue({
			valid: true,
			data: {
				name: 'Org Vendor',
				org_id: 'test-org-id',
				client_id: null,
				project_id: null,
			},
		});

		let result = await createVendorModal({
			request: mockRequest,
			locals: mockLocals,
			cookies: mockCookies,
		});

		expect(result.vendor.access_level).toBe('organization');

		// Test client level
		superValidate.mockResolvedValue({
			valid: true,
			data: {
				name: 'Client Vendor',
				org_id: null,
				client_id: 'test-client-id',
				project_id: null,
			},
		});

		result = await createVendorModal({
			request: mockRequest,
			locals: mockLocals,
			cookies: mockCookies,
		});

		expect(result.vendor.access_level).toBe('client');
	});
});
