import { describe, it, expect, beforeEach } from 'vitest';
import { supabaseServiceClient } from '$lib/server/supabase_client';
import type { Database } from '$lib/database.types';

describe('Audit Logging System', () => {
	let supabase: typeof supabaseServiceClient;

	beforeEach(async () => {
		supabase = supabaseServiceClient;
	});

	describe('Audit Table Structure', () => {
		it('should have all required audit tables', async () => {
			// Test that all audit tables exist by querying their structure
			const auditTables = [
				'project_stage_audit',
				'gateway_checklist_item_audit',
				'project_gateway_stage_info_audit',
				'risk_register_audit',
			];

			for (const tableName of auditTables) {
				const { data, error } = await supabase
					.from(tableName as keyof Database['public']['Tables'])
					.select('audit_id')
					.limit(0);

				expect(error).toBeNull();
				expect(data).toBeDefined();
			}
		});

		it('should have correct audit table structure', async () => {
			// Test that audit tables have the expected columns
			const { data, error } = await supabase
				.from('project_stage_audit')
				.select('audit_id, operation_type, changed_by, changed_at, old_values, new_values')
				.limit(0);

			expect(error).toBeNull();
			expect(data).toBeDefined();
		});

		it('should verify audit system is properly configured', async () => {
			// This test verifies the audit system exists and is accessible
			// The actual functionality testing would require authenticated users
			// which is complex to set up in integration tests

			// Test that we can access audit tables (even if empty)
			const auditTables = [
				'project_stage_audit',
				'gateway_checklist_item_audit',
				'project_gateway_stage_info_audit',
				'risk_register_audit',
			];

			for (const tableName of auditTables) {
				const { data, error } = await supabase
					.from(tableName as keyof Database['public']['Tables'])
					.select('audit_id, operation_type, changed_at')
					.limit(1);

				// Should not error (table exists and is accessible)
				expect(error).toBeNull();
				expect(data).toBeDefined();
			}
		});
	});
});
