/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createMockSupabaseClient, createMockEventLocals } from '$tests/mocks/supabase';

// Sample hooks.server.ts implementation for testing session handling
const createSessionHandling = (supabase) => {
	// Mock function for handling server side auth
	const getSession = async () => {
		const { data, error } = await supabase.auth.getSession();
		if (error) throw error;
		return data.session;
	};

	// Mock function for client side session sync
	const handleClientSession = async (session) => {
		if (!session) {
			// Client-side session expired or doesn't exist
			return { user: null, session: null };
		}

		// Check if token is expired and needs refresh
		const isTokenExpired = session.expires_at * 1000 < Date.now();

		if (isTokenExpired) {
			const { data, error } = await supabase.auth.refreshSession();
			if (error) {
				// If refresh fails, session is invalid
				return { user: null, session: null };
			}
			return { user: data.user, session: data.session };
		}

		return { user: session.user, session };
	};

	return { getSession, handleClientSession };
};

describe('Session Persistence and Management', () => {
	beforeEach(() => {
		vi.resetAllMocks();
		// Reset timers if used
		vi.useFakeTimers();
	});

	afterEach(() => {
		vi.useRealTimers();
	});

	describe('Session initialization and retrieval', () => {
		it('should retrieve an active session when user is authenticated', async () => {
			// Mock user and session data
			const mockUser = { id: 'user123', email: '<EMAIL>' };
			const mockSession = {
				user: mockUser,
				access_token: 'valid-token',
				expires_at: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
			};

			// Create mock Supabase client
			const mockSupabase = createMockSupabaseClient({
				getSession: { data: { session: mockSession }, error: null },
			});

			const { getSession } = createSessionHandling(mockSupabase);

			// Test getSession
			const session = await getSession();

			// Verify session data
			expect(session).toEqual(mockSession);
			expect(session.user).toEqual(mockUser);
		});

		it('should return null session when user is not authenticated', async () => {
			// Create mock Supabase client with no session
			const mockSupabase = createMockSupabaseClient({
				getSession: { data: { session: null }, error: null },
			});

			const { getSession } = createSessionHandling(mockSupabase);

			// Test getSession
			const session = await getSession();

			// Verify session is null
			expect(session).toBeNull();
		});
	});

	describe('Session refresh handling', () => {
		it('should refresh token when expired', async () => {
			// Mock user and expired session data
			const mockUser = { id: 'user123', email: '<EMAIL>' };
			const expiredSession = {
				user: mockUser,
				access_token: 'expired-token',
				expires_at: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
			};

			// Mock refreshed session data
			const refreshedSession = {
				user: mockUser,
				access_token: 'new-token',
				expires_at: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
			};

			// Create mock Supabase client
			const mockSupabase = createMockSupabaseClient({
				refreshSession: { data: { session: refreshedSession, user: mockUser }, error: null },
			});

			const { handleClientSession } = createSessionHandling(mockSupabase);

			// Test handleClientSession with expired session
			const { user, session } = await handleClientSession(expiredSession);

			// Verify token was refreshed
			expect(session).toEqual(refreshedSession);
			expect(session.access_token).toBe('new-token');
			expect(user).toEqual(mockUser);
		});

		it('should handle failed token refresh', async () => {
			// Mock user and expired session data
			const mockUser = { id: 'user123', email: '<EMAIL>' };
			const expiredSession = {
				user: mockUser,
				access_token: 'expired-token',
				expires_at: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
			};

			// Create mock Supabase client that returns an error on refresh
			const mockSupabase = createMockSupabaseClient({
				refreshSession: {
					data: { session: null, user: null },
					error: { message: 'Invalid refresh token' },
				},
			});

			const { handleClientSession } = createSessionHandling(mockSupabase);

			// Test handleClientSession with expired session
			const { user, session } = await handleClientSession(expiredSession);

			// Verify failed refresh results in null session
			expect(session).toBeNull();
			expect(user).toBeNull();
		});
	});

	describe('Session persistence across requests', () => {
		it('should maintain session state for authenticated user', async () => {
			// Mock user and session data
			const mockUser = { id: 'user123', email: '<EMAIL>' };
			const mockSession = {
				user: mockUser,
				access_token: 'valid-token',
				expires_at: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
			};

			// Mock event locals
			const locals = createMockEventLocals({
				session: mockSession,
				user: mockUser,
			});

			// Simulate multiple requests using the same session
			for (let i = 0; i < 3; i++) {
				// Get session from locals
				const currentSession = locals.session;
				const currentUser = locals.user;

				// Verify session data persists
				expect(currentSession).toEqual(mockSession);
				expect(currentUser).toEqual(mockUser);
			}
		});
	});
});
