/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createMockSupabaseClient } from '../../../tests/mocks/supabase';

describe('Email Verification Integration Tests', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	// Create a mock version of the email verification handler
	const mockVerifyEmailHandler = async ({
		url,
		locals,
	}: {
		url: string;
		locals: { supabase: ReturnType<typeof createMockSupabaseClient> };
	}) => {
		// Extract token from URL
		const token = new URL(url).searchParams.get('token');
		const type = new URL(url).searchParams.get('type');

		if (!token || type !== 'email_verification') {
			return { success: false, error: 'Invalid verification link' };
		}

		// Simulate Supabase verifyOtp function
		const { data, error } = await locals.supabase.auth.verifyOtp({
			token_hash: token,
			type: 'email_verification',
		});

		if (error) {
			return { success: false, error: error.message };
		}

		return { success: true, data };
	};

	it('should verify email with valid token', async () => {
		// Mock Supabase client with successful verification
		const mockSupabase = createMockSupabaseClient({
			verifyOtp: {
				data: {
					user: {
						id: 'user123',
						email: '<EMAIL>',
						email_confirmed_at: new Date().toISOString(),
					},
				},
				error: null,
			},
		});

		const locals = { supabase: mockSupabase };

		// Mock valid verification URL
		const url = 'http://localhost:5173/auth/verify-email?token=valid-token&type=email_verification';

		// Call the handler
		const result = await mockVerifyEmailHandler({ url, locals });

		// Verify successful verification
		expect(result.success).toBe(true);
		expect(result.data.user).toBeDefined();
		expect(result.data.user.email_confirmed_at).toBeDefined();

		// Verify Supabase was called with correct parameters
		expect(mockSupabase.auth.verifyOtp).toHaveBeenCalledWith({
			token_hash: 'valid-token',
			type: 'email_verification',
		});
	});

	it('should handle invalid verification token', async () => {
		// Mock Supabase client with verification error
		const mockSupabase = createMockSupabaseClient({
			verifyOtp: {
				data: {},
				error: { message: 'Invalid token' },
			},
		});

		const locals = { supabase: mockSupabase };

		// Mock invalid verification URL
		const url =
			'http://localhost:5173/auth/verify-email?token=invalid-token&type=email_verification';

		// Call the handler
		const result = await mockVerifyEmailHandler({ url, locals });

		// Verify failed verification
		expect(result.success).toBe(false);
		expect(result.error).toBe('Invalid token');
	});

	it('should handle expired verification token', async () => {
		// Mock Supabase client with expired token error
		const mockSupabase = createMockSupabaseClient({
			verifyOtp: {
				data: {},
				error: { message: 'Token has expired' },
			},
		});

		const locals = { supabase: mockSupabase };

		// Mock expired verification URL
		const url =
			'http://localhost:5173/auth/verify-email?token=expired-token&type=email_verification';

		// Call the handler
		const result = await mockVerifyEmailHandler({ url, locals });

		// Verify failed verification
		expect(result.success).toBe(false);
		expect(result.error).toBe('Token has expired');
	});

	it('should handle missing token parameter', async () => {
		const mockSupabase = createMockSupabaseClient();
		const locals = { supabase: mockSupabase };

		// Mock URL with missing token
		const url = 'http://localhost:5173/auth/verify-email?type=email_verification';

		// Call the handler
		const result = await mockVerifyEmailHandler({ url, locals });

		// Verify error handling
		expect(result.success).toBe(false);
		expect(result.error).toBe('Invalid verification link');

		// Verify Supabase was not called
		expect(mockSupabase.auth.verifyOtp).not.toHaveBeenCalled();
	});

	it('should handle wrong verification type', async () => {
		const mockSupabase = createMockSupabaseClient();
		const locals = { supabase: mockSupabase };

		// Mock URL with wrong type
		const url = 'http://localhost:5173/auth/verify-email?token=valid-token&type=password_recovery';

		// Call the handler
		const result = await mockVerifyEmailHandler({ url, locals });

		// Verify error handling
		expect(result.success).toBe(false);
		expect(result.error).toBe('Invalid verification link');

		// Verify Supabase was not called
		expect(mockSupabase.auth.verifyOtp).not.toHaveBeenCalled();
	});
});
