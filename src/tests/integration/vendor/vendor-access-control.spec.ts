/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { createMockSupabaseClient } from '$tests/mocks/supabase';
import { createSentryMock } from '$tests/mocks/sentry';

// Mock Sentry before any other imports
vi.mock('@sentry/sveltekit', () => createSentryMock());

// Mock data for access control testing
const mockOrganizations = [
	{ org_id: 'org-1', name: 'Organization 1' },
	{ org_id: 'org-2', name: 'Organization 2' },
];

const mockClients = [
	{ client_id: 'client-1', name: 'Client 1', org_id: 'org-1' },
	{ client_id: 'client-2', name: 'Client 2', org_id: 'org-1' },
	{ client_id: 'client-3', name: 'Client 3', org_id: 'org-2' },
];

const mockProjects = [
	{ project_id: 'project-1', name: 'Project 1', client_id: 'client-1' },
	{ project_id: 'project-2', name: 'Project 2', client_id: 'client-1' },
	{ project_id: 'project-3', name: 'Project 3', client_id: 'client-2' },
	{ project_id: 'project-4', name: 'Project 4', client_id: 'client-3' },
];

const mockMemberships = [
	// Org admin has admin access to org-1
	{ user_id: 'org-admin', entity_id: 'org-1', entity_type: 'organization', role: 'admin' },
	// Client admin has admin access to client-1
	{ user_id: 'client-admin', entity_id: 'client-1', entity_type: 'client', role: 'admin' },
	// Project editor has editor access to project-1
	{ user_id: 'project-editor', entity_id: 'project-1', entity_type: 'project', role: 'editor' },
	// Project viewer has viewer access to project-2
	{ user_id: 'project-viewer', entity_id: 'project-2', entity_type: 'project', role: 'viewer' },
	// External user has no memberships
];

const mockVendors = [
	{
		vendor_id: 'vendor-org-1',
		name: 'Org Level Vendor 1',
		org_id: 'org-1',
		client_id: null,
		project_id: null,
		created_by_user_id: 'org-admin',
	},
	{
		vendor_id: 'vendor-client-1',
		name: 'Client Level Vendor 1',
		org_id: null,
		client_id: 'client-1',
		project_id: null,
		created_by_user_id: 'client-admin',
	},
	{
		vendor_id: 'vendor-project-1',
		name: 'Project Level Vendor 1',
		org_id: null,
		client_id: null,
		project_id: 'project-1',
		created_by_user_id: 'project-editor',
	},
	{
		vendor_id: 'vendor-org-2',
		name: 'Org Level Vendor 2',
		org_id: 'org-2',
		client_id: null,
		project_id: null,
		created_by_user_id: 'external-user',
	},
];

describe('Vendor Access Control Scenarios', () => {
	let mockSupabase: ReturnType<typeof createMockSupabaseClient>;

	beforeEach(() => {
		vi.clearAllMocks();
		mockSupabase = createMockSupabaseClient({
			vendor: { data: mockVendors },
			organization: { data: mockOrganizations },
			client: { data: mockClients },
			project: { data: mockProjects },
			membership: { data: mockMemberships },
		});
	});

	describe('Hierarchical Access Control', () => {
		it('should allow org admin to access all vendors in their organization hierarchy', async () => {
			// Mock RPC function to simulate hierarchical access
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { user_id_param, entity_type_param, entity_id_param } = params;

					if (
						user_id_param === 'org-admin' &&
						entity_type_param === 'organization' &&
						entity_id_param === 'org-1'
					) {
						// Org admin should see all vendors in org-1 hierarchy
						const accessibleVendors = mockVendors.filter(
							(v) =>
								v.org_id === 'org-1' ||
								mockClients.some((c) => c.org_id === 'org-1' && c.client_id === v.client_id) ||
								mockProjects.some((p) => {
									const client = mockClients.find((c) => c.client_id === p.client_id);
									return client?.org_id === 'org-1' && p.project_id === v.project_id;
								}),
						);

						return Promise.resolve({
							data: accessibleVendors.map((v) => ({
								vendor_id: v.vendor_id,
								name: v.name,
								access_level: v.project_id ? 'project' : v.client_id ? 'client' : 'organization',
							})),
							error: null,
						});
					}
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'org-admin',
				entity_type_param: 'organization',
				entity_id_param: 'org-1',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(3); // Should see org, client, and project level vendors
			expect(data.some((v) => v.access_level === 'organization')).toBe(true);
			expect(data.some((v) => v.access_level === 'client')).toBe(true);
			expect(data.some((v) => v.access_level === 'project')).toBe(true);
		});

		it('should allow client admin to access client and org level vendors', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { user_id_param, entity_type_param, entity_id_param } = params;

					if (
						user_id_param === 'client-admin' &&
						entity_type_param === 'client' &&
						entity_id_param === 'client-1'
					) {
						// Client admin should see client-1 vendors and org-1 vendors
						const accessibleVendors = mockVendors.filter(
							(v) => v.client_id === 'client-1' || v.org_id === 'org-1',
						);

						return Promise.resolve({
							data: accessibleVendors.map((v) => ({
								vendor_id: v.vendor_id,
								name: v.name,
								access_level: v.client_id ? 'client' : 'organization',
							})),
							error: null,
						});
					}
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'client-admin',
				entity_type_param: 'client',
				entity_id_param: 'client-1',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(2); // Should see org and client level vendors
			expect(data.some((v) => v.access_level === 'organization')).toBe(true);
			expect(data.some((v) => v.access_level === 'client')).toBe(true);
			expect(data.some((v) => v.access_level === 'project')).toBe(false);
		});

		it('should allow project editor to access project, client, and org level vendors', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { user_id_param, entity_type_param, entity_id_param } = params;

					if (
						user_id_param === 'project-editor' &&
						entity_type_param === 'project' &&
						entity_id_param === 'project-1'
					) {
						// Project editor should see project-1, client-1, and org-1 vendors
						const accessibleVendors = mockVendors.filter(
							(v) =>
								v.project_id === 'project-1' || v.client_id === 'client-1' || v.org_id === 'org-1',
						);

						return Promise.resolve({
							data: accessibleVendors.map((v) => ({
								vendor_id: v.vendor_id,
								name: v.name,
								access_level: v.project_id ? 'project' : v.client_id ? 'client' : 'organization',
							})),
							error: null,
						});
					}
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'project-editor',
				entity_type_param: 'project',
				entity_id_param: 'project-1',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(3); // Should see all three levels
			expect(data.some((v) => v.access_level === 'organization')).toBe(true);
			expect(data.some((v) => v.access_level === 'client')).toBe(true);
			expect(data.some((v) => v.access_level === 'project')).toBe(true);
		});
	});

	describe('Permission-Based Creation Rights', () => {
		it('should allow org admin to create vendors at organization level', async () => {
			const newVendor = {
				name: 'New Org Vendor',
				org_id: 'org-1',
				client_id: null,
				project_id: null,
				created_by_user_id: 'org-admin',
			};

			// Check if user has admin role for org-1
			const userMembership = mockMemberships.find(
				(m) =>
					m.user_id === 'org-admin' &&
					m.entity_id === 'org-1' &&
					m.entity_type === 'organization' &&
					m.role === 'admin',
			);

			expect(userMembership).toBeDefined();
			expect(userMembership?.role).toBe('admin');

			// Mock successful insert
			mockSupabase.from = vi.fn().mockReturnValue({
				insert: vi.fn().mockReturnValue({
					select: vi.fn().mockResolvedValue({
						data: [{ ...newVendor, vendor_id: 'new-vendor-1' }],
						error: null,
					}),
				}),
			});

			const { data, error } = await mockSupabase.from('vendor').insert(newVendor).select();

			expect(error).toBeNull();
			expect(data).toHaveLength(1);
			expect(data[0].org_id).toBe('org-1');
		});

		it('should allow client admin to create vendors at client level', async () => {
			const newVendor = {
				name: 'New Client Vendor',
				org_id: null,
				client_id: 'client-1',
				project_id: null,
				created_by_user_id: 'client-admin',
			};

			// Check if user has admin role for client-1
			const userMembership = mockMemberships.find(
				(m) =>
					m.user_id === 'client-admin' &&
					m.entity_id === 'client-1' &&
					m.entity_type === 'client' &&
					m.role === 'admin',
			);

			expect(userMembership).toBeDefined();
			expect(userMembership?.role).toBe('admin');

			// Mock successful insert
			mockSupabase.from = vi.fn().mockReturnValue({
				insert: vi.fn().mockReturnValue({
					select: vi.fn().mockResolvedValue({
						data: [{ ...newVendor, vendor_id: 'new-vendor-2' }],
						error: null,
					}),
				}),
			});

			const { data, error } = await mockSupabase.from('vendor').insert(newVendor).select();

			expect(error).toBeNull();
			expect(data).toHaveLength(1);
			expect(data[0].client_id).toBe('client-1');
		});

		it('should allow project editor to create vendors at project level', async () => {
			const newVendor = {
				name: 'New Project Vendor',
				org_id: null,
				client_id: null,
				project_id: 'project-1',
				created_by_user_id: 'project-editor',
			};

			// Check if user has editor or admin role for project-1
			const userMembership = mockMemberships.find(
				(m) =>
					m.user_id === 'project-editor' &&
					m.entity_id === 'project-1' &&
					m.entity_type === 'project' &&
					(m.role === 'editor' || m.role === 'admin'),
			);

			expect(userMembership).toBeDefined();
			expect(userMembership?.role).toBe('editor');

			// Mock successful insert
			mockSupabase.from = vi.fn().mockReturnValue({
				insert: vi.fn().mockReturnValue({
					select: vi.fn().mockResolvedValue({
						data: [{ ...newVendor, vendor_id: 'new-vendor-3' }],
						error: null,
					}),
				}),
			});

			const { data, error } = await mockSupabase.from('vendor').insert(newVendor).select();

			expect(error).toBeNull();
			expect(data).toHaveLength(1);
			expect(data[0].project_id).toBe('project-1');
		});

		it('should reject vendor creation by project viewer', async () => {
			const newVendor = {
				name: 'Unauthorized Vendor',
				org_id: null,
				client_id: null,
				project_id: 'project-2',
				created_by_user_id: 'project-viewer',
			};

			// Check user role - should be viewer, not editor/admin
			const userMembership = mockMemberships.find(
				(m) =>
					m.user_id === 'project-viewer' &&
					m.entity_id === 'project-2' &&
					m.entity_type === 'project',
			);

			expect(userMembership?.role).toBe('viewer'); // Not sufficient for creation

			// Mock RLS rejection
			mockSupabase.from = vi.fn().mockReturnValue({
				insert: vi.fn().mockReturnValue({
					select: vi.fn().mockResolvedValue({
						data: null,
						error: { message: 'new row violates row-level security policy' },
					}),
				}),
			});

			const { data, error } = await mockSupabase.from('vendor').insert(newVendor).select();

			expect(error).not.toBeNull();
			expect(error.message).toContain('row-level security policy');
			expect(data).toBeNull();
		});

		it('should reject vendor creation by external user', async () => {
			const newVendor = {
				name: 'External Vendor',
				org_id: 'org-1',
				client_id: null,
				project_id: null,
				created_by_user_id: 'external-user',
			};

			// Check user has no memberships in org-1
			const userMembership = mockMemberships.find(
				(m) => m.user_id === 'external-user' && m.entity_id === 'org-1',
			);

			expect(userMembership).toBeUndefined(); // No access

			// Mock RLS rejection
			mockSupabase.from = vi.fn().mockReturnValue({
				insert: vi.fn().mockReturnValue({
					select: vi.fn().mockResolvedValue({
						data: null,
						error: { message: 'new row violates row-level security policy' },
					}),
				}),
			});

			const { data, error } = await mockSupabase.from('vendor').insert(newVendor).select();

			expect(error).not.toBeNull();
			expect(error.message).toContain('row-level security policy');
			expect(data).toBeNull();
		});
	});

	describe('Cross-Organization Isolation', () => {
		it('should prevent access to vendors from different organizations', async () => {
			// User from org-1 trying to access vendors from org-2
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { user_id_param, entity_type_param, entity_id_param } = params;

					if (
						user_id_param === 'org-admin' &&
						entity_type_param === 'organization' &&
						entity_id_param === 'org-2'
					) {
						// Should return empty - no access to org-2
						return Promise.resolve({ data: [], error: null });
					}
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'org-admin',
				entity_type_param: 'organization',
				entity_id_param: 'org-2',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(0); // No access to org-2 vendors
		});

		it('should prevent cross-project vendor access within same client', async () => {
			// Project editor from project-1 should not see project-2 specific vendors
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { user_id_param, entity_type_param, entity_id_param } = params;

					if (
						user_id_param === 'project-editor' &&
						entity_type_param === 'project' &&
						entity_id_param === 'project-2'
					) {
						// Should return empty - no access to project-2
						return Promise.resolve({ data: [], error: null });
					}
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'project-editor',
				entity_type_param: 'project',
				entity_id_param: 'project-2',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(0); // No access to project-2 vendors
		});
	});

	describe('Edge Cases and Error Scenarios', () => {
		it('should handle invalid entity IDs gracefully', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation(() => {
				return Promise.resolve({
					data: null,
					error: { message: 'Entity not found' },
				});
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'org-admin',
				entity_type_param: 'organization',
				entity_id_param: 'invalid-org-id',
			});

			expect(error).not.toBeNull();
			expect(error.message).toBe('Entity not found');
			expect(data).toBeNull();
		});

		it('should handle vendors with inconsistent hierarchy data', async () => {
			// Test vendor with multiple scope IDs (should be prevented by constraints)
			const invalidVendor = {
				name: 'Invalid Vendor',
				org_id: 'org-1',
				client_id: 'client-1', // Should not have both org_id and client_id
				project_id: null,
				created_by_user_id: 'org-admin',
			};

			// Mock constraint violation
			mockSupabase.from = vi.fn().mockReturnValue({
				insert: vi.fn().mockReturnValue({
					select: vi.fn().mockResolvedValue({
						data: null,
						error: { message: 'check constraint "vendor_scope_check" violated' },
					}),
				}),
			});

			const { data, error } = await mockSupabase.from('vendor').insert(invalidVendor).select();

			expect(error).not.toBeNull();
			expect(error.message).toContain('check constraint');
			expect(data).toBeNull();
		});

		it('should handle user with no memberships', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation(() => {
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'external-user',
				entity_type_param: 'organization',
				entity_id_param: 'org-1',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(0); // No access for user with no memberships
		});

		it('should handle deleted/inactive entities gracefully', async () => {
			// Test accessing vendors for a deleted organization
			mockSupabase.rpc = vi.fn().mockImplementation(() => {
				return Promise.resolve({
					data: null,
					error: { message: 'Organization not found or inactive' },
				});
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'org-admin',
				entity_type_param: 'organization',
				entity_id_param: 'deleted-org-id',
			});

			expect(error).not.toBeNull();
			expect(error.message).toContain('not found or inactive');
			expect(data).toBeNull();
		});
	});

	describe('Vendor Scope Validation', () => {
		it('should validate that vendor belongs to accessible hierarchy', async () => {
			// Test that a vendor's scope is properly validated against user access
			const vendorInDifferentOrg = mockVendors.find((v) => v.vendor_id === 'vendor-org-2');
			expect(vendorInDifferentOrg?.org_id).toBe('org-2');

			// User from org-1 should not be able to access vendor from org-2
			mockSupabase.from = vi.fn().mockReturnValue({
				select: vi.fn().mockReturnValue({
					eq: vi.fn().mockResolvedValue({
						data: [], // RLS filters out inaccessible vendors
						error: null,
					}),
				}),
			});

			const { data, error } = await mockSupabase
				.from('vendor')
				.select('*')
				.eq('vendor_id', 'vendor-org-2');

			expect(error).toBeNull();
			expect(data).toHaveLength(0); // Should be filtered out by RLS
		});

		it('should ensure vendor creation respects hierarchy constraints', async () => {
			// Test creating a vendor at client level when user only has project access
			const invalidVendor = {
				name: 'Invalid Scope Vendor',
				org_id: null,
				client_id: 'client-1',
				project_id: null,
				created_by_user_id: 'project-editor', // Only has project-1 access
			};

			// Mock RLS rejection - project editor cannot create client-level vendors
			mockSupabase.from = vi.fn().mockReturnValue({
				insert: vi.fn().mockReturnValue({
					select: vi.fn().mockResolvedValue({
						data: null,
						error: { message: 'insufficient privileges for client-level vendor creation' },
					}),
				}),
			});

			const { data, error } = await mockSupabase.from('vendor').insert(invalidVendor).select();

			expect(error).not.toBeNull();
			expect(error.message).toContain('insufficient privileges');
			expect(data).toBeNull();
		});
	});
});
