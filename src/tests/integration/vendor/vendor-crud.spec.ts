/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { createMockSupabaseClient } from '$tests/mocks/supabase';
import { createSentryMock } from '$tests/mocks/sentry';

// Mock Sentry before any other imports
vi.mock('@sentry/sveltekit', () => createSentryMock());

// Mock vendor data for testing
const mockOrganizations = [
	{ org_id: 'org-1', name: 'Test Organization 1' },
	{ org_id: 'org-2', name: 'Test Organization 2' },
];

const mockClients = [
	{ client_id: 'client-1', name: 'Test Client 1', org_id: 'org-1' },
	{ client_id: 'client-2', name: 'Test Client 2', org_id: 'org-2' },
];

const mockProjects = [
	{ project_id: 'project-1', name: 'Test Project 1', client_id: 'client-1' },
	{ project_id: 'project-2', name: 'Test Project 2', client_id: 'client-2' },
];

const mockVendors = [
	{
		vendor_id: 'vendor-1',
		name: 'Org Level Vendor',
		description: 'Organization level vendor',
		org_id: 'org-1',
		client_id: null,
		project_id: null,
		contact_name: 'John Doe',
		contact_email: '<EMAIL>',
		vendor_type: 'contractor',
		is_active: true,
		created_by_user_id: 'user-1',
	},
	{
		vendor_id: 'vendor-2',
		name: 'Client Level Vendor',
		description: 'Client level vendor',
		org_id: null,
		client_id: 'client-1',
		project_id: null,
		contact_name: 'Jane Smith',
		contact_email: '<EMAIL>',
		vendor_type: 'supplier',
		is_active: true,
		created_by_user_id: 'user-1',
	},
	{
		vendor_id: 'vendor-3',
		name: 'Project Level Vendor',
		description: 'Project level vendor',
		org_id: null,
		client_id: null,
		project_id: 'project-1',
		contact_name: 'Bob Johnson',
		contact_email: '<EMAIL>',
		vendor_type: 'subcontractor',
		is_active: true,
		created_by_user_id: 'user-1',
	},
];

const mockMemberships = [
	// User 1 is admin of org-1
	{ user_id: 'user-1', entity_id: 'org-1', entity_type: 'organization', role: 'admin' },
	// User 1 is admin of client-1 (under org-1)
	{ user_id: 'user-1', entity_id: 'client-1', entity_type: 'client', role: 'admin' },
	// User 1 is editor of project-1 (under client-1)
	{ user_id: 'user-1', entity_id: 'project-1', entity_type: 'project', role: 'editor' },
	// User 2 is only viewer of org-2
	{ user_id: 'user-2', entity_id: 'org-2', entity_type: 'organization', role: 'viewer' },
];

describe('Vendor CRUD Operations Integration', () => {
	let mockSupabase: ReturnType<typeof createMockSupabaseClient>;

	beforeEach(() => {
		vi.clearAllMocks();
		mockSupabase = createMockSupabaseClient({
			vendor: { data: mockVendors },
			organization: { data: mockOrganizations },
			client: { data: mockClients },
			project: { data: mockProjects },
			membership: { data: mockMemberships },
		});
	});

	describe('Vendor Creation', () => {
		it('should allow admin to create organization-level vendor', async () => {
			const newVendor = {
				name: 'New Org Vendor',
				description: 'A new organization vendor',
				org_id: 'org-1',
				client_id: null,
				project_id: null,
				vendor_type: 'contractor',
				created_by_user_id: 'user-1',
			};

			// Mock the insert operation
			mockSupabase.from = vi.fn().mockReturnValue({
				insert: vi.fn().mockReturnValue({
					select: vi.fn().mockResolvedValue({
						data: [{ ...newVendor, vendor_id: 'new-vendor-1' }],
						error: null,
					}),
				}),
			});

			// Simulate RLS policy check - user-1 is admin of org-1
			const userRole = mockMemberships.find(
				(m) =>
					m.user_id === 'user-1' && m.entity_id === 'org-1' && m.entity_type === 'organization',
			)?.role;

			expect(userRole).toBe('admin');

			const { data, error } = await mockSupabase.from('vendor').insert(newVendor).select();

			expect(error).toBeNull();
			expect(data).toHaveLength(1);
			expect(data[0].name).toBe('New Org Vendor');
			expect(data[0].org_id).toBe('org-1');
		});

		it('should allow admin to create client-level vendor', async () => {
			const newVendor = {
				name: 'New Client Vendor',
				org_id: null,
				client_id: 'client-1',
				project_id: null,
				created_by_user_id: 'user-1',
			};

			// Mock the insert operation
			mockSupabase.from = vi.fn().mockReturnValue({
				insert: vi.fn().mockReturnValue({
					select: vi.fn().mockResolvedValue({
						data: [{ ...newVendor, vendor_id: 'new-vendor-2' }],
						error: null,
					}),
				}),
			});

			// Simulate RLS policy check - user-1 is admin of client-1
			const userRole = mockMemberships.find(
				(m) => m.user_id === 'user-1' && m.entity_id === 'client-1' && m.entity_type === 'client',
			)?.role;

			expect(userRole).toBe('admin');

			const { data, error } = await mockSupabase.from('vendor').insert(newVendor).select();

			expect(error).toBeNull();
			expect(data).toHaveLength(1);
			expect(data[0].client_id).toBe('client-1');
		});

		it('should allow editor to create project-level vendor', async () => {
			const newVendor = {
				name: 'New Project Vendor',
				org_id: null,
				client_id: null,
				project_id: 'project-1',
				created_by_user_id: 'user-1',
			};

			// Mock the insert operation
			mockSupabase.from = vi.fn().mockReturnValue({
				insert: vi.fn().mockReturnValue({
					select: vi.fn().mockResolvedValue({
						data: [{ ...newVendor, vendor_id: 'new-vendor-3' }],
						error: null,
					}),
				}),
			});

			// Simulate RLS policy check - user-1 is editor of project-1
			const userRole = mockMemberships.find(
				(m) => m.user_id === 'user-1' && m.entity_id === 'project-1' && m.entity_type === 'project',
			)?.role;

			expect(userRole).toBe('editor');

			const { data, error } = await mockSupabase.from('vendor').insert(newVendor).select();

			expect(error).toBeNull();
			expect(data).toHaveLength(1);
			expect(data[0].project_id).toBe('project-1');
		});

		it('should reject vendor creation by user without proper permissions', async () => {
			const newVendor = {
				name: 'Unauthorized Vendor',
				org_id: 'org-2',
				created_by_user_id: 'user-2', // user-2 is only viewer of org-2
			};

			// Mock the insert operation to simulate RLS rejection
			mockSupabase.from = vi.fn().mockReturnValue({
				insert: vi.fn().mockReturnValue({
					select: vi.fn().mockResolvedValue({
						data: null,
						error: { message: 'new row violates row-level security policy' },
					}),
				}),
			});

			// Simulate RLS policy check - user-2 is only viewer of org-2
			const userRole = mockMemberships.find(
				(m) =>
					m.user_id === 'user-2' && m.entity_id === 'org-2' && m.entity_type === 'organization',
			)?.role;

			expect(userRole).toBe('viewer'); // Not admin, should fail

			const { data, error } = await mockSupabase.from('vendor').insert(newVendor).select();

			expect(error).not.toBeNull();
			expect(error.message).toContain('row-level security policy');
			expect(data).toBeNull();
		});
	});

	describe('Vendor Reading/Access', () => {
		it('should return vendors accessible to user based on hierarchy', async () => {
			// Mock the RPC function call
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { entity_type_param, entity_id_param } = params;

					// Simulate the RPC function logic
					let accessibleVendors = [];

					if (entity_type_param === 'project' && entity_id_param === 'project-1') {
						// For project-1, return project, client, and org level vendors
						accessibleVendors = mockVendors.filter(
							(v) =>
								v.project_id === 'project-1' || v.client_id === 'client-1' || v.org_id === 'org-1',
						);
					}

					return Promise.resolve({
						data: accessibleVendors.map((v) => ({
							vendor_id: v.vendor_id,
							name: v.name,
							description: v.description,
							vendor_type: v.vendor_type,
							contact_name: v.contact_name,
							contact_email: v.contact_email,
							contact_phone: v.contact_phone,
							is_active: v.is_active,
							access_level: v.project_id ? 'project' : v.client_id ? 'client' : 'organization',
						})),
						error: null,
					});
				}
				return Promise.resolve({ data: null, error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'user-1',
				entity_type_param: 'project',
				entity_id_param: 'project-1',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(3); // Should return all 3 vendors (org, client, project level)

			const accessLevels = data.map((v) => v.access_level);
			expect(accessLevels).toContain('organization');
			expect(accessLevels).toContain('client');
			expect(accessLevels).toContain('project');
		});

		it('should only return organization-level vendors for organization scope', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { entity_type_param, entity_id_param } = params;

					if (entity_type_param === 'organization' && entity_id_param === 'org-1') {
						const accessibleVendors = mockVendors.filter((v) => v.org_id === 'org-1');
						return Promise.resolve({
							data: accessibleVendors.map((v) => ({
								vendor_id: v.vendor_id,
								name: v.name,
								access_level: 'organization',
							})),
							error: null,
						});
					}
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'user-1',
				entity_type_param: 'organization',
				entity_id_param: 'org-1',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(1);
			expect(data[0].access_level).toBe('organization');
		});
	});

	describe('Vendor Updates', () => {
		it('should allow updating vendor by user with proper permissions', async () => {
			const updatedVendor = {
				vendor_id: 'vendor-1',
				name: 'Updated Org Vendor',
				description: 'Updated description',
			};

			// Mock the update operation
			mockSupabase.from = vi.fn().mockReturnValue({
				update: vi.fn().mockReturnValue({
					eq: vi.fn().mockReturnValue({
						select: vi.fn().mockResolvedValue({
							data: [{ ...mockVendors[0], ...updatedVendor }],
							error: null,
						}),
					}),
				}),
			});

			const { data, error } = await mockSupabase
				.from('vendor')
				.update(updatedVendor)
				.eq('vendor_id', 'vendor-1')
				.select();

			expect(error).toBeNull();
			expect(data).toHaveLength(1);
			expect(data[0].name).toBe('Updated Org Vendor');
		});

		it('should reject vendor updates by unauthorized users', async () => {
			const updatedVendor = {
				name: 'Unauthorized Update',
			};

			// Mock the update operation to simulate RLS rejection
			mockSupabase.from = vi.fn().mockReturnValue({
				update: vi.fn().mockReturnValue({
					eq: vi.fn().mockReturnValue({
						select: vi.fn().mockResolvedValue({
							data: null,
							error: { message: 'update violates row-level security policy' },
						}),
					}),
				}),
			});

			const { data, error } = await mockSupabase
				.from('vendor')
				.update(updatedVendor)
				.eq('vendor_id', 'vendor-1')
				.select();

			expect(error).not.toBeNull();
			expect(error.message).toContain('row-level security policy');
			expect(data).toBeNull();
		});
	});

	describe('Vendor Deletion', () => {
		it('should allow deleting vendor by user with proper permissions', async () => {
			// Mock the delete operation
			mockSupabase.from = vi.fn().mockReturnValue({
				delete: vi.fn().mockReturnValue({
					eq: vi.fn().mockResolvedValue({
						data: [{ vendor_id: 'vendor-1' }],
						error: null,
					}),
				}),
			});

			const { data, error } = await mockSupabase
				.from('vendor')
				.delete()
				.eq('vendor_id', 'vendor-1');

			expect(error).toBeNull();
			expect(data).toHaveLength(1);
		});

		it('should reject vendor deletion by unauthorized users', async () => {
			// Mock the delete operation to simulate RLS rejection
			mockSupabase.from = vi.fn().mockReturnValue({
				delete: vi.fn().mockReturnValue({
					eq: vi.fn().mockResolvedValue({
						data: null,
						error: { message: 'delete violates row-level security policy' },
					}),
				}),
			});

			const { data, error } = await mockSupabase
				.from('vendor')
				.delete()
				.eq('vendor_id', 'vendor-1');

			expect(error).not.toBeNull();
			expect(error.message).toContain('row-level security policy');
			expect(data).toBeNull();
		});
	});

	describe('Cross-Organization Isolation', () => {
		it('should not allow access to vendors from different organizations', async () => {
			// Mock query that simulates RLS filtering
			mockSupabase.from = vi.fn().mockReturnValue({
				select: vi.fn().mockReturnValue({
					eq: vi.fn().mockResolvedValue({
						data: [], // RLS should filter out vendors from other orgs
						error: null,
					}),
				}),
			});

			// User-1 trying to access vendor from org-2 (they only have access to org-1)
			const { data, error } = await mockSupabase.from('vendor').select('*').eq('org_id', 'org-2');

			expect(error).toBeNull();
			expect(data).toHaveLength(0); // Should return empty due to RLS
		});

		it('should enforce hierarchy-based access control', async () => {
			// Test that project-level users can see org and client level vendors
			// but org-level users cannot see project-specific vendors from other projects
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { entity_type_param, entity_id_param } = params;

					if (entity_type_param === 'client' && entity_id_param === 'client-1') {
						// Client-level access should return client and org level vendors
						const accessibleVendors = mockVendors.filter(
							(v) => v.client_id === 'client-1' || v.org_id === 'org-1',
						);
						return Promise.resolve({
							data: accessibleVendors.map((v) => ({
								vendor_id: v.vendor_id,
								name: v.name,
								access_level: v.client_id ? 'client' : 'organization',
							})),
							error: null,
						});
					}
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'user-1',
				entity_type_param: 'client',
				entity_id_param: 'client-1',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(2); // Should return org and client level vendors
			expect(data.some((v) => v.access_level === 'organization')).toBe(true);
			expect(data.some((v) => v.access_level === 'client')).toBe(true);
			expect(data.some((v) => v.access_level === 'project')).toBe(false); // No project vendors
		});
	});
});
