/// <reference types="@vitest/browser/matchers" />
/// <reference types="@vitest/browser/providers/playwright" />

import { vi, beforeAll, afterEach } from 'vitest';
import { createSentryMock, resetSentryMocks } from './tests/mocks/sentry';

// Mock Sentry before any imports
vi.mock('@sentry/sveltekit', () => createSentryMock());

// Mock environment variables that <PERSON><PERSON> might use
vi.mock('$app/environment', () => ({
	dev: true,
	building: false,
	version: 'test',
}));

// Prevent Sentry from being initialized during tests
beforeAll(() => {
	// Set NODE_ENV to test to disable Sen<PERSON>
	if (typeof process !== 'undefined') {
		process.env.NODE_ENV = 'test';
	}

	// Mock any global Sentry initialization
	if (typeof globalThis !== 'undefined') {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(globalThis as any).__SENTRY__ = undefined;
	}
});

// Reset Sentry mocks after each test
afterEach(() => {
	resetSentryMocks();
});
