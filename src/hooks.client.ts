import { replayIntegration } from '@sentry/sveltekit';
import * as Sentry from '@sentry/sveltekit';
import { PUBLIC_VERCEL_ENV } from '$env/static/public';
import { dev } from '$app/environment';
import type { HandleClientError } from '@sveltejs/kit';

Sentry.init({
	dsn: 'https://<EMAIL>/4509638125944912',
	environment: PUBLIC_VERCEL_ENV,
	enabled: !dev,

	tracesSampleRate: 1.0,

	// This sets the sample rate to be 10%. You may want this to be 100% while
	// in development and sample at a lower rate in production
	replaysSessionSampleRate: 0.1,

	// If the entire session is not sampled, use the below sample rate to sample
	// sessions when an error occurs.
	replaysOnErrorSampleRate: 1.0,

	// If you don't want to use Session Replay, just remove the line below:
	integrations: [replayIntegration()],
});

export const handleError: HandleClientError = async ({ error, event, status, message }) => {
	const errorId = crypto.randomUUID();

	Sentry.captureException(error, {
		extra: { event, errorId, status },
	});

	return {
		message,
		errorId,
	};
};
