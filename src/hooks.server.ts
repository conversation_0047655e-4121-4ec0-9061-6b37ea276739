import * as Sentry from '@sentry/sveltekit';
import { createServerClient } from '@supabase/ssr';
import { redirect, type Handle, type HandleServerError } from '@sveltejs/kit';
import { PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY } from '$env/static/public';
import { sequence } from '@sveltejs/kit/hooks';
import { randomUUID } from 'node:crypto';
import { als } from '$lib/server/request-context';
import { logger } from '$lib/server/logger';
import { ORG_COOKIE_NAME } from '$lib/current-org.svelte';
import { env } from '$env/dynamic/private';
import { dev } from '$app/environment';
import type { Database } from '$lib/database.types';

Sentry.init({
	dsn: 'https://<EMAIL>/4509638125944912',
	environment: env.VERCEL_ENV ?? 'development',
	enabled: !dev,

	tracesSampleRate: 1,
});

const supabase: Handle = async ({ event, resolve }) => {
	event.locals.supabase = createServerClient<Database>(
		PUBLIC_SUPABASE_URL,
		PUBLIC_SUPABASE_ANON_KEY,
		{
			cookies: {
				getAll: () => {
					return event.cookies.getAll();
				},
				/**
				 * SvelteKit's cookies API requires `path` to be explicitly set in
				 * the cookie options. Setting `path` to `/` replicates previous/
				 * standard behavior.
				 */
				setAll: (cookies) => {
					cookies.forEach(({ name, value, options }) => {
						event.cookies.set(name, value, { ...options, path: '/' });
					});
				},
			},
		},
	);

	/**
	 * a little helper that is written for convenience so that instead
	 * of calling `const { data: { session } } = await supabase.auth.getSession()`
	 * you just call this `await getSession()`
	 */
	event.locals.getSession = async () => {
		const {
			data: { session },
		} = await event.locals.supabase.auth.getSession();
		if (!session) {
			return { session: null, user: null };
		}
		const {
			data: { user },
			error,
		} = await event.locals.supabase.auth.getUser();
		if (error) {
			// JWT validation has failed
			return { session: null, user: null };
		}

		return { session, user };
	};

	return resolve(event, {
		/**
		 * There´s an issue with `filterSerializedResponseHeaders` not working when using `sequence`
		 *
		 * https://github.com/sveltejs/kit/issues/8061
		 */
		filterSerializedResponseHeaders(name) {
			return name === 'content-range';
		},
	});
};

// Add the organization ID from cookies to event.locals
const orgContext: Handle = async ({ event, resolve }) => {
	// Get the organization ID from cookies
	const orgId = event.cookies.get(ORG_COOKIE_NAME);
	event.locals.orgId = orgId ?? null;

	return resolve(event);
};

const authGuard: Handle = async ({ event, resolve }) => {
	const { session, user } = await event.locals.getSession();
	event.locals.session = session;
	event.locals.user = user;

	if (event.locals.session && event.url.pathname.startsWith('/auth/signup')) {
		redirect(303, '/');
	}

	if (!event.locals.session && !event.url.pathname.startsWith('/auth')) {
		redirect(303, '/auth/signup');
	}

	return resolve(event);
};

const requestContext: Handle = async ({ event, resolve }) => {
	// Use platform request ID if present or generate one
	const platformId = event.request.headers.get('x-vercel-id') ?? undefined;
	const requestId = platformId ?? randomUUID();
	const start = performance.now();

	return als.run({ requestId, start }, async () => {
		// Create basic request-scoped logger (user context will be added later)
		event.locals.log = logger.child({
			requestId,
			path: event.url.pathname,
			method: event.request.method,
		});

		event.locals.log.info({ msg: 'request.start' });

		try {
			const res = await resolve(event);
			event.locals.log.info({
				msg: 'request.end',
				status: res.status,
				duration_ms: Math.round(performance.now() - start),
			});
			return res;
		} catch (error) {
			event.locals.log.error({
				msg: 'request.error',
				error,
				duration_ms: Math.round(performance.now() - start),
			});
			throw error;
		}
	});
};

// Add user context to logging after auth is available
const enhanceLogging: Handle = async ({ event, resolve }) => {
	// Get user context if available
	const { user } = await event.locals.getSession();
	const userId = user?.id;
	const orgId = event.cookies.get(ORG_COOKIE_NAME);

	// Update context with user info
	const context = als.getStore();
	if (context) {
		context.userId = userId;
		context.orgId = orgId;
	}

	// Enhance logger with user context
	if (userId || orgId) {
		event.locals.log = event.locals.log.child({
			userId,
			orgId,
		});
	}

	return resolve(event);
};

export const handle: Handle = sequence(
	Sentry.sentryHandle(),
	requestContext,
	supabase,
	orgContext,
	enhanceLogging,
	authGuard,
);

export const handleError: HandleServerError = async ({ error, event, status, message }) => {
	const errorId = randomUUID();

	event.locals.log.error({
		msg: 'request.error',
		error,
	});

	Sentry.captureException(error, {
		extra: { event, errorId, status },
	});

	return {
		message,
		errorId,
	};
};
