import type { Database } from '$lib/database.types';
import type { Session, SupabaseClient, User } from '@supabase/supabase-js';
import type pino from 'pino';

declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			supabase: SupabaseClient<Database>;
			getSession: () => Promise<{
				session: Session | null;
				user: User | null;
			}>;
			session: Session | null;
			user: User | null;
			orgId: string | null;
			log: pino.Logger;
		}
		interface PageData {
			flash?: {
				type: 'success' | 'error';
				message: string;
				data?: { newOrgId?: string; [key: string]: unknown };
			};
		}
		// interface PageState {}
		// interface Platform {}
		interface Error {
			code?: string;
			errorId?: string;
		}
		namespace Superforms {
			type Message = {
				type: 'error' | 'success';
				text: string;
				data?: { [key: string]: unknown };
			};
		}
	}
}

export {};
