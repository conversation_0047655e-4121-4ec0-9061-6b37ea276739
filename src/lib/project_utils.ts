import type { SupabaseClient } from '@supabase/supabase-js';
import { error } from '@sveltejs/kit';
import type { Database, Enums, TablesInsert } from './database.types';
import { projectUUID, type CreateBudgetItem, type UpdateBudgetItem } from './schemas/project';

// Project Stages
export type ProjectStage = {
	name: string;
	stage_order: number;
	stage?: number;
	description?: string;
};

export const StandardICMSStages: ProjectStage[] = [
	{
		name: 'Rough Order of Cost Estimate',
		stage_order: 0,
		stage: 0,
	},
	{
		name: 'Order of cost estimate',
		stage_order: 1,
		stage: 1,
	},
	{
		name: 'Elemental cost estimate',
		stage_order: 2,
		stage: 2,
	},
	{
		name: 'Formal cost plan 1',
		stage_order: 3,
		stage: 3,
	},
	{
		name: 'Formal cost plan 2',
		stage_order: 4,
		stage: 4,
	},
	{
		name: 'Formal cost plan 3',
		stage_order: 5,
		stage: 5,
	},
	{
		name: 'Pre-tender estimate',
		stage_order: 6,
		stage: 6,
	},
	{
		name: 'Pricing documents',
		stage_order: 7,
		stage: 7,
	},
	{
		name: 'Post-tender estimate',
		stage_order: 8,
		stage: 8,
	},
	{
		name: 'Construction',
		stage_order: 9,
		stage: 9,
	},
	{
		name: 'Handover',
		stage_order: 10,
		stage: 10,
	},
];

export const StandardRibaStages: ProjectStage[] = [
	{
		stage: 0,
		name: 'Strategic Definition',
		stage_order: 0,
		description: `
Stage Outcome:
The best means of achieving the Client Requirements confirmed

Core Tasks:
• Prepare Client Requirements
• Develop business case options including review of Project Risks and Project Budget
• Identify and evaluate options that best deliver Client Requirements
• Review feedback from previous projects
• Undertake Site Appraisals

Core Statutory Processes:
• Strategic appraisal of Planning considerations

Information Exchanges:
• Client Requirements
• Business Case
• Project Budget
• Feasibility Studies
• Site Information
• Project Programme
• Project Execution Plan
• Responsibility Matrix
• Information Requirements
		`.trim(),
	},
	{
		stage: 1,
		name: 'Preparation and Briefing',
		stage_order: 1,
		description: `
Stage Outcome:
Project Brief approved by the client and confirmed that it can be accommodated on the site

Core Tasks:
• Prepare Project Brief including Project Outcomes, Sustainability Aspirations, and Spatial Requirements
• Undertake Feasibility Studies
• Agree Project Budget
• Gather Site Information, including Site Surveys
• Prepare Project Programme
• Prepare Project Execution Plan

Core Statutory Processes:
• Secure pre-application Planning Advice
• Initiate Outline Health and Safety (pre-construction) information

Information Exchanges:
• Project Brief Derogations
• Signed off Stage Report
• Project Strategies
• Outline Specification
• Cost Plan
		`.trim(),
	},
	{
		stage: 2,
		name: 'Concept Design',
		stage_order: 2,
		description: `
Stage Outcome:
Architectural Concept approved by the client and aligned to the Project Brief

Core Tasks:
• Prepare Architectural Concept (incorporating requirements and intent from the Project Brief, Project Strategies, and Outline Specification)
• Agree Project Brief
• Undertake Design Reviews with client and Project Stakeholders
• Initiate Change Control Procedure
• Prepare stage Design Programme

Core Statutory Processes:
• Obtain pre-application Planning Advice
• Review route to Building Regulations compliance
• Option to submit Outline Planning Application

Information Exchanges:
• Signed off Stage Report
• Updated Outline Specification
• Updated Cost Plan
• Planning Application
		`.trim(),
	},
	{
		stage: 3,
		name: 'Spatial Coordination',
		stage_order: 3,
		description: `
Stage Outcome:
Architectural and engineering information Spatially Coordinated

Core Tasks:
• Undertake Design Studies in Architectural, Engineering, and Specialist Consultancy for Spatially Coordinated Design
• Continue alignment of Project Strategies and Outline Specification
• Coordinate Plan, Project Stakeholders, and design information
• Initiate Change Control Procedure
• Prepare stage Design Programme

Core Statutory Processes:
• Review design against Building Regulations
• Prepare Planning Application

Information Exchanges:
• Spatially Coordinated design information
• Updated Cost Plan
• Updated Project Strategies
• Responsibility Matrix / Information Requirements
• Stage 3 Report (if applicable)
		`.trim(),
	},
	{
		stage: 4,
		name: 'Technical Design',
		stage_order: 4,
		description: `
Stage Outcome:
All design information required to manufacture and construct the project completed

Core Tasks:
• Develop Architectural and Engineering Technical Design
• Coordinate with Specialist Design Team (Building Systems information)
• Integrate Specialist Subcontractor Building Systems information
• Prepare stage Design Programme

Core Statutory Processes:
• Submit Building Regulations Application
• Discharge pre-commencement Planning Conditions
• Prepare Construction Phase Plan
• Submit form F10 to HSE if applicable

Information Exchanges:
• Construction Information
• Fire Safety Information
• Final Specifications
• Building Regulations Application
		`.trim(),
	},
	{
		stage: 5,
		name: 'Manufacturing and Construction',
		stage_order: 5,
		description: `
Stage Outcome:
Manufacturing, construction, and Commissioning completed

Core Tasks:
• Finalise Site Logistics
• Manufacture offsite and construct onsite
• Monitor progress against Construction Programme
• Inspect Construction Quality
• Resolve Site Queries as required
• Undertake Commissioning of the building
• Prepare Building Manual

Core Statutory Processes:
• Carry out Construction Phase Plan
• Comply with Planning Conditions as required

Information Exchanges:
• Manufacturing Information
• Final Specifications
• Residual Project Strategies
• Building Regulations Application
		`.trim(),
	},
	{
		stage: 6,
		name: 'Handover',
		stage_order: 6,
		description: `
Stage Outcome:
Building handed over, Aftercare initiated, Building Contract concluded

Core Tasks:
• Hand over building in line with Soft Landings Strategy
• Undertake review of Project Performance
• Undertake seasonal Commissioning
• Rectify defects
• Complete initial Aftercare tasks, including light-touch Post Occupancy Evaluation

Core Statutory Processes:
• Comply with Planning Conditions as required

Information Exchanges:
• Building Manual (including Health and Safety File and Fire Safety Information)
• Practical Completion Certificate (including Defects List)
• Asset Information
		`.trim(),
	},
	{
		stage: 7,
		name: 'Use',
		stage_order: 7,
		description: `
Stage Outcome:
Building used, operated, and maintained efficiently

Core Tasks:
• Implement Facilities Management and Asset Management
• Undertake Post Occupancy Evaluation of building performance in use
• Verify Project Outcomes including Sustainability Outcomes

Core Statutory Processes:
• Comply with Planning Conditions as required

Information Exchanges:
• Feedback on Project Performance
• Final Certificate
• Feedback from light-touch Post Occupancy Evaluation
• Feedback from comprehensive Post Occupancy Evaluation
• Updated Building Manual (including Health and Safety File and Fire Safety Information as necessary)
		`.trim(),
	},
];

// Budget Management Functions

/**
 * Creates or updates a budget line item
 */
export async function upsertBudgetLineItem(
	supabase: SupabaseClient<Database>,
	lineItem: CreateBudgetItem | UpdateBudgetItem,
	changeReason?: string,
) {
	// Always use the RPC function to manage the budget line item
	// It will properly calculate costs and handle audit entries
	const rpcParams = {
		p_project_id: lineItem.project_id,
		p_wbs_library_item_id: lineItem.wbs_library_item_id,
		p_quantity: lineItem.quantity,
		p_unit: lineItem.unit ?? undefined,
		p_material_rate: lineItem.material_rate,
		p_labor_rate: lineItem.labor_rate ?? undefined,
		p_productivity_per_hour: lineItem.productivity_per_hour ?? undefined,
		p_unit_rate_manual_override: lineItem.unit_rate_manual_override,
		p_unit_rate: lineItem.unit_rate,
		p_factor: lineItem.factor ?? undefined,
		p_remarks: lineItem.remarks ?? undefined,
		p_cost_certainty: lineItem.cost_certainty ?? undefined,
		p_design_certainty: lineItem.design_certainty ?? undefined,
		p_change_reason: changeReason,
		p_budget_line_item_id:
			'budget_line_item_id' in lineItem && lineItem.budget_line_item_id !== undefined
				? lineItem.budget_line_item_id
				: undefined,
	};

	console.log('Calling upsert_budget_line_item RPC with params:', rpcParams);

	const { data, error: err } = await supabase.rpc('upsert_budget_line_item', rpcParams);

	if (err) {
		console.error('RPC upsert_budget_line_item failed:', {
			error: err,
			code: err.code,
			message: err.message,
			details: err.details,
			hint: err.hint,
			params: rpcParams,
		});

		throw error(500, {
			message: `Failed to create/update budget line item: ${err.message}`,
		});
	}

	console.log('RPC upsert_budget_line_item succeeded:', data);
	return data;
}

export async function getProjectWithStages(
	supabase: SupabaseClient<Database>,
	project_id_short: string,
) {
	const project_id = projectUUID(project_id_short);
	const { data: project, error: projectError } = await supabase
		.from('project')
		.select('*, project_stage!project_stage_project_id_fkey(*)')
		.eq('project_id', project_id)
		.order('stage_order', {
			referencedTable: 'project_stage',
			ascending: true,
		})
		.limit(1)
		.maybeSingle();
	return { project, projectError };
}

/**
 * Gets all current budget line items for a project
 */
export async function getBudgetLineItems(supabase: SupabaseClient<Database>, projectId: string) {
	const { data, error: err } = await supabase
		.from('budget_line_item_current')
		.select(
			`
			budget_line_item_id,
      project_id,
      wbs_library_item_id,
      quantity,
      unit,
      material_rate,
      labor_rate,
      productivity_per_hour,
      unit_rate_manual_override,
      unit_rate,
			factor,
      remarks,
			cost_certainty,
			design_certainty,
      wbs_library_item (
        code,
        description,
        level,
        parent_item_id
      )
    `,
		)
		.eq('project_id', projectId);

	if (err) {
		throw error(500, {
			message: `Failed to get budget line items: ${err.message}`,
		});
	}

	return data;
}

/**
 * Creates a budget snapshot for a project stage
 */
export async function createBudgetSnapshot(
	supabase: SupabaseClient<Database>,
	projectStageId: string,
	freezeReason?: string,
) {
	const { data, error: err } = await supabase.rpc('create_budget_snapshot', {
		p_project_stage_id: projectStageId,
		p_freeze_reason: freezeReason,
	});

	if (err) {
		throw error(500, {
			message: `Failed to create budget snapshot: ${err.message}`,
		});
	}

	return data;
}

/**
 * Gets all snapshots for a project
 */
export async function getBudgetSnapshots(supabase: SupabaseClient<Database>, projectId: string) {
	const { data, error: err } = await supabase
		.from('budget_snapshot')
		.select(
			`
      budget_snapshot_id,
      project_stage_id,
      freeze_date,
      freeze_reason,
      created_by_user_id,
      created_at,
      project_stage (
        name,
        stage_order,
        stage,
        project_id
      )
    `,
		)
		.eq('project_stage.project_id', projectId);

	if (err) {
		throw error(500, {
			message: `Failed to get budget snapshots: ${err.message}`,
		});
	}

	return data;
}

/**
 * Gets budget line items for a specific snapshot
 */
export async function getSnapshotLineItems(supabase: SupabaseClient<Database>, snapshotId: string) {
	const { data, error: err } = await supabase
		.from('budget_snapshot_line_item')
		.select(
			`
      budget_snapshot_line_item_id,
      budget_snapshot_id,
      wbs_library_item_id,
      quantity,
      unit,
      material_rate,
      labor_rate,
      productivity_per_hour,
      unit_rate_manual_override,
      unit_rate,
			factor,
      remarks,
			cost_certainty,
			design_certainty,
      wbs_library_item (
        code,
        description,
        level,
        parent_item_id
      )
    `,
		)
		.eq('budget_snapshot_id', snapshotId);

	if (err) {
		throw error(500, {
			message: `Failed to get snapshot line items: ${err.message}`,
		});
	}

	return data;
}

/**
 * Compares two budget snapshots
 */
export async function compareBudgetSnapshots(
	supabase: SupabaseClient<Database>,
	snapshotId1: string,
	snapshotId2: string,
) {
	const { data, error: err } = await supabase.rpc('compare_budget_snapshots', {
		p_snapshot_id_1: snapshotId1,
		p_snapshot_id_2: snapshotId2,
	});

	if (err) {
		throw error(500, {
			message: `Failed to compare budget snapshots: ${err.message}`,
		});
	}

	return data;
}

/**
 * Reverts budget to a specific snapshot
 */
export async function revertToBudgetSnapshot(
	supabase: SupabaseClient<Database>,
	snapshotId: string,
	revertReason?: string,
) {
	const { data, error: err } = await supabase.rpc('revert_to_budget_snapshot', {
		p_budget_snapshot_id: snapshotId,
		p_revert_reason: revertReason || 'Reverted to snapshot',
	});

	if (err) {
		throw error(500, {
			message: `Failed to revert to budget snapshot: ${err.message}`,
		});
	}

	return data;
}

/**
 * Gets audit history for a specific budget line item
 */
export async function getBudgetItemAuditHistory(
	supabase: SupabaseClient<Database>,
	projectId: string,
	wbsLibraryItemId: string,
) {
	const { data, error: err } = await supabase
		.from('budget_line_item_audit')
		.select('*')
		.eq('project_id', projectId)
		.eq('wbs_library_item_id', wbsLibraryItemId)
		.order('changed_at', { ascending: false });

	if (err) {
		throw error(500, {
			message: `Failed to get budget item audit history: ${err.message}`,
		});
	}

	return data;
}

/**
 * Gets gateway checklist items for a project stage
 */
export async function getGatewayChecklistItems(
	supabase: SupabaseClient<Database>,
	projectStageId: string,
) {
	const { data, error: err } = await supabase
		.from('gateway_checklist_item')
		.select(
			`
    *,
    gateway_checklist_item_status_log(
      gateway_checklist_item_id,
		  status,
      valid_at,
			latest
    )
  `,
		)
		.eq('project_stage_id', projectStageId)
		// only pull back the one “latest” status row per item
		.eq('gateway_checklist_item_status_log.latest', true);

	if (err) {
		throw error(500, {
			message: `Failed to get gateway checklist items: ${err.message}`,
		});
	}

	return data.map((item) => ({
		...item,
		// Safely access status with optional chaining and fallback to 'Incomplete'
		status: item.gateway_checklist_item_status_log[0]?.status || 'Incomplete',
		gateway_checklist_item_status_log: undefined, // remove the array of status logs
	}));
}

/**
 * TODO: we need a new function to just update the status
 * because name and description are not included in the form
 * on the gateway page
 *
 * */

/**
 * Creates or updates a gateway checklist item and optionally updates its status
 * @param supabase - Supabase client
 * @param item - The checklist item data
 * @param status - Optional status to set for the item
 * @returns Result of the operation
 */
export async function upsertGatewayChecklistItem(
	supabase: SupabaseClient<Database>,
	item:
		| TablesInsert<'gateway_checklist_item'>
		| {
				gateway_checklist_item_id: string;
				name: string;
				description?: string | null;
				project_stage_id: string;
		  },
	status?: Enums<'checklist_item_status'>,
) {
	let itemResult;

	console.log('upsertGatewayChecklistItem', { item });
	if (item.gateway_checklist_item_id && item.name === '') {
		const { error: statusError } = await supabase.from('gateway_checklist_item_status_log').insert({
			gateway_checklist_item_id: item.gateway_checklist_item_id,
			status: status,
			// Note: 'latest' is managed by the trigger, don't set it manually
		});

		if (statusError) {
			console.error('upsertGatewayChecklistItem status update error:', { statusError });
			return {
				data: null,
				error: statusError,
				statusUpdated: false,
			};
		}

		return {
			data: null,
			error: null,
			statusUpdated: true,
		};
	}

	if (item.gateway_checklist_item_id) {
		// Update existing item
		const { data, error } = await supabase
			.from('gateway_checklist_item')
			.update({
				name: item.name,
				description: item.description ?? null,
			})
			.eq('gateway_checklist_item_id', item.gateway_checklist_item_id)
			.select()
			.single();

		console.log({ data, error });
		if (error) {
			console.error('upsertGatewayChecklistItem update error:', { error });
			return { data, error };
		}

		itemResult = { data, error };
	} else {
		// Insert new item
		const { data, error } = await supabase
			.from('gateway_checklist_item')
			.insert({
				project_stage_id: item.project_stage_id,
				name: item.name,
				description: item.description,
			})
			.select()
			.single();

		console.log({ data, error });
		if (error) {
			console.error('upsertGatewayChecklistItem insert error:', { error });
			return { data, error };
		}

		itemResult = { data, error };
	}

	if (status) {
		// If a status is provided and the item operation was successful, create a new status log entry
		if (itemResult.data) {
			const itemId = itemResult.data.gateway_checklist_item_id;

			const { error: statusError } = await supabase
				.from('gateway_checklist_item_status_log')
				.insert({
					gateway_checklist_item_id: itemId,
					status: status,
					// Note: 'latest' is managed by the trigger, don't set it manually
				});

			if (statusError) {
				console.error('upsertGatewayChecklistItem status update error:', { statusError });
				return {
					data: itemResult.data,
					error: statusError,
					statusUpdated: false,
				};
			}

			return {
				data: itemResult.data,
				error: null,
				statusUpdated: true,
			};
		}
	}

	return itemResult;
}

/**
 * Checks if a project stage is ready for completion
 */
export async function isStageReadyForCompletion(
	supabase: SupabaseClient<Database>,
	projectStageId: string,
) {
	const { data, error: err } = await supabase.rpc('is_stage_ready_for_completion', {
		p_project_stage_id: projectStageId,
	});

	if (err) {
		throw error(500, {
			message: `Failed to check stage completion status: ${err.message}`,
		});
	}

	return data;
}

/**
 * Completes a project stage (with snapshot creation)
 */
export async function completeProjectStage(
	supabase: SupabaseClient<Database>,
	projectStageId: string,
	completionNotes?: string,
) {
	const { data, error: err } = await supabase.rpc('complete_project_stage', {
		p_project_stage_id: projectStageId,
		p_completion_notes: completionNotes,
	});

	if (err) {
		throw error(500, {
			message: `Failed to complete project stage: ${err.message}`,
		});
	}

	return data;
}
