<script module>
	import {
		DropdownMenu,
		DropdownMenuTrigger,
		DropdownMenuContent,
		DropdownMenuItem,
	} from '$lib/components/ui/dropdown-menu';
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import But<PERSON> from '../button/button.svelte';

	const { Story } = defineMeta({
		title: 'UI/DropdownMenu',
		component: DropdownMenu,
		tags: ['autodocs'],
		argTypes: {
			open: { control: 'boolean' },
		},
	});
</script>

<Story name="Default">
	<div class="p-32">
		<DropdownMenuTrigger><Button>Open</Button></DropdownMenuTrigger>
		<DropdownMenuContent>
			<DropdownMenuItem>Item One</DropdownMenuItem>
			<DropdownMenuItem>Item Two</DropdownMenuItem>
			<DropdownMenuItem>Item Three</DropdownMenuItem>
		</DropdownMenuContent>
	</div>
</Story>
