<script module>
	import { Toaster } from '$lib/components/ui/sonner';
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { toast } from 'svelte-sonner';

	const { Story } = defineMeta({
		title: 'UI/Sonner',
		component: Toaster,
		tags: ['autodocs'],
		argTypes: {
			position: {
				control: 'select',
				options: ['top-right', 'top-left', 'bottom-right', 'bottom-left'],
			},
		},
	});
</script>

<Story name="Default"></Story>
<div class="grid h-96 w-full place-items-center">
	<button
		onclick={() => {
			toast('Hello World!');
		}}
	>
		Show Toast
	</button>
</div>
