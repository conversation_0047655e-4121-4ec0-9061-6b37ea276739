<script lang="ts">
	import * as d3 from 'd3';
	import { Sunburst } from './observable/sunburst';
	import type { CurrentBudgetNode } from '$lib/budget_utils';

	const { data }: { data: CurrentBudgetNode } = $props();

	let element: HTMLDivElement | undefined = $state(undefined);

	$effect(() => {
		if (!element) return;
		if (!data) return;

		// Clear any existing content
		d3.select(element).selectAll('*').remove();

		let chart = Sunburst(data, {
			label: (d) =>
				d.description.length > 20 ? `${d.description.slice(0, 20)}...` : d.description, // display name for each cell
			title: (d, n) => `${d.code}: ${d.description}\n${(n.value || 0).toLocaleString()}`, // hover text
			width: 800,
			height: 800,
			margin: 40, // Add some margin
		});

		d3.select(element).append(() => chart);
	});
</script>

<div
	bind:this={element}
	class="flex h-full w-full items-center justify-center overflow-hidden"
></div>
