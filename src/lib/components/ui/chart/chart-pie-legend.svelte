<script lang="ts">
	import { PieChart } from 'layerchart';
	import * as Chart from '$lib/components/ui/chart/index.js';
	import * as Card from '$lib/components/ui/card/index.js';

	const {
		title,
		description,
		chartData,
		chartConfig,
		key,
		value,
	}: {
		title: string;
		description: string;
		chartData: object[];
		chartConfig: Chart.ChartConfig;
		key: string;
		value: string;
	} = $props();

	const adjustedData = $derived(
		chartData.map((d) => ({
			...d,
			color: chartConfig[(d as Record<string, unknown>)[key] as string]?.color,
		})),
	);

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	function labeller(d: any) {
		return d[key];
	}
</script>

<Card.Root class="flex flex-col">
	<Card.Header class="items-center">
		<Card.Title>{title}</Card.Title>
		<Card.Description>{description}</Card.Description>
	</Card.Header>
	<Card.Content class="flex-1">
		<Chart.Container config={chartConfig} class="mx-auto aspect-square max-h-[250px]">
			<PieChart
				data={adjustedData}
				{key}
				{value}
				label={labeller}
				cRange={Object.keys(chartConfig).map((k) => chartConfig[k].color!)}
				props={{
					pie: {
						motion: 'tween',
					},
				}}
				legend
			>
				{#snippet tooltip()}
					<Chart.Tooltip labelKey={key} />
				{/snippet}
			</PieChart>
		</Chart.Container>
	</Card.Content>
</Card.Root>
