<script module>
	import {
		B<PERSON><PERSON><PERSON><PERSON>,
		<PERSON><PERSON><PERSON>rumbList,
		BreadcrumbItem,
		BreadcrumbLink,
		BreadcrumbSeparator,
		BreadcrumbPage,
	} from '$lib/components/ui/breadcrumb';
	import { defineMeta } from '@storybook/addon-svelte-csf';

	const { Story } = defineMeta({
		title: 'UI/Breadcrumb',
		component: Breadcrumb,
		tags: ['autodocs'],
	});
</script>

<Story name="Default">
	<Breadcrumb>
		<BreadcrumbList>
			<BreadcrumbItem><BreadcrumbLink href="#">Home</BreadcrumbLink></BreadcrumbItem>
			<BreadcrumbSeparator />
			<BreadcrumbItem><BreadcrumbPage>Library</BreadcrumbPage></BreadcrumbItem>
		</BreadcrumbList>
	</Breadcrumb>
</Story>
