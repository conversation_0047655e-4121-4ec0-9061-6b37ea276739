<script lang="ts">
	import { Calendar as CalendarPrimitive } from 'bits-ui';
	import CalendarMonthSelect from './calendar/calendar-month-select.svelte';
	import { Input } from './input';
	import { Label } from './label';
	import { cn } from '$lib/utils';
	import {
		createAccountingPeriod,
		parseAccountingPeriod,
		getCurrentAccountingPeriod,
	} from '$lib/schemas/invoice';

	let {
		value = $bindable(),
		placeholder = 'Select period',
		class: className,
		disabled = false,
		...restProps
	}: {
		value?: string | null;
		placeholder?: string;
		class?: string;
		disabled?: boolean;
	} = $props();

	// Parse current value or use current period as default
	const currentPeriod = getCurrentAccountingPeriod();
	const defaultParsed = parseAccountingPeriod(currentPeriod);

	let selectedYear = $state<number>(
		value ? (parseAccountingPeriod(value)?.year ?? defaultParsed!.year) : defaultParsed!.year,
	);
	let selectedMonth = $state<number>(
		value ? (parseAccountingPeriod(value)?.month ?? defaultParsed!.month) : defaultParsed!.month,
	);

	// Update value when year or month changes
	$effect(() => {
		if (selectedYear && selectedMonth) {
			value = createAccountingPeriod(selectedYear, selectedMonth);
		}
	});

	// Update internal state when value changes externally
	$effect(() => {
		if (value) {
			const parsed = parseAccountingPeriod(value);
			if (parsed) {
				selectedYear = parsed.year;
				selectedMonth = parsed.month;
			}
		}
	});

	function handleYearChange(event: Event) {
		const target = event.target as HTMLInputElement;
		const year = parseInt(target.value, 10);
		if (!isNaN(year) && year > 1900 && year < 3000) {
			selectedYear = year;
		}
	}

	function handleMonthChange(event: Event) {
		const target = event.target as HTMLSelectElement;
		const month = parseInt(target.value, 10);
		if (!isNaN(month) && month >= 1 && month <= 12) {
			selectedMonth = month;
		}
	}
</script>

<div class={cn('flex items-center gap-2', className)} {...restProps}>
	<!-- Month Selector -->
	<div class="flex-1">
		<Label class="text-muted-foreground mb-1 block text-xs">Month</Label>
		<CalendarPrimitive.Root type="single">
			<CalendarMonthSelect
				value={selectedMonth}
				onchange={handleMonthChange}
				{disabled}
				class="w-full"
			/>
		</CalendarPrimitive.Root>
	</div>

	<!-- Year Input -->
	<div class="flex-1">
		<Label class="text-muted-foreground mb-1 block text-xs">Year</Label>
		<Input
			type="number"
			value={selectedYear}
			onchange={handleYearChange}
			min="1900"
			max="3000"
			{placeholder}
			{disabled}
			class="w-full"
		/>
	</div>
</div>
