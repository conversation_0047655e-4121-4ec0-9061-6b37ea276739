<script module>
	import { Badge } from '$lib/components/ui/badge';
	import { defineMeta } from '@storybook/addon-svelte-csf';

	const { Story } = defineMeta({
		title: 'UI/Badge',
		component: Badge,
		tags: ['autodocs'],
		argTypes: {
			variant: {
				control: { type: 'select' },
				options: ['default', 'secondary', 'destructive', 'outline'],
			},
		},
	});
</script>

<Story name="Default">Default Badge</Story>
<Story name="Secondary" args={{ variant: 'secondary' }}>Secondary Badge</Story>
<Story name="Destructive" args={{ variant: 'destructive' }}>Destructive Badge</Story>
<Story name="Outline" args={{ variant: 'outline' }}>Outline Badge</Story>
