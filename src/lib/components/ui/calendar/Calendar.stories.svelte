<script module>
	import { Calendar } from '$lib/components/ui/calendar';
	import { defineMeta } from '@storybook/addon-svelte-csf';

	const { Story } = defineMeta({
		title: 'UI/Calendar',
		component: Calendar,
		tags: ['autodocs'],
		argTypes: {
			type: { control: 'select', options: ['single', 'multiple'] },
		},
	});
</script>

<Story name="Single" args={{ type: 'single' }}></Story>

<Story name="Multiple" args={{ type: 'multiple' }}></Story>
