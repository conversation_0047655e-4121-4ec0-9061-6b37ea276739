<script module>
	import { Select, SelectTrigger, SelectContent, SelectItem } from '$lib/components/ui/select';
	import { defineMeta } from '@storybook/addon-svelte-csf';

	const { Story } = defineMeta({
		title: 'UI/Select',
		component: Select,
		tags: ['autodocs'],
	});
</script>

<Story name="Default">
	<Select type="single">
		<SelectTrigger>Select an option</SelectTrigger>
		<SelectContent>
			<SelectItem value="1">One</SelectItem>
			<SelectItem value="2">Two</SelectItem>
		</SelectContent>
	</Select>
</Story>
