<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import Input from './input.svelte';

	const { Story } = defineMeta({
		title: 'UI/Input',
		component: Input,
		tags: ['autodocs'],
		argTypes: {
			type: { control: 'text' },
			value: { control: 'text' },
		},
	});
</script>

<Story name="Default" args={{ value: 'Example' }} />
<Story name="Password" args={{ type: 'password', placeholder: 'Password' }} />
<Story name="Email" args={{ type: 'email', placeholder: 'Email address' }} />
