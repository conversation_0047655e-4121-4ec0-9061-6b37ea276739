<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import * as Dialog from './index.js';
	import Button from '../button/button.svelte';

	const { Story } = defineMeta({
		title: 'UI/Dialog',
		tags: ['autodocs'],
	});
</script>

<Story name="Default">
	<Dialog.Root>
		<Dialog.Trigger>
			<Button>Open Dialog</Button>
		</Dialog.Trigger>
		<Dialog.Content>
			<Dialog.Header>
				<Dialog.Title>Dialog Title</Dialog.Title>
			</Dialog.Header>
			<p class="py-4">Example content.</p>
			<Dialog.Footer>
				<Dialog.Close>
					<Button variant="secondary">Close</Button>
				</Dialog.Close>
			</Dialog.Footer>
		</Dialog.Content>
	</Dialog.Root>
</Story>
