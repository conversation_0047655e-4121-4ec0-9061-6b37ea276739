<script module>
	import { Command, CommandInput, CommandList, CommandItem } from '$lib/components/ui/command';
	import { defineMeta } from '@storybook/addon-svelte-csf';

	const { Story } = defineMeta({
		title: 'UI/Command',
		component: Command,
		tags: ['autodocs'],
	});
</script>

<Story name="Default">
	<Command>
		<CommandInput placeholder="Type a command" />
		<CommandList>
			<CommandItem>Item One</CommandItem>
			<CommandItem>Item Two</CommandItem>
		</CommandList>
	</Command>
</Story>
