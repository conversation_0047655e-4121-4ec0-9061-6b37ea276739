<script module>
	import {
		Sheet,
		Sheet<PERSON>rigger,
		She<PERSON><PERSON>onte<PERSON>,
		She<PERSON><PERSON><PERSON>,
		She<PERSON><PERSON>ooter,
	} from '$lib/components/ui/sheet';
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import But<PERSON> from '../button/button.svelte';

	const { Story } = defineMeta({
		title: 'UI/Sheet',
		component: Sheet,
		tags: ['autodocs'],
	});
</script>

<Story name="Default">
	<SheetTrigger><Button>Open</Button></SheetTrigger>
	<SheetContent>
		<SheetFooter>
			<SheetClose>
				<Button variant="secondary">Close</Button>
			</SheetClose>
		</SheetFooter>
	</SheetContent>
</Story>
