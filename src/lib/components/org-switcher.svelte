<script lang="ts">
	import { getCurrentOrgId } from '$lib/current-org.svelte';
	import * as Popover from '$lib/components/ui/popover';
	import { cn } from '$lib/utils';
	import {
		UsersThree as UsersThreeIcon,
		CaretDown as CaretDownIcon,
		Plus as PlusIcon,
		Gear as GearIcon,
	} from 'phosphor-svelte';
	import { buttonVariants } from './ui/button';
	import { page } from '$app/state';

	let isOpen = $state(false);
	const orgContext = getCurrentOrgId();
	const currentOrgId = $derived(orgContext.getOrgId());
	const availableOrgs = $derived(orgContext.getAvailableOrgs());
	const currentOrg = $derived(orgContext.getCurrentOrg());

	// Watch for changes to currentOrg.orgId
	$effect(() => {
		if (currentOrgId) {
			isOpen = false;
		}
	});
</script>

<div class="relative">
	<Popover.Root open={isOpen} onOpenChange={(open) => (isOpen = open)}>
		<Popover.Trigger
			id="org-switcher"
			class={cn(buttonVariants({ variant: 'outline' }), 'min-w-48 justify-between px-2 py-6')}
		>
			<div class="flex items-center gap-3">
				<div class="flex size-10 items-center justify-center rounded-md">
					<UsersThreeIcon class="size-5 text-black" weight="regular" />
				</div>
				<div class="flex flex-col items-start text-left text-sm">
					<span class="font-medium">{currentOrg?.name || 'Select Organization'}</span>
					{#if currentOrg?.role}
						<span class="text-muted-foreground text-xs">{currentOrg.role}</span>
					{/if}
				</div>
			</div>
			<CaretDownIcon
				class={cn('size-4 transition-transform', isOpen ? 'rotate-180' : '')}
				weight="bold"
			/>
		</Popover.Trigger>

		<Popover.Content class="w-72 p-0" align="start" sideOffset={4}>
			<div class="p-2 pb-0">
				<h3 class="px-2 py-1.5 text-lg font-medium">Your Organizations</h3>
			</div>

			<div class="p-2">
				{#each availableOrgs as org (org.org_id)}
					<form method="POST" action="/api/org-selection?/selectOrganization">
						<input type="hidden" name="orgId" value={org.org_id} />
						<input type="hidden" name="orgName" value={org.name} />
						<input type="hidden" name="route" value={page.url.pathname} />
						<div
							class={cn(
								'hover:bg-muted/50 flex w-full items-center justify-between gap-3 rounded-md p-2 text-left',
								currentOrgId === org.org_id ? 'bg-muted' : '',
							)}
						>
							<button
								class="hover:bg-muted/30 flex w-full items-center gap-3 rounded-md text-left"
								type="submit"
							>
								<div class="flex size-8 items-center justify-center rounded-md">
									<UsersThreeIcon class="size-4 text-black" weight="regular" />
								</div>
								<span class="flex-1">{org.name}</span>
							</button>
							<div>
								<a
									href="/org/{encodeURIComponent(org.name)}/members"
									onclick={() => (isOpen = false)}
								>
									<GearIcon class="size-4 text-black" weight="regular" />
								</a>
							</div>
						</div>
					</form>
				{:else}
					<div class="text-muted-foreground p-2 text-sm">
						No organizations found. <a href="/org/new">Create one to get started.</a>
					</div>
				{/each}

				<a
					href="/org/new"
					onclick={() => (isOpen = false)}
					class="hover:bg-muted/50 mt-2 flex w-full items-center gap-3 rounded-md p-2 text-left"
				>
					<div class="flex size-8 items-center justify-center rounded-md border border-dashed">
						<PlusIcon class="size-4" />
					</div>
					<span>Create organization</span>
				</a>
			</div>
		</Popover.Content>
	</Popover.Root>
</div>
