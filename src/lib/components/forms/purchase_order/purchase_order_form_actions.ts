import { fail, type RequestEvent } from '@sveltejs/kit';
import { superValidate, message } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { purchaseOrderSchema, purchaseOrderModalSchema } from '$lib/schemas/purchase_order';
import { redirect } from 'sveltekit-flash-message/server';
import { requireProject, requireUser } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';

export const createPurchaseOrder = async ({ request, locals, cookies }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	const form = await superValidate(request, zod(purchaseOrderSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	// Prepare the purchase order data
	const purchaseOrderData = {
		...form.data,
		project_id: project_id,
		created_by_user_id: user.id,
	};

	// Insert the purchase order
	const { data: purchaseOrder, error: purchaseOrderError } = await supabase
		.from('purchase_order')
		.insert(purchaseOrderData)
		.select('purchase_order_id, po_number')
		.single();

	if (purchaseOrderError) {
		locals.log.error({
			msg: `Error creating purchase order: 	${JSON.stringify(purchaseOrderError)}`,
		});
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create purchase order' },
		});
	}

	throw redirect(
		302,
		`../purchase-order/${purchaseOrder.purchase_order_id}`,
		{
			type: 'success',
			message: `Purchase order "${purchaseOrder.po_number}" created successfully`,
		},
		cookies,
	);
};

export const createPurchaseOrderModal = async ({ request, locals }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	const form = await superValidate(request, zod(purchaseOrderModalSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	// Set the project_id in the form data
	form.data.project_id = project_id;

	// Prepare the purchase order data
	const purchaseOrderData = {
		...form.data,
		created_by_user_id: user.id,
	};

	// Insert the purchase order and get vendor name
	const { data: purchaseOrder, error: purchaseOrderError } = await supabase
		.from('purchase_order')
		.insert(purchaseOrderData)
		.select(
			`
				purchase_order_id,
				po_number,
				description,
				po_date,
				vendor_id,
				account,
				original_amount,
				co_amount,
				freight,
				tax,
				other,
				notes,
				vendor:vendor_id (name)
			`,
		)
		.single();

	if (purchaseOrderError) {
		locals.log.error({
			msg: `Error creating purchase order: ${JSON.stringify(purchaseOrderError)}`,
		});
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create purchase order' },
		});
	}

	// Return success with purchase order data instead of redirecting
	return {
		form: message(form, {
			type: 'success',
			text: `Purchase order "${purchaseOrder.po_number}" created successfully`,
		}),
		purchaseOrder: {
			purchase_order_id: purchaseOrder.purchase_order_id,
			po_number: purchaseOrder.po_number,
			description: purchaseOrder.description,
			po_date: purchaseOrder.po_date,
			vendor_id: purchaseOrder.vendor_id,
			vendor_name: purchaseOrder.vendor?.name || '',
			account: purchaseOrder.account,
			original_amount: purchaseOrder.original_amount,
			co_amount: purchaseOrder.co_amount,
			freight: purchaseOrder.freight,
			tax: purchaseOrder.tax,
			other: purchaseOrder.other,
			notes: purchaseOrder.notes,
		},
	};
};
