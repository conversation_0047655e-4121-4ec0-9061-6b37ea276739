import { fail, type RequestEvent } from '@sveltejs/kit';
import { superValidate, message } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { vendorSchema } from '$lib/schemas/vendor';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';

export const createVendor = async ({ request, locals, cookies }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;

	const form = await superValidate(request, zod(vendorSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	// Prepare the vendor data
	const vendorData = {
		...form.data,
		created_by_user_id: user.id,
	};

	// Insert the vendor
	const { data: vendor, error: vendorError } = await supabase
		.from('vendor')
		.insert(vendorData)
		.select('vendor_id, name')
		.single();

	if (vendorError) {
		locals.log.error({ msg: `Error creating vendor: ${JSON.stringify(vendorError)}` });
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create vendor' },
		});
	}

	throw redirect(
		'/vendor',
		{
			type: 'success',
			message: `Vendor "${vendor.name}" created successfully`,
		},
		cookies,
	);
};

export const createVendorModal = async ({ request, locals }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;

	const form = await superValidate(request, zod(vendorSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	// Prepare the vendor data
	const vendorData = {
		...form.data,
		created_by_user_id: user.id,
	};

	// Insert the vendor
	const { data: vendor, error: vendorError } = await supabase
		.from('vendor')
		.insert(vendorData)
		.select(
			'vendor_id, name, description, vendor_type, contact_name, contact_email, contact_phone, is_active',
		)
		.single();

	if (vendorError) {
		locals.log.error({ msg: `Error creating vendor: ${JSON.stringify(vendorError)}` });
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create vendor' },
		});
	}

	// Return success with vendor data instead of redirecting
	return {
		form: message(form, {
			type: 'success',
			text: `Vendor "${vendor.name}" created successfully`,
		}),
		vendor: {
			vendor_id: vendor.vendor_id,
			name: vendor.name,
			description: vendor.description,
			vendor_type: vendor.vendor_type,
			contact_name: vendor.contact_name,
			contact_email: vendor.contact_email,
			contact_phone: vendor.contact_phone,
			is_active: vendor.is_active,
			access_level: form.data.org_id ? 'organization' : form.data.client_id ? 'client' : 'project',
		},
	};
};
