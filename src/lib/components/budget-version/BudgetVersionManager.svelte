<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Badge } from '$lib/components/ui/badge';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { Clock, FileText, Upload, User, Undo2, Eye, CheckCircle2 } from '@lucide/svelte';
	import { DotsThreeVertical } from 'phosphor-svelte';
	import { toast } from 'svelte-sonner';
	import { formatCurrency, formatDate } from '$lib/utils';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import type { Database } from '$lib/database.types';
	import posthog from 'posthog-js';

	interface BudgetVersion {
		budget_version_id: string;
		label: string | null;
		kind: 'stage' | 'import' | 'manual' | 'system';
		is_active: boolean;
		item_count: number;
		total_cost: number;
		created_at: string;
	}

	interface BudgetImport {
		budget_import_id: string;
		source_filename: string;
		pre_version_id: string;
		new_version_id: string;
		is_undone: boolean | null;
		undone_at: string | null;
		created_at: string;
	}

	interface Props {
		projectId: string;
		supabase: SupabaseClient<Database>;
		canModifyProject: boolean;
	}

	const { projectId, supabase, canModifyProject }: Props = $props();

	let isOpen = $state(false);
	let versions: BudgetVersion[] = $state([]);
	let imports: BudgetImport[] = $state([]);
	let isLoading = $state(false);
	let isUndoing = $state(false);
	let isActivating = $state(false);
	let isComparing = $state(false);
	let diffData: any | null = $state(null);
	let diffForVersionId: string | null = $state(null);

	// Load versions and imports when dialog opens
	$effect(() => {
		if (isOpen) {
			loadVersionsAndImports();
		}
	});

	async function loadVersionsAndImports() {
		isLoading = true;
		try {
			// Load budget versions
			const { data: versionsData, error: versionsError } = await supabase.rpc(
				'list_budget_versions',
				{
					p_project_id: projectId,
					p_limit: 20,
				},
			);

			if (versionsError) {
				throw new Error(versionsError.message);
			}

			versions = versionsData || [];

			// Load budget imports
			const { data: importsData, error: importsError } = await supabase
				.from('budget_import')
				.select('*')
				.eq('project_id', projectId)
				.order('created_at', { ascending: false })
				.limit(20);

			if (importsError) {
				throw new Error(importsError.message);
			}

			imports = importsData || [];
		} catch (error) {
			console.error('Failed to load versions and imports:', error);
			toast.error('Failed to load version history', {
				description: error instanceof Error ? error.message : 'Unknown error',
			});
		} finally {
			isLoading = false;
		}
	}

	async function undoImport(importId: string, filename: string) {
		isUndoing = true;
		try {
			const { data, error } = await supabase.rpc('undo_budget_import', {
				p_budget_import_id: importId,
				p_reason: `Undoing import of ${filename}`,
			});

			if (error) {
				throw new Error(error.message);
			}

			toast.success('Import undone successfully', {
				description: `Reverted import of ${filename}`,
			});

			try {
				posthog.capture('budget_import_undone', {
					project_id: projectId,
					budget_import_id: importId,
				});
			} catch {
				// ignore
			}

			// Reload data
			await loadVersionsAndImports();

			// Refresh the page to show updated budget
			window.location.reload();
		} catch (error) {
			console.error('Failed to undo import:', error);
			toast.error('Failed to undo import', {
				description: error instanceof Error ? error.message : 'Unknown error',
			});
		} finally {
			isUndoing = false;
		}
	}

	async function activateVersion(versionId: string) {
		isActivating = true;
		try {
			const { error } = await supabase.rpc('activate_budget_version', {
				p_version_id: versionId,
				p_reason: 'Activate from Version History',
			});
			if (error) throw new Error(error.message);
			toast.success('Version activated');

			try {
				posthog.capture('budget_version_activated', {
					project_id: projectId,
					version_id: versionId,
					activation_source: 'version_history_ui',
				});
			} catch {
				// ignore telemetry errors
			}

			await loadVersionsAndImports();
			// Reload page so budget reads reflect active version
			window.location.reload();
		} catch (e) {
			console.error('Failed to activate version:', e);
			toast.error('Failed to activate version', {
				description: e instanceof Error ? e.message : 'Unknown error',
			});

			try {
				posthog.capture('budget_version_activation_failed', {
					project_id: projectId,
					version_id: versionId,
					error: e instanceof Error ? e.message : 'Unknown error',
				});
			} catch {
				// ignore telemetry errors
			}
		} finally {
			isActivating = false;
		}
	}

	async function compareToActive(versionId: string) {
		const active = versions.find((v) => v.is_active);
		if (!active) {
			toast.error('No active version to compare');
			return;
		}
		if (active.budget_version_id === versionId) {
			toast.info('Selected version is already active');
			return;
		}
		isComparing = true;
		try {
			const { data, error } = await supabase.rpc('diff_budget_versions', {
				p_version_a: active.budget_version_id,
				p_version_b: versionId,
			});
			if (error) throw new Error(error.message);
			diffData = data;
			diffForVersionId = versionId;

			try {
				const diffResult = data as any;
				posthog.capture('budget_version_compared', {
					project_id: projectId,
					version_a: active.budget_version_id,
					version_b: versionId,
					items_added: Array.isArray(diffResult?.added) ? diffResult.added.length : 0,
					items_removed: Array.isArray(diffResult?.removed) ? diffResult.removed.length : 0,
					items_changed: Array.isArray(diffResult?.changed) ? diffResult.changed.length : 0,
				});
			} catch {
				// ignore telemetry errors
			}
		} catch (e) {
			console.error('Failed to diff versions:', e);
			toast.error('Failed to compute diff', {
				description: e instanceof Error ? e.message : 'Unknown error',
			});
		} finally {
			isComparing = false;
		}
	}

	function getVersionLabel(version: BudgetVersion): string {
		if (version.label) return version.label;

		switch (version.kind) {
			case 'stage':
				return 'Stage Version';
			case 'import':
				return 'Import Version';
			case 'manual':
				return 'Manual Version';
			case 'system':
				return 'System Version';
			default:
				return 'Unknown Version';
		}
	}

	function getVersionBadgeVariant(
		version: BudgetVersion,
	): 'default' | 'secondary' | 'destructive' | 'outline' {
		if (version.is_active) return 'default';

		switch (version.kind) {
			case 'import':
				return 'secondary';
			case 'manual':
				return 'outline';
			default:
				return 'outline';
		}
	}

	// Find import record for a version
	function findImportForVersion(versionId: string): BudgetImport | undefined {
		return imports.find((imp) => imp.new_version_id === versionId);
	}
</script>

<Dialog.Root bind:open={isOpen}>
	<Dialog.Trigger>
		{#snippet child({ props })}
			<Button {...props} variant="outline" size="sm">
				<Clock class="mr-2 h-4 w-4" />
				Version History
			</Button>
		{/snippet}
	</Dialog.Trigger>
	<Dialog.Content class="max-h-[80vh] max-w-4xl overflow-hidden">
		<Dialog.Header>
			<Dialog.Title>Budget Version History</Dialog.Title>
			<Dialog.Description>
				View and manage budget versions and imports for this project.
			</Dialog.Description>
		</Dialog.Header>

		<div class="flex-1 overflow-y-auto">
			{#if isLoading}
				<div class="flex items-center justify-center py-8">
					<div class="text-muted-foreground">Loading version history...</div>
				</div>
			{:else if versions.length === 0}
				<div class="flex items-center justify-center py-8">
					<div class="text-muted-foreground">No versions found</div>
				</div>
			{:else}
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Version</TableHead>
							<TableHead>Type</TableHead>
							<TableHead class="text-right">Items</TableHead>
							<TableHead class="text-right">Total Cost</TableHead>
							<TableHead>Created</TableHead>
							<TableHead class="text-right">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{#each versions as version (version.budget_version_id)}
							{@const importRecord = findImportForVersion(version.budget_version_id)}
							<TableRow>
								<TableCell>
									<div class="flex items-center gap-2">
										<span class="font-medium">{getVersionLabel(version)}</span>
										<Badge variant={getVersionBadgeVariant(version)}>
											{version.is_active ? 'Active' : version.kind}
										</Badge>
										{#if importRecord?.is_undone}
											<Badge variant="destructive">Undone</Badge>
										{/if}
									</div>
									{#if importRecord}
										<div class="text-muted-foreground mt-1 text-sm">
											<FileText class="mr-1 inline h-3 w-3" />
											{importRecord.source_filename}
										</div>
									{/if}
								</TableCell>
								<TableCell>
									<div class="flex items-center gap-1">
										{#if version.kind === 'import'}
											<Upload class="h-4 w-4" />
										{:else if version.kind === 'stage'}
											<User class="h-4 w-4" />
										{:else}
											<FileText class="h-4 w-4" />
										{/if}
										<span class="capitalize">{version.kind}</span>
									</div>
								</TableCell>
								<TableCell class="text-right">{version.item_count.toLocaleString()}</TableCell>
								<TableCell class="text-right">{formatCurrency(version.total_cost)}</TableCell>
								<TableCell>{formatDate(version.created_at)}</TableCell>
								<TableCell class="text-right">
									<DropdownMenu.Root>
										<DropdownMenu.Trigger>
											{#snippet child({ props })}
												<Button {...props} variant="ghost" size="sm">
													<DotsThreeVertical class="h-4 w-4" />
												</Button>
											{/snippet}
										</DropdownMenu.Trigger>
										<DropdownMenu.Content align="end">
											<DropdownMenu.Item
												onclick={() => compareToActive(version.budget_version_id)}
												disabled={version.is_active || isComparing}
											>
												<Eye class="mr-2 h-4 w-4" />
												Compare to Active
											</DropdownMenu.Item>
											{#if canModifyProject && !version.is_active}
												<DropdownMenu.Item
													onclick={() => activateVersion(version.budget_version_id)}
													disabled={isActivating}
												>
													<CheckCircle2 class="mr-2 h-4 w-4" />
													Activate Version
												</DropdownMenu.Item>
											{/if}
											{#if canModifyProject && importRecord && !importRecord.is_undone && version.kind === 'import'}
												<DropdownMenu.Separator />
												<DropdownMenu.Item
													onclick={() =>
														undoImport(importRecord.budget_import_id, importRecord.source_filename)}
													disabled={isUndoing}
												>
													<Undo2 class="mr-2 h-4 w-4" />
													Undo Import
												</DropdownMenu.Item>
											{/if}
										</DropdownMenu.Content>
									</DropdownMenu.Root>
								</TableCell>
							</TableRow>
						{/each}
					</TableBody>
				</Table>

				{#if diffData}
					<Card class="mt-4">
						<CardHeader class="flex-row items-center justify-between">
							<CardTitle>Diff vs Active</CardTitle>
							<Button
								variant="outline"
								size="sm"
								onclick={() => {
									diffData = null;
									diffForVersionId = null;
								}}>Close</Button
							>
						</CardHeader>
						<CardContent>
							{#if diffData.summary}
								<div class="text-muted-foreground mb-2 text-sm">
									Delta: {formatCurrency(diffData.summary.total_cost_delta)}
								</div>
							{/if}
							<div class="grid grid-cols-3 gap-4 text-sm">
								<div>
									<span class="font-medium">Added:</span>
									{Array.isArray(diffData.added) ? diffData.added.length : 0}
								</div>
								<div>
									<span class="font-medium">Removed:</span>
									{Array.isArray(diffData.removed) ? diffData.removed.length : 0}
								</div>
								<div>
									<span class="font-medium">Changed:</span>
									{Array.isArray(diffData.changed) ? diffData.changed.length : 0}
								</div>
							</div>
							<pre class="bg-muted mt-3 max-h-64 overflow-auto rounded p-2 text-xs">{JSON.stringify(
									diffData,
									null,
									2,
								)}</pre>
						</CardContent>
					</Card>
				{/if}
			{/if}
		</div>

		<Dialog.Footer>
			<Button variant="outline" onclick={() => (isOpen = false)}>Close</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
