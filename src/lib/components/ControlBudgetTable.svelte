<script lang="ts">
	import { SvelteSet } from 'svelte/reactivity';
	import ControlBudgetNode from './ControlBudgetNode.svelte';
	import { createControlBudgetHierarchy } from '$lib/budget_utils.js';
	import { page } from '$app/state';
	import type { Tables } from '$lib/database.types';

	interface Props {
		allWbsItems: Tables<'wbs_library_item'>[];
		rawCurrentItems: Tables<'budget_line_item_current'>[];
		hideZeros: boolean;
		showRateCalculation: boolean;
		riskRegisterData: Tables<'risk_register'>[];
	}

	const { allWbsItems, rawCurrentItems, hideZeros, showRateCalculation, riskRegisterData }: Props =
		$props();

	// Create the control budget hierarchy on the client side using derived state
	const controlHierarchy = $derived(
		createControlBudgetHierarchy(allWbsItems, rawCurrentItems, riskRegisterData),
	);

	let projectId = $derived(page.data.project.project_id);

	let expandedNodeIds = new SvelteSet<string>();

	// Toggle expanded/collapsed state of a node
	function toggleNodeExpanded(nodeId: string) {
		if (expandedNodeIds.has(nodeId)) {
			expandedNodeIds.delete(nodeId);
		} else {
			expandedNodeIds.add(nodeId);
		}
	}
</script>

<table class="relative table-fixed border-collapse bg-white text-sm">
	<thead class="bg-muted sticky top-0 z-10 border-b">
		<tr class="bg-muted h-12 text-sm font-bold">
			<th class="bg-muted w-24 py-2 pr-2 pl-2 text-left">WBS Code</th>
			<th class="bg-muted w-72 px-2 text-left">Description</th>
			{#if showRateCalculation}
				<th class="bg-muted w-20 px-2 text-right">Quantity</th>
				<th class="bg-muted w-16 px-2 text-left">Unit</th>
				<th class="bg-muted w-80 px-2 text-center" colspan="3">Rate Calculation</th>
				<th class="bg-muted w-32 px-2 text-right">Unit Rate</th>
				<th class="bg-muted w-16 px-2 text-right">Factor</th>
			{/if}
			<th class="bg-muted w-32 px-2 text-right">Control Budget</th>
			<th class="bg-muted w-32 px-2 text-right">Accepted Tender</th>
			<th class="bg-muted w-32 px-2 text-right">Works to Complete</th>
			<th class="bg-muted w-32 px-2 text-right">Budget Unallocated</th>
			<!-- Accepted -->
			<th class="w-32 bg-green-500 px-2 text-right">Accepted Changes</th>
			<!-- Pending -->
			<th class="w-32 bg-amber-400 px-2 text-right">Anticipated Changes</th>
			<th class="bg-muted w-32 px-2 text-right">Forecast Final Cost</th>
			<th class="bg-muted w-32 px-2 text-right">Delta to Control Budget</th>
			<!-- Risk-->
			<th class="w-32 bg-red-300 px-2 text-right">Advanced Warnings</th>
		</tr>
		<tr class="bg-muted h-12 text-sm font-bold">
			<th colspan="4" class="bg-muted"></th>
			{#if showRateCalculation}
				<th class="bg-muted w-32 px-2 text-right">Material Rate</th>
				<th class="bg-muted w-24 px-2 text-right">Labor Rate</th>
				<th class="bg-muted w-24 px-2 text-right">Productivity</th>
			{:else}
				<th colspan="3" class="bg-muted"></th>
			{/if}
			<th colspan="11" class="bg-muted"></th>
		</tr>
	</thead>
	<tbody>
		{#if controlHierarchy}
			<ControlBudgetNode
				node={controlHierarchy}
				indent={0}
				expanded={expandedNodeIds}
				toggle={toggleNodeExpanded}
				{projectId}
				{hideZeros}
				{showRateCalculation}
			/>
		{/if}
	</tbody>
</table>
