<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { Input } from '$lib/components/ui/input';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import {
		Warning as AlertTriangle,
		CheckCircle,
		CurrencyDollar as DollarSign,
		Hash,
		Stack as Layers,
	} from 'phosphor-svelte';
	import { DotsThreeVertical, Pencil, Trash } from 'phosphor-svelte';
	import type { ClassifiedRow, ColumnMapping, ImportItem } from '$lib/budget_import_utils';
	import {
		transformToImportData,
		parseWbsCode,
		getRowStringValue,
		getRowNumericValue,
	} from '$lib/budget_import_utils';
	import { formatCurrency } from '$lib/utils';
	import { SvelteSet } from 'svelte/reactivity';

	interface Props {
		classifiedRows: ClassifiedRow[];
		columnMapping: ColumnMapping;
		projectId: string;
		onImport: (importData: ReturnType<typeof transformToImportData>) => void;
		onBack: () => void;
		isImporting?: boolean;
	}

	let {
		classifiedRows = $bindable(),
		columnMapping,
		projectId,
		onImport,
		onBack,
		isImporting = false,
	}: Props = $props();

	// Transform data for preview - this shows exactly what will be imported
	const transformedData = $derived(transformToImportData(classifiedRows, columnMapping, projectId));

	// Extract items for easier access
	const importItems = $derived(transformedData.items);

	// State for edit dialog
	let showEditDialog = $state(false);
	let editingIndex = $state(-1);

	// State for delete confirmation dialog
	let showDeleteDialog = $state(false);
	let deletingItem: ImportItem | null = $state(null);
	let deletingIndex = $state(-1);

	// Form data for editing
	let editFormData = $state({
		code: '',
		description: '',
		quantity: 0,
		unit: '',
		material_rate: 0,
		labor_rate: 0,
		productivity_per_hour: 1,
		unit_rate_manual_override: false,
		unit_rate: 0,
		factor: 1,
		remarks: '',
	});

	// Filter detail rows for backward compatibility with existing calculations
	const detailRows = $derived(classifiedRows.filter((row) => row.classification === 'detail'));

	// Functions for handling edit and delete operations
	function openEditDialog(item: ImportItem, index: number) {
		editingIndex = index;
		editFormData = {
			code: item.code,
			description: item.description,
			quantity: item.quantity,
			unit: item.unit,
			material_rate: item.material_rate,
			labor_rate: item.labor_rate ?? 0,
			productivity_per_hour: item.productivity_per_hour ?? 1,
			unit_rate_manual_override: item.unit_rate_manual_override,
			unit_rate: item.unit_rate ?? 0,
			factor: item.factor ?? 1,
			remarks: item.remarks ?? '',
		};
		showEditDialog = true;
	}

	function saveEdit() {
		if (editingIndex >= 0) {
			// Find the corresponding detail row in classifiedRows
			const detailRows = classifiedRows.filter((row) => row.classification === 'detail');
			if (editingIndex < detailRows.length) {
				const targetRow = detailRows[editingIndex];

				// Update the row data by modifying the raw values based on column mapping
				if (columnMapping.code !== undefined) {
					targetRow.rawValues[columnMapping.code] = editFormData.code;
				}
				if (columnMapping.description !== undefined) {
					targetRow.rawValues[columnMapping.description] = editFormData.description;
				}
				if (columnMapping.quantity !== undefined) {
					targetRow.rawValues[columnMapping.quantity] = editFormData.quantity;
				}
				if (columnMapping.uom !== undefined) {
					targetRow.rawValues[columnMapping.uom] = editFormData.unit;
				}
				if (columnMapping.material_rate !== undefined) {
					targetRow.rawValues[columnMapping.material_rate] = editFormData.material_rate;
				}
				if (columnMapping.labor_rate !== undefined) {
					targetRow.rawValues[columnMapping.labor_rate] = editFormData.labor_rate;
				}
				if (columnMapping.productivity_per_hour !== undefined) {
					targetRow.rawValues[columnMapping.productivity_per_hour] =
						editFormData.productivity_per_hour;
				}
				if (columnMapping.unit_rate_manual_override !== undefined) {
					targetRow.rawValues[columnMapping.unit_rate_manual_override] =
						editFormData.unit_rate_manual_override ? 1 : 0;
				}
				if (columnMapping.unit_rate !== undefined) {
					targetRow.rawValues[columnMapping.unit_rate] = editFormData.unit_rate;
				}
				if (columnMapping.factor !== undefined) {
					targetRow.rawValues[columnMapping.factor] = editFormData.factor;
				}

				// Update finalDescription if it exists
				if (editFormData.description !== targetRow.finalDescription) {
					targetRow.finalDescription = editFormData.description;
				}

				// Trigger reactivity by creating a new array
				classifiedRows = [...classifiedRows];
			}
		}
		closeEditDialog();
	}

	function closeEditDialog() {
		showEditDialog = false;
		editingIndex = -1;
	}

	function openDeleteDialog(item: ImportItem, index: number) {
		deletingItem = item;
		deletingIndex = index;
		showDeleteDialog = true;
	}

	function confirmDelete() {
		if (deletingIndex >= 0) {
			// Find the corresponding detail row in classifiedRows and remove it
			const detailRows = classifiedRows.filter((row) => row.classification === 'detail');
			if (deletingIndex < detailRows.length) {
				const targetRow = detailRows[deletingIndex];
				const targetIndex = classifiedRows.indexOf(targetRow);
				if (targetIndex >= 0) {
					classifiedRows.splice(targetIndex, 1);
					// Trigger reactivity
					classifiedRows = [...classifiedRows];
				}
			}
		}
		closeDeleteDialog();
	}

	function closeDeleteDialog() {
		showDeleteDialog = false;
		deletingItem = null;
		deletingIndex = -1;
	}

	// Handle Enter key in form inputs
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			event.preventDefault();
			saveEdit();
		}
	}

	// Helper functions for calculations using column mapping (kept for backward compatibility)
	function getRate(row: ClassifiedRow): number {
		// Try different rate fields based on mapping
		if (columnMapping.material_rate !== undefined) {
			return getRowNumericValue(row, 'material_rate', columnMapping);
		}
		if (columnMapping.rate !== undefined) {
			return getRowNumericValue(row, 'rate', columnMapping);
		}
		return 0;
	}

	function getQuantity(row: ClassifiedRow): number {
		return getRowNumericValue(row, 'quantity', columnMapping) ?? 0;
	}

	// Calculate statistics based on transformed data
	const totalItems = $derived(importItems.length);
	const totalBudget = $derived(
		importItems.reduce((sum, item) => {
			const quantity = item.quantity;
			const materialRate = item.material_rate;
			const laborRate = item.labor_rate ?? 0;
			const productivityPerHour = item.productivity_per_hour ?? 0;
			const factor = item.factor ?? 1;
			const parentFactor = item.parentFactor ?? 1;

			// Calculate unit rate based on the item's configuration
			let unitRate = 0;
			if (item.unit_rate_manual_override && item.unit_rate !== undefined) {
				unitRate = item.unit_rate;
			} else {
				// Calculate unit rate from material and labor components
				unitRate = materialRate;
				if (laborRate && productivityPerHour) {
					unitRate += laborRate / productivityPerHour;
				}
			}

			return sum + quantity * unitRate * factor * parentFactor;
		}, 0),
	);

	// Get unique WBS levels that will be created
	const wbsLevels = $derived.by(() => {
		const levels = new SvelteSet<string>();
		detailRows.forEach((row) => {
			const code = getRowStringValue(row, 'code', columnMapping);
			if (code) {
				try {
					const parsed = parseWbsCode(code);
					levels.add(`Level ${parsed.level}`);
				} catch (_e) {
					// Skip invalid codes
				}
			}
		});
		return Array.from(levels).sort();
	});

	// Validation warnings
	const validationWarnings = $derived.by(() => {
		const warnings: string[] = [];

		detailRows.forEach((row) => {
			const quantity = getQuantity(row);
			const rate = getRate(row);
			const code = getRowStringValue(row, 'code', columnMapping);

			// Check for negative values
			if (quantity < 0) {
				warnings.push(`${code}: Negative quantity (${quantity})`);
			}
			if (rate < 0) {
				warnings.push(`${code}: Negative rate (${rate})`);
			}
		});

		return warnings;
	});

	// Validation errors
	const validationErrors = $derived.by(() => {
		const errors: string[] = [];

		detailRows.forEach((row) => {
			const code = getRowStringValue(row, 'code', columnMapping);
			const description = getRowStringValue(row, 'description', columnMapping);

			// Check for missing required fields
			if (!code?.trim()) {
				errors.push(`${code}: Missing WBS code`);
			}
			if (!row.finalDescription?.trim() && !description?.trim()) {
				errors.push(`${code}: Missing description`);
			}
		});

		return errors;
	});

	// Check if import should be blocked
	let hasErrors = $derived(validationErrors.length > 0);

	function handleImport() {
		onImport(transformedData);
	}
</script>

<div class="space-y-6">
	<div>
		<h2 class="mb-2 text-xl font-semibold">Step 5: Review Import</h2>
		<p class="text-muted-foreground">
			Review the import summary and resolve any warnings before proceeding.
		</p>
	</div>

	<!-- Statistics Cards -->
	<div class="grid grid-cols-1 gap-4 md:grid-cols-3">
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">Items to Import</CardTitle>
				<Hash class="text-muted-foreground h-4 w-4" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{totalItems}</div>
				<p class="text-muted-foreground text-xs">Budget line items</p>
			</CardContent>
		</Card>

		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">Total Budget</CardTitle>
				<DollarSign class="text-muted-foreground h-4 w-4" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{formatCurrency(totalBudget)}</div>
				<p class="text-muted-foreground text-xs">Sum of quantity × rate × factor</p>
			</CardContent>
		</Card>

		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">WBS Levels</CardTitle>
				<Layers class="text-muted-foreground h-4 w-4" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{wbsLevels.length}</div>
				<p class="text-muted-foreground text-xs">
					{wbsLevels.join(', ')}
				</p>
			</CardContent>
		</Card>
	</div>

	<!-- Warnings -->
	{#if validationWarnings.length > 0 || validationErrors.length > 0}
		<Alert variant={hasErrors ? 'destructive' : 'default'}>
			<AlertTriangle class="h-4 w-4" />
			<AlertDescription>
				<div class="mb-2 font-medium">
					{hasErrors ? 'Errors found - import blocked:' : 'Warnings found:'}
				</div>
				<ul class="max-h-32 list-inside list-disc space-y-1 overflow-y-auto">
					{#each validationErrors as error (error)}
						<li class="text-sm">{error}</li>
					{/each}
					{#each validationWarnings.slice(0, 10) as warning (warning)}
						<li class="text-sm">{warning}</li>
					{/each}
					{#if validationWarnings.length > 10}
						<li class="text-sm font-medium">...and {validationWarnings.length - 10} more</li>
					{/if}
				</ul>
			</AlertDescription>
		</Alert>
	{:else}
		<Alert>
			<CheckCircle class="h-4 w-4" />
			<AlertDescription>
				No issues found. Ready to import {totalItems} budget items.
			</AlertDescription>
		</Alert>
	{/if}

	<!-- Budget Items Table -->
	<Card>
		<CardHeader>
			<CardTitle class="text-lg">Budget Items Preview</CardTitle>
		</CardHeader>
		<CardContent class="p-2">
			<div class="max-h-150 overflow-y-auto">
				<Table class="w-full table-fixed">
					<TableHeader class="bg-background sticky top-0">
						<TableRow>
							<TableHead class="w-24">WBS Code</TableHead>
							<TableHead class="w-48">Description</TableHead>
							<TableHead class="w-20 text-right">Quantity</TableHead>
							<TableHead class="w-16">Unit</TableHead>
							<TableHead class="w-24 text-right">Unit Rate</TableHead>
							<TableHead class="w-24 text-right">Subtotal</TableHead>
							<TableHead class="w-20 text-right">Factor</TableHead>
							<TableHead class="w-20 text-right">Parent Factor</TableHead>
							<TableHead class="w-24 text-right">Total</TableHead>
							<TableHead class="w-16">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{#each importItems as item, index (index)}
							{@const unit_rate = item.unit_rate_manual_override
								? (item.unit_rate ?? 0)
								: item.material_rate + (item.labor_rate ?? 0) / (item.productivity_per_hour ?? 1)}
							{@const subtotal = unit_rate * item.quantity}
							{@const total = subtotal * (item.factor ?? 1)}
							<TableRow>
								<TableCell class="font-mono text-xs">{item.code}</TableCell>
								<TableCell class="w-48 overflow-hidden text-sm overflow-ellipsis"
									>{item.description}</TableCell
								>
								<TableCell class="text-right text-sm tabular-nums">
									{#if item.quantity !== 0}
										{item.quantity.toLocaleString(undefined, {
											minimumFractionDigits: 0,
											maximumFractionDigits: 2,
										})}
									{/if}
								</TableCell>
								<TableCell class="text-sm tabular-nums">{item.unit}</TableCell>
								<TableCell class="text-right text-sm tabular-nums">
									{#if unit_rate}
										{unit_rate?.toLocaleString(undefined, {
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										})}
									{/if}
								</TableCell>
								<TableCell class="text-right text-sm tabular-nums">
									{#if subtotal}
										{subtotal.toLocaleString(undefined, {
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										})}
									{/if}
								</TableCell>
								<TableCell class="text-right text-sm tabular-nums">
									{#if item.factor && item.factor !== 1}
										{(item.factor ?? 1).toLocaleString(undefined, {
											minimumFractionDigits: 1,
											maximumFractionDigits: 2,
										})}
									{/if}
								</TableCell>
								<TableCell class="text-right text-sm tabular-nums">
									{#if item.parentFactor && item.parentFactor !== 1}
										{(item.parentFactor ?? 1).toLocaleString(undefined, {
											minimumFractionDigits: 1,
											maximumFractionDigits: 2,
										})}
									{/if}
								</TableCell>
								<TableCell class="text-right text-sm font-medium tabular-nums">
									{#if total !== 0}
										{total.toLocaleString(undefined, {
											minimumFractionDigits: 0,
											maximumFractionDigits: 0,
										})}
									{/if}
								</TableCell>
								<TableCell class="w-16">
									<DropdownMenu.Root>
										<DropdownMenu.Trigger>
											<Button variant="ghost" size="icon" class="h-8 w-8 p-0">
												<DotsThreeVertical class="h-4 w-4" />
												<span class="sr-only">Open menu</span>
											</Button>
										</DropdownMenu.Trigger>
										<DropdownMenu.Content align="end">
											<DropdownMenu.Item onclick={() => openEditDialog(item, index)}>
												<Pencil class="mr-2 h-4 w-4" />
												Edit
											</DropdownMenu.Item>
											<DropdownMenu.Item onclick={() => openDeleteDialog(item, index)}>
												<Trash class="mr-2 h-4 w-4" />
												Delete
											</DropdownMenu.Item>
										</DropdownMenu.Content>
									</DropdownMenu.Root>
								</TableCell>
							</TableRow>
						{/each}
					</TableBody>

					<!-- Table Footer with Totals -->
					<TableBody class="border-t-2">
						<TableRow class="bg-muted/50 font-medium">
							<TableCell colspan={8} class="text-right">
								<span class="text-sm">Total ({totalItems} items):</span>
							</TableCell>
							<TableCell class="text-right text-sm font-bold"
								>{formatCurrency(totalBudget)}</TableCell
							>
							<TableCell></TableCell>
						</TableRow>
					</TableBody>
				</Table>
			</div>
		</CardContent>
	</Card>

	<div class="flex justify-between">
		<Button variant="outline" onclick={onBack} disabled={isImporting}>Back</Button>
		<Button
			onclick={handleImport}
			disabled={hasErrors || isImporting || totalItems === 0}
			class="min-w-32"
		>
			{#if isImporting}
				Importing...
			{:else}
				Import {totalItems} Items
			{/if}
		</Button>
	</div>
</div>

<!-- Edit Dialog -->
<Dialog.Root bind:open={showEditDialog}>
	<Dialog.Content class="max-w-2xl">
		<Dialog.Header>
			<Dialog.Title>Edit Budget Item</Dialog.Title>
			<Dialog.Description>
				Modify the budget item details below. Press Enter in any field to save changes.
			</Dialog.Description>
		</Dialog.Header>

		<div class="grid grid-cols-2 gap-4 py-4">
			<div class="space-y-2">
				<label for="edit-code" class="text-sm font-medium">WBS Code</label>
				<Input
					id="edit-code"
					bind:value={editFormData.code}
					onkeydown={handleKeydown}
					placeholder="Enter WBS code"
				/>
			</div>

			<div class="space-y-2">
				<label for="edit-unit" class="text-sm font-medium">Unit</label>
				<Input
					id="edit-unit"
					bind:value={editFormData.unit}
					onkeydown={handleKeydown}
					placeholder="Enter unit"
				/>
			</div>

			<div class="col-span-2 space-y-2">
				<label for="edit-description" class="text-sm font-medium">Description</label>
				<Input
					id="edit-description"
					bind:value={editFormData.description}
					onkeydown={handleKeydown}
					placeholder="Enter description"
				/>
			</div>

			<div class="space-y-2">
				<label for="edit-quantity" class="text-sm font-medium">Quantity</label>
				<Input
					id="edit-quantity"
					type="number"
					step="0.01"
					bind:value={editFormData.quantity}
					onkeydown={handleKeydown}
					placeholder="Enter quantity"
				/>
			</div>

			<div class="space-y-2">
				<label for="edit-material-rate" class="text-sm font-medium">Material Rate</label>
				<Input
					id="edit-material-rate"
					type="number"
					step="0.01"
					bind:value={editFormData.material_rate}
					onkeydown={handleKeydown}
					placeholder="Enter material rate"
				/>
			</div>

			<div class="space-y-2">
				<label for="edit-labor-rate" class="text-sm font-medium">Labor Rate</label>
				<Input
					id="edit-labor-rate"
					type="number"
					step="0.01"
					bind:value={editFormData.labor_rate}
					onkeydown={handleKeydown}
					placeholder="Enter labor rate"
				/>
			</div>

			<div class="space-y-2">
				<label for="edit-productivity" class="text-sm font-medium">Productivity per Hour</label>
				<Input
					id="edit-productivity"
					type="number"
					step="0.01"
					bind:value={editFormData.productivity_per_hour}
					onkeydown={handleKeydown}
					placeholder="Enter productivity"
				/>
			</div>

			<div class="space-y-2">
				<label for="edit-factor" class="text-sm font-medium">Factor</label>
				<Input
					id="edit-factor"
					type="number"
					step="0.01"
					bind:value={editFormData.factor}
					onkeydown={handleKeydown}
					placeholder="Enter factor"
				/>
			</div>

			<div class="space-y-2">
				<label for="edit-unit-rate" class="text-sm font-medium">Manual Unit Rate</label>
				<Input
					id="edit-unit-rate"
					type="number"
					step="0.01"
					bind:value={editFormData.unit_rate}
					onkeydown={handleKeydown}
					placeholder="Enter unit rate"
					disabled={!editFormData.unit_rate_manual_override}
				/>
			</div>

			<div class="col-span-2 space-y-2">
				<label class="flex items-center space-x-2">
					<input
						type="checkbox"
						bind:checked={editFormData.unit_rate_manual_override}
						class="rounded"
					/>
					<span class="text-sm font-medium">Use manual unit rate override</span>
				</label>
			</div>

			<div class="col-span-2 space-y-2">
				<label for="edit-remarks" class="text-sm font-medium">Remarks</label>
				<Input
					id="edit-remarks"
					bind:value={editFormData.remarks}
					onkeydown={handleKeydown}
					placeholder="Enter remarks"
				/>
			</div>
		</div>

		<Dialog.Footer>
			<Button variant="outline" onclick={closeEditDialog}>Cancel</Button>
			<Button onclick={saveEdit}>Save Changes</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>

<!-- Delete Confirmation Dialog -->
<Dialog.Root bind:open={showDeleteDialog}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Delete Budget Item</Dialog.Title>
			<Dialog.Description>
				Are you sure you want to delete this budget item? This action cannot be undone.
			</Dialog.Description>
		</Dialog.Header>

		{#if deletingItem}
			<div class="py-4">
				<p class="text-muted-foreground text-sm">
					<strong>Item:</strong>
					{deletingItem.code} - {deletingItem.description}
				</p>
			</div>
		{/if}

		<Dialog.Footer>
			<Button variant="outline" onclick={closeDeleteDialog}>Cancel</Button>
			<Button variant="destructive" onclick={confirmDelete}>Delete</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
