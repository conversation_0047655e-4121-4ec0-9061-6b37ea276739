import { z } from 'zod';

// Risk status options
export const riskStatuses = ['risk', 'pending', 'approved'] as const;

// Schema for project risk items
export const riskItemSchema = z.object({
	risk_id: z.uuid().optional(),
	project_id: z.uuid(),
	title: z.string().min(1, 'Risk title is required'),
	description: z.string().min(1, 'Risk description is required'),
	wbs_library_item_id: z.uuid().nullable().optional(),
	status: z.enum(riskStatuses).default('risk'),
	date_identified: z.string().optional(),
	cause: z.string().nullable().optional(),
	effect: z.string().nullable().optional(),
	program_impact: z.string().nullable().optional(),
	design_fees: z.number().nullable().optional(),
	construction_total: z.number().nullable().optional(),
	mitigation_plan: z.string().nullable().optional(),
	date_for_review: z.string().optional(),
	// Risk owner fields - either a user_id or name/email
	risk_owner_user_id: z.uuid().nullable().optional(),
	risk_owner_name: z.string().nullable().optional(),
	risk_owner_email: z.email('Please enter a valid email').nullable().optional(),
	created_at: z.string().datetime().optional(),
	updated_at: z.string().datetime().optional(),
});

// Schema for risk filtering
export const riskFilterSchema = z.object({
	status: z.enum(['all', ...riskStatuses]).default('all'),
	date_from: z.string().optional(),
	date_to: z.string().optional(),
	wbs_library_item_id: z
		.union([z.uuid(), z.literal('__project_budget_root__'), z.literal('__unallocated_risks__')])
		.optional(),
});

export type RiskItem = z.infer<typeof riskItemSchema>;
export type RiskFilter = z.infer<typeof riskFilterSchema>;
