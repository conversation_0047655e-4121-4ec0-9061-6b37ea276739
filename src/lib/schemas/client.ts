import { z } from 'zod';

export const clientRoles = ['viewer', 'editor', 'admin'] as const;

export const clientSchema = z.object({
	name: z.string().min(1, 'Client name is required'),
	description: z.string().optional().nullable(),
	client_url: z.url('Please enter a valid URL').optional().nullable(),
	internal_url: z.url('Please enter a valid URL').optional().nullable(),
	internal_url_description: z.string().optional().nullable(),
	logo_url: z.url('Please enter a valid URL').optional().nullable(),
	// logo_file: z
	// 	.instanceof(File)
	// 	.refine((f) => f.size < 1024 * 1024, 'Max 1MB file size.')
	// 	.optional()
	// 	.nullable(),
});

export type ClientSchema = typeof clientSchema;

export const clientInviteSchema = z.object({
	client_id: z.string().min(1, 'Client ID is required'),
	email: z.email('Please enter a valid email address').min(1, 'Email is required'),
	role: z
		.enum(clientRoles, {
			error: 'Please select a role',
		})
		.default('viewer'),
});

export type ClientInviteForm = z.infer<typeof clientInviteSchema>;
