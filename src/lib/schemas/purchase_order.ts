import { z } from 'zod';

// Purchase order schema for creation
export const purchaseOrderSchema = z.object({
	po_number: z.string().min(1, 'PO number is required'),
	description: z.string().optional().nullable(),
	po_date: z.iso.date({ error: 'A PO date is required.' }),
	vendor_id: z.uuid('Please select a vendor'),
	work_package_id: z.uuid('Please select a work package'),
	account: z.string().optional().nullable(),
	original_amount: z.number().min(0, 'Original amount must be positive').optional().nullable(),
	co_amount: z.number().optional().nullable(),
	freight: z.number().min(0, 'Freight must be positive').optional().nullable(),
	tax: z.number().min(0, 'Tax must be positive').optional().nullable(),
	other: z.number().min(0, 'Other amount must be positive').optional().nullable(),
	notes: z.string().optional().nullable(),
});

// Purchase order schema for modal creation - includes project_id
export const purchaseOrderModalSchema = z.object({
	po_number: z.string().min(1, 'PO number is required'),
	description: z.string().optional().nullable(),
	po_date: z.iso.date({ error: 'A PO date is required.' }),
	vendor_id: z.uuid('Please select a vendor'),
	work_package_id: z.uuid('Please select a work package'),
	account: z.string().optional().nullable(),
	original_amount: z.number().min(0, 'Original amount must be positive').optional().nullable(),
	co_amount: z.number().optional().nullable(),
	freight: z.number().min(0, 'Freight must be positive').optional().nullable(),
	tax: z.number().min(0, 'Tax must be positive').optional().nullable(),
	other: z.number().min(0, 'Other amount must be positive').optional().nullable(),
	notes: z.string().optional().nullable(),
	project_id: z.uuid('Project ID is required'),
});

// Edit purchase order schema - includes purchase_order_id for updates
export const editPurchaseOrderSchema = z.object({
	purchase_order_id: z.uuid(),
	po_number: z.string().min(1, 'PO number is required'),
	description: z.string().optional().nullable(),
	po_date: z.iso.date({ error: 'A PO date is required.' }),
	vendor_id: z.uuid('Please select a vendor'),
	work_package_id: z.uuid('Please select a work package'),
	account: z.string().optional().nullable(),
	original_amount: z.number().min(0, 'Original amount must be positive').optional().nullable(),
	co_amount: z.number().optional().nullable(),
	freight: z.number().min(0, 'Freight must be positive').optional().nullable(),
	tax: z.number().min(0, 'Tax must be positive').optional().nullable(),
	other: z.number().min(0, 'Other amount must be positive').optional().nullable(),
	notes: z.string().optional().nullable(),
});

// Type definitions
export type PurchaseOrderSchema = z.infer<typeof purchaseOrderSchema>;
export type PurchaseOrderModalSchema = z.infer<typeof purchaseOrderModalSchema>;
export type EditPurchaseOrderSchema = z.infer<typeof editPurchaseOrderSchema>;

// Type for purchase order list items (with joined vendor information)
// This matches the return type from get_accessible_purchase_orders RPC function
export type PurchaseOrderListItem = {
	purchase_order_id: string;
	po_number: string;
	description: string | null;
	po_date: string;
	vendor_name: string;
	vendor_id: string;
	original_amount: number | null;
	co_amount: number | null;
	total_amount: number;
	created_at: string;
};

// Helper function to format currency
export function formatCurrency(
	amount: number | null | undefined,
	maximumFractionDigits = 2,
): string {
	if (amount === null || amount === undefined) return '';
	return new Intl.NumberFormat('en-GB', {
		style: 'currency',
		currency: 'SEK',
		minimumFractionDigits: maximumFractionDigits ?? 2,
		maximumFractionDigits,
	}).format(amount);
}

// Helper function to calculate total amount
export function calculateTotalAmount(
	originalAmount: number | null,
	coAmount: number | null,
	freight: number | null,
	tax: number | null,
	other: number | null,
): number {
	return (originalAmount || 0) + (coAmount || 0) + (freight || 0) + (tax || 0) + (other || 0);
}
