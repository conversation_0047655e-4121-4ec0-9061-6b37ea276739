import { z } from 'zod';

export const inviteRequestSchema = z.discriminatedUnion('resourceType', [
	z.object({
		resourceType: z.literal('organization'),
		resourceId: z.uuid(),
		role: z.string(),
		inviteeEmail: z.email(),
		expiresAt: z.string().refine((v) => !isNaN(Date.parse(v)), { message: 'Invalid datetime' }),
	}),
	z.object({
		resourceType: z.literal('client'),
		resourceId: z.uuid(),
		role: z.string(),
		inviteeEmail: z.email(),
		expiresAt: z.string().refine((v) => !isNaN(Date.parse(v)), { message: 'Invalid datetime' }),
	}),
	z.object({
		resourceType: z.literal('project'),
		resourceId: z.uuid(),
		role: z.string(),
		inviteeEmail: z.email(),
		expiresAt: z.string().refine((v) => !isNaN(Date.parse(v)), { message: 'Invalid datetime' }),
	}),
]);

export const tokenSchema = z.object({
	token: z.uuid({ message: 'Invalid token' }),
});
