import { z } from 'zod';

// Schema for gateway checklist items
export const gatewayChecklistItemSchema = z.object({
	name: z.string().min(1, 'Checklist item name is required'),
	description: z.string().optional().nullable(),
	project_stage_id: z.uuid(),
});

// Schema for gateway checklist items with ID (for updates)
export const gatewayChecklistItemWithIdSchema = gatewayChecklistItemSchema.extend({
	gateway_checklist_item_id: z.uuid(),
});

// Schema for updating checklist item status
export const checklistItemStatusSchema = z.object({
	gateway_checklist_item_id: z.uuid(),
	status: z.enum(['Incomplete', 'Deferred', 'Complete']),
});

export type GatewayChecklistItem = z.infer<typeof gatewayChecklistItemSchema>;
export type GatewayChecklistItemWithId = z.infer<typeof gatewayChecklistItemWithIdSchema>;
