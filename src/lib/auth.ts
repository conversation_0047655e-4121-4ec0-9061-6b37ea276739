import { goto } from '$app/navigation';
import { page } from '$app/state';

export async function signOut() {
	// Clear org cookie by calling the server endpoint
	await fetch('/api/auth/signout', { method: 'POST' });

	// Sign out from Supabase
	const supabase = page.data.supabase;
	const { error } = await supabase.auth.signOut();
	if (error) console.error('Sign out error:', error);

	// Redirect to sign-in page
	goto('/auth/signin');
}
