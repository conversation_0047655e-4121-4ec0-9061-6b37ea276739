import type { Logger } from 'pino';

export function logDatabaseOperation(
	logger: Logger,
	operation: string,
	table: string,
	duration?: number,
	error?: unknown,
) {
	const logData = {
		operation,
		table,
		duration_ms: duration,
	};

	if (error) {
		logger.error({ ...logData, error }, `Database ${operation} failed`);
	} else {
		logger.info(logData, `Database ${operation} completed`);
	}
}

export function logBusinessEvent(logger: Logger, event: string, data?: Record<string, unknown>) {
	logger.info({ event, ...data }, `Business event: ${event}`);
}

export function logPerformanceTiming(
	logger: Logger,
	operation: string,
	duration: number,
	metadata?: Record<string, unknown>,
) {
	logger.info(
		{
			operation,
			duration_ms: duration,
			...metadata,
		},
		`Performance: ${operation}`,
	);
}

export function logUserAction(
	logger: Logger,
	action: string,
	userId?: string,
	metadata?: Record<string, unknown>,
) {
	logger.info(
		{
			action,
			userId,
			...metadata,
		},
		`User action: ${action}`,
	);
}

export function logApiCall(
	logger: Logger,
	endpoint: string,
	method: string,
	duration?: number,
	status?: number,
	error?: unknown,
) {
	const logData = {
		endpoint,
		method,
		duration_ms: duration,
		status,
	};

	if (error) {
		logger.error({ ...logData, error }, `API call failed: ${method} ${endpoint}`);
	} else {
		logger.info(logData, `API call completed: ${method} ${endpoint}`);
	}
}

export function logAuthEvent(
	logger: Logger,
	event: 'login' | 'logout' | 'signup' | 'password_reset' | 'email_verification',
	userId?: string,
	metadata?: Record<string, unknown>,
) {
	logger.info(
		{
			auth_event: event,
			userId,
			...metadata,
		},
		`Auth event: ${event}`,
	);
}

export function logSecurityEvent(
	logger: Logger,
	event: string,
	severity: 'low' | 'medium' | 'high' | 'critical',
	metadata?: Record<string, unknown>,
) {
	logger.warn(
		{
			security_event: event,
			severity,
			...metadata,
		},
		`Security event: ${event}`,
	);
}
