import { PostHog } from 'posthog-node';
import { PUBLIC_POSTHOG_KEY, PUBLIC_POSTHOG_HOST } from '$env/static/public';
import { dev } from '$app/environment';

let _client: PostHog | null = null;

export function getPostHogClient() {
	if (dev) {
		return {
			capture: () => {},
		};
	}
	if (!_client) {
		_client = new PostHog(PUBLIC_POSTHOG_KEY, {
			host: PUBLIC_POSTHOG_HOST,
			flushAt: 1,
			disableGeoip: true,
		});
	}
	return _client;
}
