import { SERVICE_NAME, LOG_LEVEL, VERCEL_ENV, VERCEL_GIT_COMMIT_SHA } from '$env/static/private';
import { dev } from '$app/environment';
import pino from 'pino';

const logLevel = LOG_LEVEL ?? (dev ? 'debug' : 'info');
const serviceName = SERVICE_NAME ?? 'cost-atlas-ssr';

export const logger = pino({
	level: logLevel,
	redact: {
		paths: [
			'req.headers.authorization',
			'req.headers.cookie',
			'user.email',
			'user.token',
			'password',
			'secret',
		],
		remove: true,
	},
	base: {
		service: serviceName,
		env: VERCEL_ENV ?? 'development',
		version: VERCEL_GIT_COMMIT_SHA ?? 'unknown',
	},
	transport: dev
		? {
				target: 'pino-pretty',
				options: {
					colorize: true,
					translateTime: 'SYS:standard',
					ignore: 'pid,hostname',
				},
			}
		: undefined,
	formatters: {
		level: (label) => ({ level: label }),
	},
});
