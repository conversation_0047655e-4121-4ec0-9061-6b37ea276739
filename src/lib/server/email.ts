import { dev } from '$app/environment';
import { getRequestEvent } from '$app/server';
import { RESEND_API_KEY } from '$env/static/private';
import { PUBLIC_FROM_EMAIL_ADDRESS } from '$env/static/public';
import { Resend } from 'resend';

const resend = new Resend(RESEND_API_KEY ?? '');

const FROM = PUBLIC_FROM_EMAIL_ADDRESS;
const REPLY_TO = FROM;

// TODO: add this to a queue so that we can send emails in the background
// and not block the request
export const sendEmail = async ({
	to,
	subject,
	text,
	html,
}: {
	to: string;
	subject: string;
	text?: string;
	html?: string;
}) => {
	const { locals } = getRequestEvent();
	const log = locals.log;

	if (!to) {
		log.error({ msg: 'sendEmail - No recipient provided' });
		throw new Error('No recipient provided');
	}

	if (dev) {
		log.info({ msg: 'sendEmail - Email not sent in dev mode.', to, subject, text, html });
		return { success: true };
	}

	// Send an email
	// https://resend.com/docs/send-email
	if (html) {
		try {
			const { data, error } = await resend.emails.send({
				from: FROM,
				to,
				replyTo: REPLY_TO,
				subject,
				html,
			});
			if (error) {
				log.error({ msg: 'sendEmail - Error sending HTML email', error });
				return { error };
			}
			log.info({ msg: 'sendEmail - HTML email sent successfully', data });
			return { success: true };
		} catch (err) {
			log.error({ msg: 'sendEmail - Exception sending HTML email:', err });
			return { error: err };
		}
	}
	if (text) {
		try {
			const { data, error } = await resend.emails.send({
				from: FROM,
				to,
				replyTo: REPLY_TO,
				subject,
				text,
			});
			if (error) {
				log.error({ msg: 'sendEmail - Error sending text email:', error });
				return { error };
			}
			log.info({ msg: 'sendEmail - Text email sent successfully', data });
			return { success: true };
		} catch (err) {
			log.error({ msg: 'sendEmail - Exception sending text email:', err });
			return { error: err };
		}
	}
	log.error('sendEmail - Neither text nor html provided');
	return { error: new Error('Either text or html must be provided') };
};
