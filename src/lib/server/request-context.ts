import { AsyncLocalStorage } from 'node:async_hooks';

type RequestContext = {
	requestId: string;
	start: number;
	userId?: string;
	orgId?: string;
};

export const als = new AsyncLocalStorage<RequestContext>();

export function getRequestContext(): RequestContext | undefined {
	return als.getStore();
}

export function getRequestId(): string | undefined {
	return als.getStore()?.requestId;
}

export function getUserId(): string | undefined {
	return als.getStore()?.userId;
}

export function getOrgId(): string | undefined {
	return als.getStore()?.orgId;
}
