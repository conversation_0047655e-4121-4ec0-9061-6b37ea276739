/**
 * Examples of how to use the enhanced Pino logging system
 *
 * This file demonstrates the various logging utilities and patterns
 * available in the improved logging implementation.
 */

import type { RequestEvent } from '@sveltejs/kit';
import {
	logDatabaseOperation,
	logBusinessEvent,
	logPerformanceTiming,
	logUserAction,
	logApiCall,
	logAuthEvent,
	logSecurityEvent,
} from './logging-utils';
import { getRequestContext, getRequestId, getUserId } from './request-context';

/**
 * Example: Database operation logging
 */
export async function exampleDatabaseLogging(event: RequestEvent) {
	const start = performance.now();

	try {
		// Simulate database operation
		const { data, error } = await event.locals.supabase.from('project').select('*').limit(10);

		const duration = performance.now() - start;

		if (error) {
			logDatabaseOperation(event.locals.log, 'SELECT', 'projects', duration, error);
			throw error;
		}

		logDatabaseOperation(event.locals.log, 'SELECT', 'projects', duration);
		return data;
	} catch (error) {
		const duration = performance.now() - start;
		logDatabaseOperation(event.locals.log, 'SELECT', 'projects', duration, error);
		throw error;
	}
}

/**
 * Example: Business event logging
 */
export function exampleBusinessEventLogging(event: RequestEvent) {
	// Log project creation
	logBusinessEvent(event.locals.log, 'project_created', {
		projectId: 'proj_123',
		organizationId: event.locals.orgId,
		createdBy: event.locals.user?.id,
		projectType: 'construction',
	});

	// Log budget import
	logBusinessEvent(event.locals.log, 'budget_imported', {
		projectId: 'proj_123',
		fileName: 'budget_2024.xlsx',
		lineCount: 150,
		totalAmount: 1500000,
	});
}

/**
 * Example: Performance timing logging
 */
export async function examplePerformanceLogging(event: RequestEvent) {
	const start = performance.now();

	// Simulate expensive operation
	await new Promise((resolve) => setTimeout(resolve, 100));

	const duration = performance.now() - start;

	logPerformanceTiming(event.locals.log, 'budget_calculation', duration, {
		projectId: 'proj_123',
		lineCount: 150,
		complexity: 'high',
	});
}

/**
 * Example: User action logging
 */
export function exampleUserActionLogging(event: RequestEvent) {
	const userId = event.locals.user?.id;

	logUserAction(event.locals.log, 'project_viewed', userId, {
		projectId: 'proj_123',
		viewType: 'dashboard',
		source: 'navigation',
	});

	logUserAction(event.locals.log, 'budget_exported', userId, {
		projectId: 'proj_123',
		format: 'excel',
		includeActuals: true,
	});
}

/**
 * Example: API call logging
 */
export async function exampleApiCallLogging(event: RequestEvent) {
	const start = performance.now();

	try {
		// Simulate external API call
		const response = await fetch('https://api.example.com/data');
		const duration = performance.now() - start;

		logApiCall(event.locals.log, '/data', 'GET', duration, response.status);

		return await response.json();
	} catch (error) {
		const duration = performance.now() - start;
		logApiCall(event.locals.log, '/data', 'GET', duration, undefined, error);
		throw error;
	}
}

/**
 * Example: Authentication event logging
 */
export function exampleAuthEventLogging(event: RequestEvent) {
	const userId = event.locals.user?.id;

	// Log successful login
	logAuthEvent(event.locals.log, 'login', userId, {
		method: 'email',
		ipAddress: event.getClientAddress(),
		userAgent: event.request.headers.get('user-agent'),
	});

	// Log password reset request
	logAuthEvent(event.locals.log, 'password_reset', undefined, {
		email: '<EMAIL>',
		ipAddress: event.getClientAddress(),
	});
}

/**
 * Example: Security event logging
 */
export function exampleSecurityEventLogging(event: RequestEvent) {
	// Log suspicious activity
	logSecurityEvent(event.locals.log, 'multiple_failed_logins', 'medium', {
		email: '<EMAIL>',
		attemptCount: 5,
		ipAddress: event.getClientAddress(),
		timeWindow: '5 minutes',
	});

	// Log unauthorized access attempt
	logSecurityEvent(event.locals.log, 'unauthorized_access_attempt', 'high', {
		userId: event.locals.user?.id,
		attemptedResource: '/admin/users',
		userRole: 'member',
		requiredRole: 'admin',
	});
}

/**
 * Example: Using request context helpers
 */
export function exampleRequestContextUsage(event: RequestEvent) {
	// Get current request context
	const context = getRequestContext();
	event.locals.log.info({ context }, 'Current request context');

	// Get specific context values
	const requestId = getRequestId();
	const userId = getUserId();

	event.locals.log.info({
		requestId,
		userId,
		message: 'Processing user request',
	});
}

/**
 * Example: Error logging with context
 */
export function exampleErrorLogging(event: RequestEvent, error: unknown) {
	const context = getRequestContext();

	event.locals.log.error(
		{
			error,
			context,
			userId: event.locals.user?.id,
			orgId: event.locals.orgId,
			path: event.url.pathname,
			method: event.request.method,
		},
		'Unhandled error occurred',
	);
}

/**
 * Example: Structured logging for debugging
 */
export function exampleDebugLogging(event: RequestEvent) {
	// Debug logging with structured data
	event.locals.log.debug(
		{
			function: 'calculateBudgetTotals',
			projectId: 'proj_123',
			inputData: {
				lineItems: 150,
				categories: 12,
				phases: 4,
			},
			processingStep: 'validation',
		},
		'Starting budget calculation',
	);

	// Trace level logging for detailed debugging
	event.locals.log.trace(
		{
			sql: 'SELECT * FROM budget_lines WHERE project_id = $1',
			params: ['proj_123'],
			executionTime: 45.2,
		},
		'Database query executed',
	);
}
