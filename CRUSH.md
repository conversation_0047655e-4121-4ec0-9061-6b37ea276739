# Project Controls - CRUSH Commands & Guidelines

## Commands

- **Dev**: `pnpm dev` (add `-- --open` to open browser)
- **Build**: `pnpm build`
- **Lint/Type Check**: `pnpm check`
- **Format**: `pnpm format`
- **Unit Tests**: `pnpm test:unit` (single: `pnpm test:unit -- path/to/test.spec.ts`)
- **E2E Tests**: `pnpm test:e2e` (single: `pnpm test:e2e -- path/to/test.test.ts`)
- **Storybook**: `pnpm storybook`
- **DB Reset**: `pnpm reset-db`

## Code Style

- **TypeScript**: Strict typing, NO `any` types
- **Formatting**: Prettier (100 char width, single quotes, tabs)
- **Naming**: PascalCase (components), camelCase (vars/functions), snake_case (DB)
- **Svelte 5**: Use `$props()`, `$derived`, `$effect` runes (not `$:`)
- **Forms**: shadcn-svelte + sveltekit-superforms + zod validation
- **Imports**: Group external/internal, alphabetize
- **CRUD**: Separate routes for READ/CREATE/UPDATE operations
- **Environment**: Use `$env/static/public` not `import.meta.env`
- **Superforms**: Use `superValidate(request, zod(schema))` not `superValidate(await request.formData(), zod(schema))`

## Architecture

- SvelteKit (frontend/backend), PostgreSQL/Supabase, TailwindCSS 4.0
- Authentication via Supabase, shadcn-svelte UI components, Phosphor icons
- Follow SQL/RLS style guides in `cline/` directory
- Test with Vitest (unit) and Playwright (e2e)
